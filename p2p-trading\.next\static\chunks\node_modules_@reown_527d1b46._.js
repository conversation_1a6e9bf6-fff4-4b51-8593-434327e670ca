(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/@reown/appkit/node_modules/@walletconnect/sign-client/dist/index.es.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "AUTH_CONTEXT": (()=>wt),
    "AUTH_KEYS_CONTEXT": (()=>mt),
    "AUTH_PAIRING_TOPIC_CONTEXT": (()=>_t),
    "AUTH_PROTOCOL": (()=>yt),
    "AUTH_PUBLIC_KEY_NAME": (()=>ce),
    "AUTH_REQUEST_CONTEXT": (()=>Et),
    "AUTH_STORAGE_PREFIX": (()=>ae),
    "AUTH_VERSION": (()=>Rs),
    "ENGINE_CONTEXT": (()=>dt),
    "ENGINE_QUEUE_STATES": (()=>$),
    "ENGINE_RPC_OPTS": (()=>N),
    "HISTORY_CONTEXT": (()=>Es),
    "HISTORY_EVENTS": (()=>_s),
    "HISTORY_STORAGE_VERSION": (()=>fs),
    "METHODS_TO_VERIFY": (()=>gt),
    "PROPOSAL_CONTEXT": (()=>pt),
    "PROPOSAL_EXPIRY": (()=>Ss),
    "PROPOSAL_EXPIRY_MESSAGE": (()=>$e),
    "REQUEST_CONTEXT": (()=>ut),
    "SESSION_CONTEXT": (()=>ht),
    "SESSION_EXPIRY": (()=>J),
    "SESSION_REQUEST_EXPIRY_BOUNDARIES": (()=>_e),
    "SIGN_CLIENT_CONTEXT": (()=>ke),
    "SIGN_CLIENT_DEFAULT": (()=>me),
    "SIGN_CLIENT_EVENTS": (()=>ws),
    "SIGN_CLIENT_PROTOCOL": (()=>De),
    "SIGN_CLIENT_STORAGE_OPTIONS": (()=>ms),
    "SIGN_CLIENT_STORAGE_PREFIX": (()=>we),
    "SIGN_CLIENT_VERSION": (()=>Le),
    "SessionStore": (()=>$s),
    "SignClient": (()=>Ks),
    "TVF_METHODS": (()=>Ke),
    "WALLETCONNECT_DEEPLINK_CHOICE": (()=>Me),
    "default": (()=>Ee)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit/node_modules/@walletconnect/core/dist/index.es.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$logger$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@walletconnect/logger/dist/index.es.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$pino$2f$browser$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__pino$3e$__ = __turbopack_context__.i("[project]/node_modules/pino/browser.js [app-client] (ecmascript) <export default as pino>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$logger$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@walletconnect/logger/dist/index.es.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$types$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit/node_modules/@walletconnect/types/dist/index.es.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$time$2f$dist$2f$cjs$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@walletconnect/time/dist/cjs/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit/node_modules/@walletconnect/utils/dist/index.es.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$events$2f$events$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/events/events.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@walletconnect/jsonrpc-utils/dist/esm/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$validators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@walletconnect/jsonrpc-utils/dist/esm/validators.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@walletconnect/jsonrpc-utils/dist/esm/format.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
const De = "wc", Le = 2, ke = "client", we = `${De}@${Le}:${ke}:`, me = {
    name: ke,
    logger: "error",
    controller: !1,
    relayUrl: "wss://relay.walletconnect.org"
}, ws = {
    session_proposal: "session_proposal",
    session_update: "session_update",
    session_extend: "session_extend",
    session_ping: "session_ping",
    session_delete: "session_delete",
    session_expire: "session_expire",
    session_request: "session_request",
    session_request_sent: "session_request_sent",
    session_event: "session_event",
    proposal_expire: "proposal_expire",
    session_authenticate: "session_authenticate",
    session_request_expire: "session_request_expire",
    session_connect: "session_connect"
}, ms = {
    database: ":memory:"
}, Me = "WALLETCONNECT_DEEPLINK_CHOICE", _s = {
    created: "history_created",
    updated: "history_updated",
    deleted: "history_deleted",
    sync: "history_sync"
}, Es = "history", fs = "0.3", pt = "proposal", Ss = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$time$2f$dist$2f$cjs$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["THIRTY_DAYS"], $e = "Proposal expired", ht = "session", J = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$time$2f$dist$2f$cjs$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SEVEN_DAYS"], dt = "engine", N = {
    wc_sessionPropose: {
        req: {
            ttl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$time$2f$dist$2f$cjs$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FIVE_MINUTES"],
            prompt: !0,
            tag: 1100
        },
        res: {
            ttl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$time$2f$dist$2f$cjs$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FIVE_MINUTES"],
            prompt: !1,
            tag: 1101
        },
        reject: {
            ttl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$time$2f$dist$2f$cjs$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FIVE_MINUTES"],
            prompt: !1,
            tag: 1120
        },
        autoReject: {
            ttl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$time$2f$dist$2f$cjs$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FIVE_MINUTES"],
            prompt: !1,
            tag: 1121
        }
    },
    wc_sessionSettle: {
        req: {
            ttl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$time$2f$dist$2f$cjs$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FIVE_MINUTES"],
            prompt: !1,
            tag: 1102
        },
        res: {
            ttl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$time$2f$dist$2f$cjs$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FIVE_MINUTES"],
            prompt: !1,
            tag: 1103
        }
    },
    wc_sessionUpdate: {
        req: {
            ttl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$time$2f$dist$2f$cjs$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ONE_DAY"],
            prompt: !1,
            tag: 1104
        },
        res: {
            ttl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$time$2f$dist$2f$cjs$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ONE_DAY"],
            prompt: !1,
            tag: 1105
        }
    },
    wc_sessionExtend: {
        req: {
            ttl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$time$2f$dist$2f$cjs$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ONE_DAY"],
            prompt: !1,
            tag: 1106
        },
        res: {
            ttl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$time$2f$dist$2f$cjs$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ONE_DAY"],
            prompt: !1,
            tag: 1107
        }
    },
    wc_sessionRequest: {
        req: {
            ttl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$time$2f$dist$2f$cjs$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FIVE_MINUTES"],
            prompt: !0,
            tag: 1108
        },
        res: {
            ttl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$time$2f$dist$2f$cjs$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FIVE_MINUTES"],
            prompt: !1,
            tag: 1109
        }
    },
    wc_sessionEvent: {
        req: {
            ttl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$time$2f$dist$2f$cjs$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FIVE_MINUTES"],
            prompt: !0,
            tag: 1110
        },
        res: {
            ttl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$time$2f$dist$2f$cjs$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FIVE_MINUTES"],
            prompt: !1,
            tag: 1111
        }
    },
    wc_sessionDelete: {
        req: {
            ttl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$time$2f$dist$2f$cjs$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ONE_DAY"],
            prompt: !1,
            tag: 1112
        },
        res: {
            ttl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$time$2f$dist$2f$cjs$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ONE_DAY"],
            prompt: !1,
            tag: 1113
        }
    },
    wc_sessionPing: {
        req: {
            ttl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$time$2f$dist$2f$cjs$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ONE_DAY"],
            prompt: !1,
            tag: 1114
        },
        res: {
            ttl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$time$2f$dist$2f$cjs$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ONE_DAY"],
            prompt: !1,
            tag: 1115
        }
    },
    wc_sessionAuthenticate: {
        req: {
            ttl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$time$2f$dist$2f$cjs$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ONE_HOUR"],
            prompt: !0,
            tag: 1116
        },
        res: {
            ttl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$time$2f$dist$2f$cjs$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ONE_HOUR"],
            prompt: !1,
            tag: 1117
        },
        reject: {
            ttl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$time$2f$dist$2f$cjs$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FIVE_MINUTES"],
            prompt: !1,
            tag: 1118
        },
        autoReject: {
            ttl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$time$2f$dist$2f$cjs$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FIVE_MINUTES"],
            prompt: !1,
            tag: 1119
        }
    }
}, _e = {
    min: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$time$2f$dist$2f$cjs$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FIVE_MINUTES"],
    max: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$time$2f$dist$2f$cjs$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SEVEN_DAYS"]
}, $ = {
    idle: "IDLE",
    active: "ACTIVE"
}, Ke = {
    eth_sendTransaction: {
        key: ""
    },
    eth_sendRawTransaction: {
        key: ""
    },
    wallet_sendCalls: {
        key: ""
    },
    solana_signTransaction: {
        key: "signature"
    },
    solana_signAllTransactions: {
        key: "transactions"
    },
    solana_signAndSendTransaction: {
        key: "signature"
    }
}, ut = "request", gt = [
    "wc_sessionPropose",
    "wc_sessionRequest",
    "wc_authRequest",
    "wc_sessionAuthenticate"
], yt = "wc", Rs = 1.5, wt = "auth", mt = "authKeys", _t = "pairingTopics", Et = "requests", ae = `${yt}@${1.5}:${wt}:`, ce = `${ae}:PUB_KEY`;
var vs = Object.defineProperty, Is = Object.defineProperties, Ts = Object.getOwnPropertyDescriptors, ft = Object.getOwnPropertySymbols, qs = Object.prototype.hasOwnProperty, Ps = Object.prototype.propertyIsEnumerable, Ue = (S, n, e)=>n in S ? vs(S, n, {
        enumerable: !0,
        configurable: !0,
        writable: !0,
        value: e
    }) : S[n] = e, v = (S, n)=>{
    for(var e in n || (n = {}))qs.call(n, e) && Ue(S, e, n[e]);
    if (ft) for (var e of ft(n))Ps.call(n, e) && Ue(S, e, n[e]);
    return S;
}, b = (S, n)=>Is(S, Ts(n)), c = (S, n, e)=>Ue(S, typeof n != "symbol" ? n + "" : n, e);
class Ns extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$types$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["IEngine"] {
    constructor(n){
        super(n), c(this, "name", dt), c(this, "events", new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$events$2f$events$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]), c(this, "initialized", !1), c(this, "requestQueue", {
            state: $.idle,
            queue: []
        }), c(this, "sessionRequestQueue", {
            state: $.idle,
            queue: []
        }), c(this, "requestQueueDelay", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$time$2f$dist$2f$cjs$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ONE_SECOND"]), c(this, "expectedPairingMethodMap", new Map), c(this, "recentlyDeletedMap", new Map), c(this, "recentlyDeletedLimit", 200), c(this, "relayMessageCache", []), c(this, "pendingSessions", new Map), c(this, "init", async ()=>{
            this.initialized || (await this.cleanup(), this.registerRelayerEvents(), this.registerExpirerEvents(), this.registerPairingEvents(), await this.registerLinkModeListeners(), this.client.core.pairing.register({
                methods: Object.keys(N)
            }), this.initialized = !0, setTimeout(async ()=>{
                await this.processPendingMessageEvents(), this.sessionRequestQueue.queue = this.getPendingSessionRequests(), this.processSessionRequestQueue();
            }, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$time$2f$dist$2f$cjs$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toMiliseconds"])(this.requestQueueDelay)));
        }), c(this, "connect", async (e)=>{
            this.isInitialized(), await this.confirmOnlineStateOrThrow();
            const t = b(v({}, e), {
                requiredNamespaces: e.requiredNamespaces || {},
                optionalNamespaces: e.optionalNamespaces || {}
            });
            await this.isValidConnect(t), t.optionalNamespaces = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mergeRequiredAndOptionalNamespaces"])(t.requiredNamespaces, t.optionalNamespaces), t.requiredNamespaces = {};
            const { pairingTopic: s, requiredNamespaces: i, optionalNamespaces: r, sessionProperties: o, scopedProperties: a, relays: l } = t;
            let p = s, h, u = !1;
            try {
                if (p) {
                    const T = this.client.core.pairing.pairings.get(p);
                    this.client.logger.warn("connect() with existing pairing topic is deprecated and will be removed in the next major release."), u = T.active;
                }
            } catch (T) {
                throw this.client.logger.error(`connect() -> pairing.get(${p}) failed`), T;
            }
            if (!p || !u) {
                const { topic: T, uri: K } = await this.client.core.pairing.create();
                p = T, h = K;
            }
            if (!p) {
                const { message: T } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("NO_MATCHING_KEY", `connect() pairing topic: ${p}`);
                throw new Error(T);
            }
            const d = await this.client.core.crypto.generateKeyPair(), w = N.wc_sessionPropose.req.ttl || __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$time$2f$dist$2f$cjs$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FIVE_MINUTES"], m = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["calcExpiry"])(w), f = b(v(v({
                requiredNamespaces: i,
                optionalNamespaces: r,
                relays: l ?? [
                    {
                        protocol: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RELAYER_DEFAULT_PROTOCOL"]
                    }
                ],
                proposer: {
                    publicKey: d,
                    metadata: this.client.metadata
                },
                expiryTimestamp: m,
                pairingTopic: p
            }, o && {
                sessionProperties: o
            }), a && {
                scopedProperties: a
            }), {
                id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["payloadId"])()
            }), _ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["engineEvent"])("session_connect", f.id), { reject: g, resolve: A, done: D } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createDelayedPromise"])(w, $e), I = ({ id: T })=>{
                T === f.id && (this.client.events.off("proposal_expire", I), this.pendingSessions.delete(f.id), this.events.emit(_, {
                    error: {
                        message: $e,
                        code: 0
                    }
                }));
            };
            return this.client.events.on("proposal_expire", I), this.events.once(_, ({ error: T, session: K })=>{
                this.client.events.off("proposal_expire", I), T ? g(T) : K && A(K);
            }), await this.sendRequest({
                topic: p,
                method: "wc_sessionPropose",
                params: f,
                throwOnFailedPublish: !0,
                clientRpcId: f.id
            }), await this.setProposal(f.id, f), {
                uri: h,
                approval: D
            };
        }), c(this, "pair", async (e)=>{
            this.isInitialized(), await this.confirmOnlineStateOrThrow();
            try {
                return await this.client.core.pairing.pair(e);
            } catch (t) {
                throw this.client.logger.error("pair() failed"), t;
            }
        }), c(this, "approve", async (e)=>{
            var t, s, i;
            const r = this.client.core.eventClient.createEvent({
                properties: {
                    topic: (t = e?.id) == null ? void 0 : t.toString(),
                    trace: [
                        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_CLIENT_SESSION_TRACES"].session_approve_started
                    ]
                }
            });
            try {
                this.isInitialized(), await this.confirmOnlineStateOrThrow();
            } catch (q) {
                throw r.setError(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_CLIENT_SESSION_ERRORS"].no_internet_connection), q;
            }
            try {
                await this.isValidProposalId(e?.id);
            } catch (q) {
                throw this.client.logger.error(`approve() -> proposal.get(${e?.id}) failed`), r.setError(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_CLIENT_SESSION_ERRORS"].proposal_not_found), q;
            }
            try {
                await this.isValidApprove(e);
            } catch (q) {
                throw this.client.logger.error("approve() -> isValidApprove() failed"), r.setError(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_CLIENT_SESSION_ERRORS"].session_approve_namespace_validation_failure), q;
            }
            const { id: o, relayProtocol: a, namespaces: l, sessionProperties: p, scopedProperties: h, sessionConfig: u } = e, d = this.client.proposal.get(o);
            this.client.core.eventClient.deleteEvent({
                eventId: r.eventId
            });
            const { pairingTopic: w, proposer: m, requiredNamespaces: f, optionalNamespaces: _ } = d;
            let g = (s = this.client.core.eventClient) == null ? void 0 : s.getEvent({
                topic: w
            });
            g || (g = (i = this.client.core.eventClient) == null ? void 0 : i.createEvent({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_CLIENT_SESSION_TRACES"].session_approve_started,
                properties: {
                    topic: w,
                    trace: [
                        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_CLIENT_SESSION_TRACES"].session_approve_started,
                        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_CLIENT_SESSION_TRACES"].session_namespaces_validation_success
                    ]
                }
            }));
            const A = await this.client.core.crypto.generateKeyPair(), D = m.publicKey, I = await this.client.core.crypto.generateSharedKey(A, D), T = v(v(v({
                relay: {
                    protocol: a ?? "irn"
                },
                namespaces: l,
                controller: {
                    publicKey: A,
                    metadata: this.client.metadata
                },
                expiry: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["calcExpiry"])(J)
            }, p && {
                sessionProperties: p
            }), h && {
                scopedProperties: h
            }), u && {
                sessionConfig: u
            }), K = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TRANSPORT_TYPES"].relay;
            g.addTrace(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_CLIENT_SESSION_TRACES"].subscribing_session_topic);
            try {
                await this.client.core.relayer.subscribe(I, {
                    transportType: K
                });
            } catch (q) {
                throw g.setError(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_CLIENT_SESSION_ERRORS"].subscribe_session_topic_failure), q;
            }
            g.addTrace(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_CLIENT_SESSION_TRACES"].subscribe_session_topic_success);
            const fe = b(v({}, T), {
                topic: I,
                requiredNamespaces: f,
                optionalNamespaces: _,
                pairingTopic: w,
                acknowledged: !1,
                self: T.controller,
                peer: {
                    publicKey: m.publicKey,
                    metadata: m.metadata
                },
                controller: A,
                transportType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TRANSPORT_TYPES"].relay
            });
            await this.client.session.set(I, fe), g.addTrace(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_CLIENT_SESSION_TRACES"].store_session);
            try {
                g.addTrace(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_CLIENT_SESSION_TRACES"].publishing_session_settle), await this.sendRequest({
                    topic: I,
                    method: "wc_sessionSettle",
                    params: T,
                    throwOnFailedPublish: !0
                }).catch((q)=>{
                    throw g?.setError(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_CLIENT_SESSION_ERRORS"].session_settle_publish_failure), q;
                }), g.addTrace(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_CLIENT_SESSION_TRACES"].session_settle_publish_success), g.addTrace(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_CLIENT_SESSION_TRACES"].publishing_session_approve), await this.sendResult({
                    id: o,
                    topic: w,
                    result: {
                        relay: {
                            protocol: a ?? "irn"
                        },
                        responderPublicKey: A
                    },
                    throwOnFailedPublish: !0
                }).catch((q)=>{
                    throw g?.setError(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_CLIENT_SESSION_ERRORS"].session_approve_publish_failure), q;
                }), g.addTrace(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_CLIENT_SESSION_TRACES"].session_approve_publish_success);
            } catch (q) {
                throw this.client.logger.error(q), this.client.session.delete(I, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getSdkError"])("USER_DISCONNECTED")), await this.client.core.relayer.unsubscribe(I), q;
            }
            return this.client.core.eventClient.deleteEvent({
                eventId: g.eventId
            }), await this.client.core.pairing.updateMetadata({
                topic: w,
                metadata: m.metadata
            }), await this.client.proposal.delete(o, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getSdkError"])("USER_DISCONNECTED")), await this.client.core.pairing.activate({
                topic: w
            }), await this.setExpiry(I, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["calcExpiry"])(J)), {
                topic: I,
                acknowledged: ()=>Promise.resolve(this.client.session.get(I))
            };
        }), c(this, "reject", async (e)=>{
            this.isInitialized(), await this.confirmOnlineStateOrThrow();
            try {
                await this.isValidReject(e);
            } catch (r) {
                throw this.client.logger.error("reject() -> isValidReject() failed"), r;
            }
            const { id: t, reason: s } = e;
            let i;
            try {
                i = this.client.proposal.get(t).pairingTopic;
            } catch (r) {
                throw this.client.logger.error(`reject() -> proposal.get(${t}) failed`), r;
            }
            i && (await this.sendError({
                id: t,
                topic: i,
                error: s,
                rpcOpts: N.wc_sessionPropose.reject
            }), await this.client.proposal.delete(t, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getSdkError"])("USER_DISCONNECTED")));
        }), c(this, "update", async (e)=>{
            this.isInitialized(), await this.confirmOnlineStateOrThrow();
            try {
                await this.isValidUpdate(e);
            } catch (h) {
                throw this.client.logger.error("update() -> isValidUpdate() failed"), h;
            }
            const { topic: t, namespaces: s } = e, { done: i, resolve: r, reject: o } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createDelayedPromise"])(), a = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["payloadId"])(), l = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getBigIntRpcId"])().toString(), p = this.client.session.get(t).namespaces;
            return this.events.once((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["engineEvent"])("session_update", a), ({ error: h })=>{
                h ? o(h) : r();
            }), await this.client.session.update(t, {
                namespaces: s
            }), await this.sendRequest({
                topic: t,
                method: "wc_sessionUpdate",
                params: {
                    namespaces: s
                },
                throwOnFailedPublish: !0,
                clientRpcId: a,
                relayRpcId: l
            }).catch((h)=>{
                this.client.logger.error(h), this.client.session.update(t, {
                    namespaces: p
                }), o(h);
            }), {
                acknowledged: i
            };
        }), c(this, "extend", async (e)=>{
            this.isInitialized(), await this.confirmOnlineStateOrThrow();
            try {
                await this.isValidExtend(e);
            } catch (a) {
                throw this.client.logger.error("extend() -> isValidExtend() failed"), a;
            }
            const { topic: t } = e, s = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["payloadId"])(), { done: i, resolve: r, reject: o } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createDelayedPromise"])();
            return this.events.once((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["engineEvent"])("session_extend", s), ({ error: a })=>{
                a ? o(a) : r();
            }), await this.setExpiry(t, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["calcExpiry"])(J)), this.sendRequest({
                topic: t,
                method: "wc_sessionExtend",
                params: {},
                clientRpcId: s,
                throwOnFailedPublish: !0
            }).catch((a)=>{
                o(a);
            }), {
                acknowledged: i
            };
        }), c(this, "request", async (e)=>{
            this.isInitialized();
            try {
                await this.isValidRequest(e);
            } catch (_) {
                throw this.client.logger.error("request() -> isValidRequest() failed"), _;
            }
            const { chainId: t, request: s, topic: i, expiry: r = N.wc_sessionRequest.req.ttl } = e, o = this.client.session.get(i);
            o?.transportType === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TRANSPORT_TYPES"].relay && await this.confirmOnlineStateOrThrow();
            const a = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["payloadId"])(), l = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getBigIntRpcId"])().toString(), { done: p, resolve: h, reject: u } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createDelayedPromise"])(r, "Request expired. Please try again.");
            this.events.once((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["engineEvent"])("session_request", a), ({ error: _, result: g })=>{
                _ ? u(_) : h(g);
            });
            const d = "wc_sessionRequest", w = this.getAppLinkIfEnabled(o.peer.metadata, o.transportType);
            if (w) return await this.sendRequest({
                clientRpcId: a,
                relayRpcId: l,
                topic: i,
                method: d,
                params: {
                    request: b(v({}, s), {
                        expiryTimestamp: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["calcExpiry"])(r)
                    }),
                    chainId: t
                },
                expiry: r,
                throwOnFailedPublish: !0,
                appLink: w
            }).catch((_)=>u(_)), this.client.events.emit("session_request_sent", {
                topic: i,
                request: s,
                chainId: t,
                id: a
            }), await p();
            const m = {
                request: b(v({}, s), {
                    expiryTimestamp: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["calcExpiry"])(r)
                }),
                chainId: t
            }, f = this.shouldSetTVF(d, m);
            return await Promise.all([
                new Promise(async (_)=>{
                    await this.sendRequest(v({
                        clientRpcId: a,
                        relayRpcId: l,
                        topic: i,
                        method: d,
                        params: m,
                        expiry: r,
                        throwOnFailedPublish: !0
                    }, f && {
                        tvf: this.getTVFParams(a, m)
                    })).catch((g)=>u(g)), this.client.events.emit("session_request_sent", {
                        topic: i,
                        request: s,
                        chainId: t,
                        id: a
                    }), _();
                }),
                new Promise(async (_)=>{
                    var g;
                    if (!((g = o.sessionConfig) != null && g.disableDeepLink)) {
                        const A = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getDeepLink"])(this.client.core.storage, Me);
                        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["handleDeeplinkRedirect"])({
                            id: a,
                            topic: i,
                            wcDeepLink: A
                        });
                    }
                    _();
                }),
                p()
            ]).then((_)=>_[2]);
        }), c(this, "respond", async (e)=>{
            this.isInitialized(), await this.isValidRespond(e);
            const { topic: t, response: s } = e, { id: i } = s, r = this.client.session.get(t);
            r.transportType === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TRANSPORT_TYPES"].relay && await this.confirmOnlineStateOrThrow();
            const o = this.getAppLinkIfEnabled(r.peer.metadata, r.transportType);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$validators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isJsonRpcResult"])(s) ? await this.sendResult({
                id: i,
                topic: t,
                result: s.result,
                throwOnFailedPublish: !0,
                appLink: o
            }) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$validators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isJsonRpcError"])(s) && await this.sendError({
                id: i,
                topic: t,
                error: s.error,
                appLink: o
            }), this.cleanupAfterResponse(e);
        }), c(this, "ping", async (e)=>{
            this.isInitialized(), await this.confirmOnlineStateOrThrow();
            try {
                await this.isValidPing(e);
            } catch (s) {
                throw this.client.logger.error("ping() -> isValidPing() failed"), s;
            }
            const { topic: t } = e;
            if (this.client.session.keys.includes(t)) {
                const s = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["payloadId"])(), i = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getBigIntRpcId"])().toString(), { done: r, resolve: o, reject: a } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createDelayedPromise"])();
                this.events.once((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["engineEvent"])("session_ping", s), ({ error: l })=>{
                    l ? a(l) : o();
                }), await Promise.all([
                    this.sendRequest({
                        topic: t,
                        method: "wc_sessionPing",
                        params: {},
                        throwOnFailedPublish: !0,
                        clientRpcId: s,
                        relayRpcId: i
                    }),
                    r()
                ]);
            } else this.client.core.pairing.pairings.keys.includes(t) && (this.client.logger.warn("ping() on pairing topic is deprecated and will be removed in the next major release."), await this.client.core.pairing.ping({
                topic: t
            }));
        }), c(this, "emit", async (e)=>{
            this.isInitialized(), await this.confirmOnlineStateOrThrow(), await this.isValidEmit(e);
            const { topic: t, event: s, chainId: i } = e, r = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getBigIntRpcId"])().toString(), o = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["payloadId"])();
            await this.sendRequest({
                topic: t,
                method: "wc_sessionEvent",
                params: {
                    event: s,
                    chainId: i
                },
                throwOnFailedPublish: !0,
                relayRpcId: r,
                clientRpcId: o
            });
        }), c(this, "disconnect", async (e)=>{
            this.isInitialized(), await this.confirmOnlineStateOrThrow(), await this.isValidDisconnect(e);
            const { topic: t } = e;
            if (this.client.session.keys.includes(t)) await this.sendRequest({
                topic: t,
                method: "wc_sessionDelete",
                params: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getSdkError"])("USER_DISCONNECTED"),
                throwOnFailedPublish: !0
            }), await this.deleteSession({
                topic: t,
                emitEvent: !1
            });
            else if (this.client.core.pairing.pairings.keys.includes(t)) await this.client.core.pairing.disconnect({
                topic: t
            });
            else {
                const { message: s } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("MISMATCHED_TOPIC", `Session or pairing topic not found: ${t}`);
                throw new Error(s);
            }
        }), c(this, "find", (e)=>(this.isInitialized(), this.client.session.getAll().filter((t)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isSessionCompatible"])(t, e)))), c(this, "getPendingSessionRequests", ()=>this.client.pendingRequest.getAll()), c(this, "authenticate", async (e, t)=>{
            var s;
            this.isInitialized(), this.isValidAuthenticate(e);
            const i = t && this.client.core.linkModeSupportedApps.includes(t) && ((s = this.client.metadata.redirect) == null ? void 0 : s.linkMode), r = i ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TRANSPORT_TYPES"].link_mode : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TRANSPORT_TYPES"].relay;
            r === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TRANSPORT_TYPES"].relay && await this.confirmOnlineStateOrThrow();
            const { chains: o, statement: a = "", uri: l, domain: p, nonce: h, type: u, exp: d, nbf: w, methods: m = [], expiry: f } = e, _ = [
                ...e.resources || []
            ], { topic: g, uri: A } = await this.client.core.pairing.create({
                methods: [
                    "wc_sessionAuthenticate"
                ],
                transportType: r
            });
            this.client.logger.info({
                message: "Generated new pairing",
                pairing: {
                    topic: g,
                    uri: A
                }
            });
            const D = await this.client.core.crypto.generateKeyPair(), I = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["hashKey"])(D);
            if (await Promise.all([
                this.client.auth.authKeys.set(ce, {
                    responseTopic: I,
                    publicKey: D
                }),
                this.client.auth.pairingTopics.set(I, {
                    topic: I,
                    pairingTopic: g
                })
            ]), await this.client.core.relayer.subscribe(I, {
                transportType: r
            }), this.client.logger.info(`sending request to new pairing topic: ${g}`), m.length > 0) {
                const { namespace: x } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseChainId"])(o[0]);
                let L = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createEncodedRecap"])(x, "request", m);
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getRecapFromResources"])(_) && (L = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mergeEncodedRecaps"])(L, _.pop())), _.push(L);
            }
            const T = f && f > N.wc_sessionAuthenticate.req.ttl ? f : N.wc_sessionAuthenticate.req.ttl, K = {
                authPayload: {
                    type: u ?? "caip122",
                    chains: o,
                    statement: a,
                    aud: l,
                    domain: p,
                    version: "1",
                    nonce: h,
                    iat: new Date().toISOString(),
                    exp: d,
                    nbf: w,
                    resources: _
                },
                requester: {
                    publicKey: D,
                    metadata: this.client.metadata
                },
                expiryTimestamp: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["calcExpiry"])(T)
            }, fe = {
                eip155: {
                    chains: o,
                    methods: [
                        ...new Set([
                            "personal_sign",
                            ...m
                        ])
                    ],
                    events: [
                        "chainChanged",
                        "accountsChanged"
                    ]
                }
            }, q = {
                requiredNamespaces: {},
                optionalNamespaces: fe,
                relays: [
                    {
                        protocol: "irn"
                    }
                ],
                pairingTopic: g,
                proposer: {
                    publicKey: D,
                    metadata: this.client.metadata
                },
                expiryTimestamp: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["calcExpiry"])(N.wc_sessionPropose.req.ttl),
                id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["payloadId"])()
            }, { done: Rt, resolve: je, reject: Se } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createDelayedPromise"])(T, "Request expired"), te = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["payloadId"])(), le = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["engineEvent"])("session_connect", q.id), Re = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["engineEvent"])("session_request", te), pe = async ({ error: x, session: L })=>{
                this.events.off(Re, ve), x ? Se(x) : L && je({
                    session: L
                });
            }, ve = async (x)=>{
                var L, Fe, Qe;
                if (await this.deletePendingAuthRequest(te, {
                    message: "fulfilled",
                    code: 0
                }), x.error) {
                    const ie = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getSdkError"])("WC_METHOD_UNSUPPORTED", "wc_sessionAuthenticate");
                    return x.error.code === ie.code ? void 0 : (this.events.off(le, pe), Se(x.error.message));
                }
                await this.deleteProposal(q.id), this.events.off(le, pe);
                const { cacaos: He, responder: Q } = x.result, Te = [], ze = [];
                for (const ie of He){
                    await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["validateSignedCacao"])({
                        cacao: ie,
                        projectId: this.client.core.projectId
                    }) || (this.client.logger.error(ie, "Signature verification failed"), Se((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getSdkError"])("SESSION_SETTLEMENT_FAILED", "Signature verification failed")));
                    const { p: qe } = ie, Pe = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getRecapFromResources"])(qe.resources), Ye = [
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getNamespacedDidChainId"])(qe.iss)
                    ], vt = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getDidAddress"])(qe.iss);
                    if (Pe) {
                        const Ne = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getMethodsFromRecap"])(Pe), It = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getChainsFromRecap"])(Pe);
                        Te.push(...Ne), Ye.push(...It);
                    }
                    for (const Ne of Ye)ze.push(`${Ne}:${vt}`);
                }
                const se = await this.client.core.crypto.generateSharedKey(D, Q.publicKey);
                let he;
                Te.length > 0 && (he = {
                    topic: se,
                    acknowledged: !0,
                    self: {
                        publicKey: D,
                        metadata: this.client.metadata
                    },
                    peer: Q,
                    controller: Q.publicKey,
                    expiry: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["calcExpiry"])(J),
                    requiredNamespaces: {},
                    optionalNamespaces: {},
                    relay: {
                        protocol: "irn"
                    },
                    pairingTopic: g,
                    namespaces: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["buildNamespacesFromAuth"])([
                        ...new Set(Te)
                    ], [
                        ...new Set(ze)
                    ]),
                    transportType: r
                }, await this.client.core.relayer.subscribe(se, {
                    transportType: r
                }), await this.client.session.set(se, he), g && await this.client.core.pairing.updateMetadata({
                    topic: g,
                    metadata: Q.metadata
                }), he = this.client.session.get(se)), (L = this.client.metadata.redirect) != null && L.linkMode && (Fe = Q.metadata.redirect) != null && Fe.linkMode && (Qe = Q.metadata.redirect) != null && Qe.universal && t && (this.client.core.addLinkModeSupportedApp(Q.metadata.redirect.universal), this.client.session.update(se, {
                    transportType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TRANSPORT_TYPES"].link_mode
                })), je({
                    auths: He,
                    session: he
                });
            };
            this.events.once(le, pe), this.events.once(Re, ve);
            let Ie;
            try {
                if (i) {
                    const x = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatJsonRpcRequest"])("wc_sessionAuthenticate", K, te);
                    this.client.core.history.set(g, x);
                    const L = await this.client.core.crypto.encode("", x, {
                        type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TYPE_2"],
                        encoding: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BASE64URL"]
                    });
                    Ie = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getLinkModeURL"])(t, g, L);
                } else await Promise.all([
                    this.sendRequest({
                        topic: g,
                        method: "wc_sessionAuthenticate",
                        params: K,
                        expiry: e.expiry,
                        throwOnFailedPublish: !0,
                        clientRpcId: te
                    }),
                    this.sendRequest({
                        topic: g,
                        method: "wc_sessionPropose",
                        params: q,
                        expiry: N.wc_sessionPropose.req.ttl,
                        throwOnFailedPublish: !0,
                        clientRpcId: q.id
                    })
                ]);
            } catch (x) {
                throw this.events.off(le, pe), this.events.off(Re, ve), x;
            }
            return await this.setProposal(q.id, q), await this.setAuthRequest(te, {
                request: b(v({}, K), {
                    verifyContext: {}
                }),
                pairingTopic: g,
                transportType: r
            }), {
                uri: Ie ?? A,
                response: Rt
            };
        }), c(this, "approveSessionAuthenticate", async (e)=>{
            const { id: t, auths: s } = e, i = this.client.core.eventClient.createEvent({
                properties: {
                    topic: t.toString(),
                    trace: [
                        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_CLIENT_AUTHENTICATE_TRACES"].authenticated_session_approve_started
                    ]
                }
            });
            try {
                this.isInitialized();
            } catch (f) {
                throw i.setError(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_CLIENT_AUTHENTICATE_ERRORS"].no_internet_connection), f;
            }
            const r = this.getPendingAuthRequest(t);
            if (!r) throw i.setError(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_CLIENT_AUTHENTICATE_ERRORS"].authenticated_session_pending_request_not_found), new Error(`Could not find pending auth request with id ${t}`);
            const o = r.transportType || __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TRANSPORT_TYPES"].relay;
            o === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TRANSPORT_TYPES"].relay && await this.confirmOnlineStateOrThrow();
            const a = r.requester.publicKey, l = await this.client.core.crypto.generateKeyPair(), p = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["hashKey"])(a), h = {
                type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TYPE_1"],
                receiverPublicKey: a,
                senderPublicKey: l
            }, u = [], d = [];
            for (const f of s){
                if (!await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["validateSignedCacao"])({
                    cacao: f,
                    projectId: this.client.core.projectId
                })) {
                    i.setError(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_CLIENT_AUTHENTICATE_ERRORS"].invalid_cacao);
                    const I = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getSdkError"])("SESSION_SETTLEMENT_FAILED", "Signature verification failed");
                    throw await this.sendError({
                        id: t,
                        topic: p,
                        error: I,
                        encodeOpts: h
                    }), new Error(I.message);
                }
                i.addTrace(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_CLIENT_AUTHENTICATE_TRACES"].cacaos_verified);
                const { p: _ } = f, g = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getRecapFromResources"])(_.resources), A = [
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getNamespacedDidChainId"])(_.iss)
                ], D = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getDidAddress"])(_.iss);
                if (g) {
                    const I = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getMethodsFromRecap"])(g), T = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getChainsFromRecap"])(g);
                    u.push(...I), A.push(...T);
                }
                for (const I of A)d.push(`${I}:${D}`);
            }
            const w = await this.client.core.crypto.generateSharedKey(l, a);
            i.addTrace(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_CLIENT_AUTHENTICATE_TRACES"].create_authenticated_session_topic);
            let m;
            if (u?.length > 0) {
                m = {
                    topic: w,
                    acknowledged: !0,
                    self: {
                        publicKey: l,
                        metadata: this.client.metadata
                    },
                    peer: {
                        publicKey: a,
                        metadata: r.requester.metadata
                    },
                    controller: a,
                    expiry: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["calcExpiry"])(J),
                    authentication: s,
                    requiredNamespaces: {},
                    optionalNamespaces: {},
                    relay: {
                        protocol: "irn"
                    },
                    pairingTopic: r.pairingTopic,
                    namespaces: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["buildNamespacesFromAuth"])([
                        ...new Set(u)
                    ], [
                        ...new Set(d)
                    ]),
                    transportType: o
                }, i.addTrace(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_CLIENT_AUTHENTICATE_TRACES"].subscribing_authenticated_session_topic);
                try {
                    await this.client.core.relayer.subscribe(w, {
                        transportType: o
                    });
                } catch (f) {
                    throw i.setError(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_CLIENT_AUTHENTICATE_ERRORS"].subscribe_authenticated_session_topic_failure), f;
                }
                i.addTrace(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_CLIENT_AUTHENTICATE_TRACES"].subscribe_authenticated_session_topic_success), await this.client.session.set(w, m), i.addTrace(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_CLIENT_AUTHENTICATE_TRACES"].store_authenticated_session), await this.client.core.pairing.updateMetadata({
                    topic: r.pairingTopic,
                    metadata: r.requester.metadata
                });
            }
            i.addTrace(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_CLIENT_AUTHENTICATE_TRACES"].publishing_authenticated_session_approve);
            try {
                await this.sendResult({
                    topic: p,
                    id: t,
                    result: {
                        cacaos: s,
                        responder: {
                            publicKey: l,
                            metadata: this.client.metadata
                        }
                    },
                    encodeOpts: h,
                    throwOnFailedPublish: !0,
                    appLink: this.getAppLinkIfEnabled(r.requester.metadata, o)
                });
            } catch (f) {
                throw i.setError(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_CLIENT_AUTHENTICATE_ERRORS"].authenticated_session_approve_publish_failure), f;
            }
            return await this.client.auth.requests.delete(t, {
                message: "fulfilled",
                code: 0
            }), await this.client.core.pairing.activate({
                topic: r.pairingTopic
            }), this.client.core.eventClient.deleteEvent({
                eventId: i.eventId
            }), {
                session: m
            };
        }), c(this, "rejectSessionAuthenticate", async (e)=>{
            this.isInitialized();
            const { id: t, reason: s } = e, i = this.getPendingAuthRequest(t);
            if (!i) throw new Error(`Could not find pending auth request with id ${t}`);
            i.transportType === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TRANSPORT_TYPES"].relay && await this.confirmOnlineStateOrThrow();
            const r = i.requester.publicKey, o = await this.client.core.crypto.generateKeyPair(), a = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["hashKey"])(r), l = {
                type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TYPE_1"],
                receiverPublicKey: r,
                senderPublicKey: o
            };
            await this.sendError({
                id: t,
                topic: a,
                error: s,
                encodeOpts: l,
                rpcOpts: N.wc_sessionAuthenticate.reject,
                appLink: this.getAppLinkIfEnabled(i.requester.metadata, i.transportType)
            }), await this.client.auth.requests.delete(t, {
                message: "rejected",
                code: 0
            }), await this.client.proposal.delete(t, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getSdkError"])("USER_DISCONNECTED"));
        }), c(this, "formatAuthMessage", (e)=>{
            this.isInitialized();
            const { request: t, iss: s } = e;
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatMessage"])(t, s);
        }), c(this, "processRelayMessageCache", ()=>{
            setTimeout(async ()=>{
                if (this.relayMessageCache.length !== 0) for(; this.relayMessageCache.length > 0;)try {
                    const e = this.relayMessageCache.shift();
                    e && await this.onRelayMessage(e);
                } catch (e) {
                    this.client.logger.error(e);
                }
            }, 50);
        }), c(this, "cleanupDuplicatePairings", async (e)=>{
            if (e.pairingTopic) try {
                const t = this.client.core.pairing.pairings.get(e.pairingTopic), s = this.client.core.pairing.pairings.getAll().filter((i)=>{
                    var r, o;
                    return ((r = i.peerMetadata) == null ? void 0 : r.url) && ((o = i.peerMetadata) == null ? void 0 : o.url) === e.peer.metadata.url && i.topic && i.topic !== t.topic;
                });
                if (s.length === 0) return;
                this.client.logger.info(`Cleaning up ${s.length} duplicate pairing(s)`), await Promise.all(s.map((i)=>this.client.core.pairing.disconnect({
                        topic: i.topic
                    }))), this.client.logger.info("Duplicate pairings clean up finished");
            } catch (t) {
                this.client.logger.error(t);
            }
        }), c(this, "deleteSession", async (e)=>{
            var t;
            const { topic: s, expirerHasDeleted: i = !1, emitEvent: r = !0, id: o = 0 } = e, { self: a } = this.client.session.get(s);
            await this.client.core.relayer.unsubscribe(s), await this.client.session.delete(s, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getSdkError"])("USER_DISCONNECTED")), this.addToRecentlyDeleted(s, "session"), this.client.core.crypto.keychain.has(a.publicKey) && await this.client.core.crypto.deleteKeyPair(a.publicKey), this.client.core.crypto.keychain.has(s) && await this.client.core.crypto.deleteSymKey(s), i || this.client.core.expirer.del(s), this.client.core.storage.removeItem(Me).catch((l)=>this.client.logger.warn(l)), this.getPendingSessionRequests().forEach((l)=>{
                l.topic === s && this.deletePendingSessionRequest(l.id, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getSdkError"])("USER_DISCONNECTED"));
            }), s === ((t = this.sessionRequestQueue.queue[0]) == null ? void 0 : t.topic) && (this.sessionRequestQueue.state = $.idle), r && this.client.events.emit("session_delete", {
                id: o,
                topic: s
            });
        }), c(this, "deleteProposal", async (e, t)=>{
            if (t) try {
                const s = this.client.proposal.get(e), i = this.client.core.eventClient.getEvent({
                    topic: s.pairingTopic
                });
                i?.setError(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_CLIENT_SESSION_ERRORS"].proposal_expired);
            } catch  {}
            await Promise.all([
                this.client.proposal.delete(e, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getSdkError"])("USER_DISCONNECTED")),
                t ? Promise.resolve() : this.client.core.expirer.del(e)
            ]), this.addToRecentlyDeleted(e, "proposal");
        }), c(this, "deletePendingSessionRequest", async (e, t, s = !1)=>{
            await Promise.all([
                this.client.pendingRequest.delete(e, t),
                s ? Promise.resolve() : this.client.core.expirer.del(e)
            ]), this.addToRecentlyDeleted(e, "request"), this.sessionRequestQueue.queue = this.sessionRequestQueue.queue.filter((i)=>i.id !== e), s && (this.sessionRequestQueue.state = $.idle, this.client.events.emit("session_request_expire", {
                id: e
            }));
        }), c(this, "deletePendingAuthRequest", async (e, t, s = !1)=>{
            await Promise.all([
                this.client.auth.requests.delete(e, t),
                s ? Promise.resolve() : this.client.core.expirer.del(e)
            ]);
        }), c(this, "setExpiry", async (e, t)=>{
            this.client.session.keys.includes(e) && (this.client.core.expirer.set(e, t), await this.client.session.update(e, {
                expiry: t
            }));
        }), c(this, "setProposal", async (e, t)=>{
            this.client.core.expirer.set(e, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["calcExpiry"])(N.wc_sessionPropose.req.ttl)), await this.client.proposal.set(e, t);
        }), c(this, "setAuthRequest", async (e, t)=>{
            const { request: s, pairingTopic: i, transportType: r = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TRANSPORT_TYPES"].relay } = t;
            this.client.core.expirer.set(e, s.expiryTimestamp), await this.client.auth.requests.set(e, {
                authPayload: s.authPayload,
                requester: s.requester,
                expiryTimestamp: s.expiryTimestamp,
                id: e,
                pairingTopic: i,
                verifyContext: s.verifyContext,
                transportType: r
            });
        }), c(this, "setPendingSessionRequest", async (e)=>{
            const { id: t, topic: s, params: i, verifyContext: r } = e, o = i.request.expiryTimestamp || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["calcExpiry"])(N.wc_sessionRequest.req.ttl);
            this.client.core.expirer.set(t, o), await this.client.pendingRequest.set(t, {
                id: t,
                topic: s,
                params: i,
                verifyContext: r
            });
        }), c(this, "sendRequest", async (e)=>{
            const { topic: t, method: s, params: i, expiry: r, relayRpcId: o, clientRpcId: a, throwOnFailedPublish: l, appLink: p, tvf: h } = e, u = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatJsonRpcRequest"])(s, i, a);
            let d;
            const w = !!p;
            try {
                const _ = w ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BASE64URL"] : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BASE64"];
                d = await this.client.core.crypto.encode(t, u, {
                    encoding: _
                });
            } catch (_) {
                throw await this.cleanup(), this.client.logger.error(`sendRequest() -> core.crypto.encode() for topic ${t} failed`), _;
            }
            let m;
            if (gt.includes(s)) {
                const _ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["hashMessage"])(JSON.stringify(u)), g = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["hashMessage"])(d);
                m = await this.client.core.verify.register({
                    id: g,
                    decryptedId: _
                });
            }
            const f = N[s].req;
            if (f.attestation = m, r && (f.ttl = r), o && (f.id = o), this.client.core.history.set(t, u), w) {
                const _ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getLinkModeURL"])(p, t, d);
                await global.Linking.openURL(_, this.client.name);
            } else {
                const _ = N[s].req;
                r && (_.ttl = r), o && (_.id = o), _.tvf = b(v({}, h), {
                    correlationId: u.id
                }), l ? (_.internal = b(v({}, _.internal), {
                    throwOnFailedPublish: !0
                }), await this.client.core.relayer.publish(t, d, _)) : this.client.core.relayer.publish(t, d, _).catch((g)=>this.client.logger.error(g));
            }
            return u.id;
        }), c(this, "sendResult", async (e)=>{
            const { id: t, topic: s, result: i, throwOnFailedPublish: r, encodeOpts: o, appLink: a } = e, l = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatJsonRpcResult"])(t, i);
            let p;
            const h = a && typeof (global == null ? void 0 : global.Linking) < "u";
            try {
                const w = h ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BASE64URL"] : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BASE64"];
                p = await this.client.core.crypto.encode(s, l, b(v({}, o || {}), {
                    encoding: w
                }));
            } catch (w) {
                throw await this.cleanup(), this.client.logger.error(`sendResult() -> core.crypto.encode() for topic ${s} failed`), w;
            }
            let u, d;
            try {
                u = await this.client.core.history.get(s, t);
                const w = u.request;
                try {
                    this.shouldSetTVF(w.method, w.params) && (d = this.getTVFParams(t, w.params, i));
                } catch (m) {
                    this.client.logger.warn("sendResult() -> getTVFParams() failed", m);
                }
            } catch (w) {
                throw this.client.logger.error(`sendResult() -> history.get(${s}, ${t}) failed`), w;
            }
            if (h) {
                const w = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getLinkModeURL"])(a, s, p);
                await global.Linking.openURL(w, this.client.name);
            } else {
                const w = u.request.method, m = N[w].res;
                m.tvf = b(v({}, d), {
                    correlationId: t
                }), r ? (m.internal = b(v({}, m.internal), {
                    throwOnFailedPublish: !0
                }), await this.client.core.relayer.publish(s, p, m)) : this.client.core.relayer.publish(s, p, m).catch((f)=>this.client.logger.error(f));
            }
            await this.client.core.history.resolve(l);
        }), c(this, "sendError", async (e)=>{
            const { id: t, topic: s, error: i, encodeOpts: r, rpcOpts: o, appLink: a } = e, l = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatJsonRpcError"])(t, i);
            let p;
            const h = a && typeof (global == null ? void 0 : global.Linking) < "u";
            try {
                const d = h ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BASE64URL"] : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BASE64"];
                p = await this.client.core.crypto.encode(s, l, b(v({}, r || {}), {
                    encoding: d
                }));
            } catch (d) {
                throw await this.cleanup(), this.client.logger.error(`sendError() -> core.crypto.encode() for topic ${s} failed`), d;
            }
            let u;
            try {
                u = await this.client.core.history.get(s, t);
            } catch (d) {
                throw this.client.logger.error(`sendError() -> history.get(${s}, ${t}) failed`), d;
            }
            if (h) {
                const d = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getLinkModeURL"])(a, s, p);
                await global.Linking.openURL(d, this.client.name);
            } else {
                const d = u.request.method, w = o || N[d].res;
                this.client.core.relayer.publish(s, p, w);
            }
            await this.client.core.history.resolve(l);
        }), c(this, "cleanup", async ()=>{
            const e = [], t = [];
            this.client.session.getAll().forEach((s)=>{
                let i = !1;
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isExpired"])(s.expiry) && (i = !0), this.client.core.crypto.keychain.has(s.topic) || (i = !0), i && e.push(s.topic);
            }), this.client.proposal.getAll().forEach((s)=>{
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isExpired"])(s.expiryTimestamp) && t.push(s.id);
            }), await Promise.all([
                ...e.map((s)=>this.deleteSession({
                        topic: s
                    })),
                ...t.map((s)=>this.deleteProposal(s))
            ]);
        }), c(this, "onProviderMessageEvent", async (e)=>{
            !this.initialized || this.relayMessageCache.length > 0 ? this.relayMessageCache.push(e) : await this.onRelayMessage(e);
        }), c(this, "onRelayEventRequest", async (e)=>{
            this.requestQueue.queue.push(e), await this.processRequestsQueue();
        }), c(this, "processRequestsQueue", async ()=>{
            if (this.requestQueue.state === $.active) {
                this.client.logger.info("Request queue already active, skipping...");
                return;
            }
            for(this.client.logger.info(`Request queue starting with ${this.requestQueue.queue.length} requests`); this.requestQueue.queue.length > 0;){
                this.requestQueue.state = $.active;
                const e = this.requestQueue.queue.shift();
                if (e) try {
                    await this.processRequest(e);
                } catch (t) {
                    this.client.logger.warn(t);
                }
            }
            this.requestQueue.state = $.idle;
        }), c(this, "processRequest", async (e)=>{
            const { topic: t, payload: s, attestation: i, transportType: r, encryptedId: o } = e, a = s.method;
            if (!this.shouldIgnorePairingRequest({
                topic: t,
                requestMethod: a
            })) switch(a){
                case "wc_sessionPropose":
                    return await this.onSessionProposeRequest({
                        topic: t,
                        payload: s,
                        attestation: i,
                        encryptedId: o
                    });
                case "wc_sessionSettle":
                    return await this.onSessionSettleRequest(t, s);
                case "wc_sessionUpdate":
                    return await this.onSessionUpdateRequest(t, s);
                case "wc_sessionExtend":
                    return await this.onSessionExtendRequest(t, s);
                case "wc_sessionPing":
                    return await this.onSessionPingRequest(t, s);
                case "wc_sessionDelete":
                    return await this.onSessionDeleteRequest(t, s);
                case "wc_sessionRequest":
                    return await this.onSessionRequest({
                        topic: t,
                        payload: s,
                        attestation: i,
                        encryptedId: o,
                        transportType: r
                    });
                case "wc_sessionEvent":
                    return await this.onSessionEventRequest(t, s);
                case "wc_sessionAuthenticate":
                    return await this.onSessionAuthenticateRequest({
                        topic: t,
                        payload: s,
                        attestation: i,
                        encryptedId: o,
                        transportType: r
                    });
                default:
                    return this.client.logger.info(`Unsupported request method ${a}`);
            }
        }), c(this, "onRelayEventResponse", async (e)=>{
            const { topic: t, payload: s, transportType: i } = e, r = (await this.client.core.history.get(t, s.id)).request.method;
            switch(r){
                case "wc_sessionPropose":
                    return this.onSessionProposeResponse(t, s, i);
                case "wc_sessionSettle":
                    return this.onSessionSettleResponse(t, s);
                case "wc_sessionUpdate":
                    return this.onSessionUpdateResponse(t, s);
                case "wc_sessionExtend":
                    return this.onSessionExtendResponse(t, s);
                case "wc_sessionPing":
                    return this.onSessionPingResponse(t, s);
                case "wc_sessionRequest":
                    return this.onSessionRequestResponse(t, s);
                case "wc_sessionAuthenticate":
                    return this.onSessionAuthenticateResponse(t, s);
                default:
                    return this.client.logger.info(`Unsupported response method ${r}`);
            }
        }), c(this, "onRelayEventUnknownPayload", (e)=>{
            const { topic: t } = e, { message: s } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("MISSING_OR_INVALID", `Decoded payload on topic ${t} is not identifiable as a JSON-RPC request or a response.`);
            throw new Error(s);
        }), c(this, "shouldIgnorePairingRequest", (e)=>{
            const { topic: t, requestMethod: s } = e, i = this.expectedPairingMethodMap.get(t);
            return !i || i.includes(s) ? !1 : !!(i.includes("wc_sessionAuthenticate") && this.client.events.listenerCount("session_authenticate") > 0);
        }), c(this, "onSessionProposeRequest", async (e)=>{
            const { topic: t, payload: s, attestation: i, encryptedId: r } = e, { params: o, id: a } = s;
            try {
                const l = this.client.core.eventClient.getEvent({
                    topic: t
                });
                this.client.events.listenerCount("session_proposal") === 0 && (console.warn("No listener for session_proposal event"), l?.setError(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_CLIENT_PAIRING_ERRORS"].proposal_listener_not_found)), this.isValidConnect(v({}, s.params));
                const p = o.expiryTimestamp || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["calcExpiry"])(N.wc_sessionPropose.req.ttl), h = v({
                    id: a,
                    pairingTopic: t,
                    expiryTimestamp: p
                }, o);
                await this.setProposal(a, h);
                const u = await this.getVerifyContext({
                    attestationId: i,
                    hash: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["hashMessage"])(JSON.stringify(s)),
                    encryptedId: r,
                    metadata: h.proposer.metadata
                });
                l?.addTrace(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_CLIENT_PAIRING_TRACES"].emit_session_proposal), this.client.events.emit("session_proposal", {
                    id: a,
                    params: h,
                    verifyContext: u
                });
            } catch (l) {
                await this.sendError({
                    id: a,
                    topic: t,
                    error: l,
                    rpcOpts: N.wc_sessionPropose.autoReject
                }), this.client.logger.error(l);
            }
        }), c(this, "onSessionProposeResponse", async (e, t, s)=>{
            const { id: i } = t;
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$validators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isJsonRpcResult"])(t)) {
                const { result: r } = t;
                this.client.logger.trace({
                    type: "method",
                    method: "onSessionProposeResponse",
                    result: r
                });
                const o = this.client.proposal.get(i);
                this.client.logger.trace({
                    type: "method",
                    method: "onSessionProposeResponse",
                    proposal: o
                });
                const a = o.proposer.publicKey;
                this.client.logger.trace({
                    type: "method",
                    method: "onSessionProposeResponse",
                    selfPublicKey: a
                });
                const l = r.responderPublicKey;
                this.client.logger.trace({
                    type: "method",
                    method: "onSessionProposeResponse",
                    peerPublicKey: l
                });
                const p = await this.client.core.crypto.generateSharedKey(a, l);
                this.pendingSessions.set(i, {
                    sessionTopic: p,
                    pairingTopic: e,
                    proposalId: i,
                    publicKey: a
                });
                const h = await this.client.core.relayer.subscribe(p, {
                    transportType: s
                });
                this.client.logger.trace({
                    type: "method",
                    method: "onSessionProposeResponse",
                    subscriptionId: h
                }), await this.client.core.pairing.activate({
                    topic: e
                });
            } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$validators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isJsonRpcError"])(t)) {
                await this.client.proposal.delete(i, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getSdkError"])("USER_DISCONNECTED"));
                const r = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["engineEvent"])("session_connect", i);
                if (this.events.listenerCount(r) === 0) throw new Error(`emitting ${r} without any listeners, 954`);
                this.events.emit(r, {
                    error: t.error
                });
            }
        }), c(this, "onSessionSettleRequest", async (e, t)=>{
            const { id: s, params: i } = t;
            try {
                this.isValidSessionSettleRequest(i);
                const { relay: r, controller: o, expiry: a, namespaces: l, sessionProperties: p, scopedProperties: h, sessionConfig: u } = t.params, d = [
                    ...this.pendingSessions.values()
                ].find((f)=>f.sessionTopic === e);
                if (!d) return this.client.logger.error(`Pending session not found for topic ${e}`);
                const w = this.client.proposal.get(d.proposalId), m = b(v(v(v({
                    topic: e,
                    relay: r,
                    expiry: a,
                    namespaces: l,
                    acknowledged: !0,
                    pairingTopic: d.pairingTopic,
                    requiredNamespaces: w.requiredNamespaces,
                    optionalNamespaces: w.optionalNamespaces,
                    controller: o.publicKey,
                    self: {
                        publicKey: d.publicKey,
                        metadata: this.client.metadata
                    },
                    peer: {
                        publicKey: o.publicKey,
                        metadata: o.metadata
                    }
                }, p && {
                    sessionProperties: p
                }), h && {
                    scopedProperties: h
                }), u && {
                    sessionConfig: u
                }), {
                    transportType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TRANSPORT_TYPES"].relay
                });
                await this.client.session.set(m.topic, m), await this.setExpiry(m.topic, m.expiry), await this.client.core.pairing.updateMetadata({
                    topic: d.pairingTopic,
                    metadata: m.peer.metadata
                }), this.client.events.emit("session_connect", {
                    session: m
                }), this.events.emit((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["engineEvent"])("session_connect", d.proposalId), {
                    session: m
                }), this.pendingSessions.delete(d.proposalId), this.deleteProposal(d.proposalId, !1), this.cleanupDuplicatePairings(m), await this.sendResult({
                    id: t.id,
                    topic: e,
                    result: !0,
                    throwOnFailedPublish: !0
                });
            } catch (r) {
                await this.sendError({
                    id: s,
                    topic: e,
                    error: r
                }), this.client.logger.error(r);
            }
        }), c(this, "onSessionSettleResponse", async (e, t)=>{
            const { id: s } = t;
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$validators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isJsonRpcResult"])(t) ? (await this.client.session.update(e, {
                acknowledged: !0
            }), this.events.emit((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["engineEvent"])("session_approve", s), {})) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$validators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isJsonRpcError"])(t) && (await this.client.session.delete(e, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getSdkError"])("USER_DISCONNECTED")), this.events.emit((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["engineEvent"])("session_approve", s), {
                error: t.error
            }));
        }), c(this, "onSessionUpdateRequest", async (e, t)=>{
            const { params: s, id: i } = t;
            try {
                const r = `${e}_session_update`, o = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MemoryStore"].get(r);
                if (o && this.isRequestOutOfSync(o, i)) {
                    this.client.logger.warn(`Discarding out of sync request - ${i}`), this.sendError({
                        id: i,
                        topic: e,
                        error: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getSdkError"])("INVALID_UPDATE_REQUEST")
                    });
                    return;
                }
                this.isValidUpdate(v({
                    topic: e
                }, s));
                try {
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MemoryStore"].set(r, i), await this.client.session.update(e, {
                        namespaces: s.namespaces
                    }), await this.sendResult({
                        id: i,
                        topic: e,
                        result: !0,
                        throwOnFailedPublish: !0
                    });
                } catch (a) {
                    throw __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MemoryStore"].delete(r), a;
                }
                this.client.events.emit("session_update", {
                    id: i,
                    topic: e,
                    params: s
                });
            } catch (r) {
                await this.sendError({
                    id: i,
                    topic: e,
                    error: r
                }), this.client.logger.error(r);
            }
        }), c(this, "isRequestOutOfSync", (e, t)=>t.toString().slice(0, -3) < e.toString().slice(0, -3)), c(this, "onSessionUpdateResponse", (e, t)=>{
            const { id: s } = t, i = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["engineEvent"])("session_update", s);
            if (this.events.listenerCount(i) === 0) throw new Error(`emitting ${i} without any listeners`);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$validators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isJsonRpcResult"])(t) ? this.events.emit((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["engineEvent"])("session_update", s), {}) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$validators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isJsonRpcError"])(t) && this.events.emit((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["engineEvent"])("session_update", s), {
                error: t.error
            });
        }), c(this, "onSessionExtendRequest", async (e, t)=>{
            const { id: s } = t;
            try {
                this.isValidExtend({
                    topic: e
                }), await this.setExpiry(e, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["calcExpiry"])(J)), await this.sendResult({
                    id: s,
                    topic: e,
                    result: !0,
                    throwOnFailedPublish: !0
                }), this.client.events.emit("session_extend", {
                    id: s,
                    topic: e
                });
            } catch (i) {
                await this.sendError({
                    id: s,
                    topic: e,
                    error: i
                }), this.client.logger.error(i);
            }
        }), c(this, "onSessionExtendResponse", (e, t)=>{
            const { id: s } = t, i = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["engineEvent"])("session_extend", s);
            if (this.events.listenerCount(i) === 0) throw new Error(`emitting ${i} without any listeners`);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$validators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isJsonRpcResult"])(t) ? this.events.emit((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["engineEvent"])("session_extend", s), {}) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$validators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isJsonRpcError"])(t) && this.events.emit((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["engineEvent"])("session_extend", s), {
                error: t.error
            });
        }), c(this, "onSessionPingRequest", async (e, t)=>{
            const { id: s } = t;
            try {
                this.isValidPing({
                    topic: e
                }), await this.sendResult({
                    id: s,
                    topic: e,
                    result: !0,
                    throwOnFailedPublish: !0
                }), this.client.events.emit("session_ping", {
                    id: s,
                    topic: e
                });
            } catch (i) {
                await this.sendError({
                    id: s,
                    topic: e,
                    error: i
                }), this.client.logger.error(i);
            }
        }), c(this, "onSessionPingResponse", (e, t)=>{
            const { id: s } = t, i = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["engineEvent"])("session_ping", s);
            setTimeout(()=>{
                if (this.events.listenerCount(i) === 0) throw new Error(`emitting ${i} without any listeners 2176`);
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$validators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isJsonRpcResult"])(t) ? this.events.emit((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["engineEvent"])("session_ping", s), {}) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$validators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isJsonRpcError"])(t) && this.events.emit((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["engineEvent"])("session_ping", s), {
                    error: t.error
                });
            }, 500);
        }), c(this, "onSessionDeleteRequest", async (e, t)=>{
            const { id: s } = t;
            try {
                this.isValidDisconnect({
                    topic: e,
                    reason: t.params
                }), Promise.all([
                    new Promise((i)=>{
                        this.client.core.relayer.once(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RELAYER_EVENTS"].publish, async ()=>{
                            i(await this.deleteSession({
                                topic: e,
                                id: s
                            }));
                        });
                    }),
                    this.sendResult({
                        id: s,
                        topic: e,
                        result: !0,
                        throwOnFailedPublish: !0
                    }),
                    this.cleanupPendingSentRequestsForTopic({
                        topic: e,
                        error: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getSdkError"])("USER_DISCONNECTED")
                    })
                ]).catch((i)=>this.client.logger.error(i));
            } catch (i) {
                this.client.logger.error(i);
            }
        }), c(this, "onSessionRequest", async (e)=>{
            var t, s, i;
            const { topic: r, payload: o, attestation: a, encryptedId: l, transportType: p } = e, { id: h, params: u } = o;
            try {
                await this.isValidRequest(v({
                    topic: r
                }, u));
                const d = this.client.session.get(r), w = await this.getVerifyContext({
                    attestationId: a,
                    hash: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["hashMessage"])(JSON.stringify((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatJsonRpcRequest"])("wc_sessionRequest", u, h))),
                    encryptedId: l,
                    metadata: d.peer.metadata,
                    transportType: p
                }), m = {
                    id: h,
                    topic: r,
                    params: u,
                    verifyContext: w
                };
                await this.setPendingSessionRequest(m), p === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TRANSPORT_TYPES"].link_mode && (t = d.peer.metadata.redirect) != null && t.universal && this.client.core.addLinkModeSupportedApp((s = d.peer.metadata.redirect) == null ? void 0 : s.universal), (i = this.client.signConfig) != null && i.disableRequestQueue ? this.emitSessionRequest(m) : (this.addSessionRequestToSessionRequestQueue(m), this.processSessionRequestQueue());
            } catch (d) {
                await this.sendError({
                    id: h,
                    topic: r,
                    error: d
                }), this.client.logger.error(d);
            }
        }), c(this, "onSessionRequestResponse", (e, t)=>{
            const { id: s } = t, i = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["engineEvent"])("session_request", s);
            if (this.events.listenerCount(i) === 0) throw new Error(`emitting ${i} without any listeners`);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$validators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isJsonRpcResult"])(t) ? this.events.emit((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["engineEvent"])("session_request", s), {
                result: t.result
            }) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$validators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isJsonRpcError"])(t) && this.events.emit((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["engineEvent"])("session_request", s), {
                error: t.error
            });
        }), c(this, "onSessionEventRequest", async (e, t)=>{
            const { id: s, params: i } = t;
            try {
                const r = `${e}_session_event_${i.event.name}`, o = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MemoryStore"].get(r);
                if (o && this.isRequestOutOfSync(o, s)) {
                    this.client.logger.info(`Discarding out of sync request - ${s}`);
                    return;
                }
                this.isValidEmit(v({
                    topic: e
                }, i)), this.client.events.emit("session_event", {
                    id: s,
                    topic: e,
                    params: i
                }), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MemoryStore"].set(r, s);
            } catch (r) {
                await this.sendError({
                    id: s,
                    topic: e,
                    error: r
                }), this.client.logger.error(r);
            }
        }), c(this, "onSessionAuthenticateResponse", (e, t)=>{
            const { id: s } = t;
            this.client.logger.trace({
                type: "method",
                method: "onSessionAuthenticateResponse",
                topic: e,
                payload: t
            }), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$validators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isJsonRpcResult"])(t) ? this.events.emit((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["engineEvent"])("session_request", s), {
                result: t.result
            }) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$validators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isJsonRpcError"])(t) && this.events.emit((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["engineEvent"])("session_request", s), {
                error: t.error
            });
        }), c(this, "onSessionAuthenticateRequest", async (e)=>{
            var t;
            const { topic: s, payload: i, attestation: r, encryptedId: o, transportType: a } = e;
            try {
                const { requester: l, authPayload: p, expiryTimestamp: h } = i.params, u = await this.getVerifyContext({
                    attestationId: r,
                    hash: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["hashMessage"])(JSON.stringify(i)),
                    encryptedId: o,
                    metadata: l.metadata,
                    transportType: a
                }), d = {
                    requester: l,
                    pairingTopic: s,
                    id: i.id,
                    authPayload: p,
                    verifyContext: u,
                    expiryTimestamp: h
                };
                await this.setAuthRequest(i.id, {
                    request: d,
                    pairingTopic: s,
                    transportType: a
                }), a === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TRANSPORT_TYPES"].link_mode && (t = l.metadata.redirect) != null && t.universal && this.client.core.addLinkModeSupportedApp(l.metadata.redirect.universal), this.client.events.emit("session_authenticate", {
                    topic: s,
                    params: i.params,
                    id: i.id,
                    verifyContext: u
                });
            } catch (l) {
                this.client.logger.error(l);
                const p = i.params.requester.publicKey, h = await this.client.core.crypto.generateKeyPair(), u = this.getAppLinkIfEnabled(i.params.requester.metadata, a), d = {
                    type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TYPE_1"],
                    receiverPublicKey: p,
                    senderPublicKey: h
                };
                await this.sendError({
                    id: i.id,
                    topic: s,
                    error: l,
                    encodeOpts: d,
                    rpcOpts: N.wc_sessionAuthenticate.autoReject,
                    appLink: u
                });
            }
        }), c(this, "addSessionRequestToSessionRequestQueue", (e)=>{
            this.sessionRequestQueue.queue.push(e);
        }), c(this, "cleanupAfterResponse", (e)=>{
            this.deletePendingSessionRequest(e.response.id, {
                message: "fulfilled",
                code: 0
            }), setTimeout(()=>{
                this.sessionRequestQueue.state = $.idle, this.processSessionRequestQueue();
            }, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$time$2f$dist$2f$cjs$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toMiliseconds"])(this.requestQueueDelay));
        }), c(this, "cleanupPendingSentRequestsForTopic", ({ topic: e, error: t })=>{
            const s = this.client.core.history.pending;
            s.length > 0 && s.filter((i)=>i.topic === e && i.request.method === "wc_sessionRequest").forEach((i)=>{
                const r = i.request.id, o = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["engineEvent"])("session_request", r);
                if (this.events.listenerCount(o) === 0) throw new Error(`emitting ${o} without any listeners`);
                this.events.emit((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["engineEvent"])("session_request", i.request.id), {
                    error: t
                });
            });
        }), c(this, "processSessionRequestQueue", ()=>{
            if (this.sessionRequestQueue.state === $.active) {
                this.client.logger.info("session request queue is already active.");
                return;
            }
            const e = this.sessionRequestQueue.queue[0];
            if (!e) {
                this.client.logger.info("session request queue is empty.");
                return;
            }
            try {
                this.sessionRequestQueue.state = $.active, this.emitSessionRequest(e);
            } catch (t) {
                this.client.logger.error(t);
            }
        }), c(this, "emitSessionRequest", (e)=>{
            this.client.events.emit("session_request", e);
        }), c(this, "onPairingCreated", (e)=>{
            if (e.methods && this.expectedPairingMethodMap.set(e.topic, e.methods), e.active) return;
            const t = this.client.proposal.getAll().find((s)=>s.pairingTopic === e.topic);
            t && this.onSessionProposeRequest({
                topic: e.topic,
                payload: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatJsonRpcRequest"])("wc_sessionPropose", b(v({}, t), {
                    requiredNamespaces: t.requiredNamespaces,
                    optionalNamespaces: t.optionalNamespaces,
                    relays: t.relays,
                    proposer: t.proposer,
                    sessionProperties: t.sessionProperties,
                    scopedProperties: t.scopedProperties
                }), t.id)
            });
        }), c(this, "isValidConnect", async (e)=>{
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidParams"])(e)) {
                const { message: l } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("MISSING_OR_INVALID", `connect() params: ${JSON.stringify(e)}`);
                throw new Error(l);
            }
            const { pairingTopic: t, requiredNamespaces: s, optionalNamespaces: i, sessionProperties: r, scopedProperties: o, relays: a } = e;
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isUndefined"])(t) || await this.isValidPairingTopic(t), !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidRelays"])(a, !0)) {
                const { message: l } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("MISSING_OR_INVALID", `connect() relays: ${a}`);
                throw new Error(l);
            }
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isUndefined"])(s) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidObject"])(s) !== 0) {
                const l = "requiredNamespaces are deprecated and are automatically assigned to optionalNamespaces";
                [
                    "fatal",
                    "error",
                    "silent"
                ].includes(this.client.logger.level) ? console.warn(l) : this.client.logger.warn(l), this.validateNamespaces(s, "requiredNamespaces");
            }
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isUndefined"])(i) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidObject"])(i) !== 0 && this.validateNamespaces(i, "optionalNamespaces"), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isUndefined"])(r) || this.validateSessionProps(r, "sessionProperties"), !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isUndefined"])(o)) {
                this.validateSessionProps(o, "scopedProperties");
                const l = Object.keys(s || {}).concat(Object.keys(i || {}));
                if (!Object.keys(o).every((p)=>l.includes(p))) throw new Error(`Scoped properties must be a subset of required/optional namespaces, received: ${JSON.stringify(o)}, required/optional namespaces: ${JSON.stringify(l)}`);
            }
        }), c(this, "validateNamespaces", (e, t)=>{
            const s = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidRequiredNamespaces"])(e, "connect()", t);
            if (s) throw new Error(s.message);
        }), c(this, "isValidApprove", async (e)=>{
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidParams"])(e)) throw new Error((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("MISSING_OR_INVALID", `approve() params: ${e}`).message);
            const { id: t, namespaces: s, relayProtocol: i, sessionProperties: r, scopedProperties: o } = e;
            this.checkRecentlyDeleted(t), await this.isValidProposalId(t);
            const a = this.client.proposal.get(t), l = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidNamespaces"])(s, "approve()");
            if (l) throw new Error(l.message);
            const p = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isConformingNamespaces"])(a.requiredNamespaces, s, "approve()");
            if (p) throw new Error(p.message);
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidString"])(i, !0)) {
                const { message: h } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("MISSING_OR_INVALID", `approve() relayProtocol: ${i}`);
                throw new Error(h);
            }
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isUndefined"])(r) || this.validateSessionProps(r, "sessionProperties"), !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isUndefined"])(o)) {
                this.validateSessionProps(o, "scopedProperties");
                const h = new Set(Object.keys(s));
                if (!Object.keys(o).every((u)=>h.has(u))) throw new Error(`Scoped properties must be a subset of approved namespaces, received: ${JSON.stringify(o)}, approved namespaces: ${Array.from(h).join(", ")}`);
            }
        }), c(this, "isValidReject", async (e)=>{
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidParams"])(e)) {
                const { message: i } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("MISSING_OR_INVALID", `reject() params: ${e}`);
                throw new Error(i);
            }
            const { id: t, reason: s } = e;
            if (this.checkRecentlyDeleted(t), await this.isValidProposalId(t), !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidErrorReason"])(s)) {
                const { message: i } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("MISSING_OR_INVALID", `reject() reason: ${JSON.stringify(s)}`);
                throw new Error(i);
            }
        }), c(this, "isValidSessionSettleRequest", (e)=>{
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidParams"])(e)) {
                const { message: l } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("MISSING_OR_INVALID", `onSessionSettleRequest() params: ${e}`);
                throw new Error(l);
            }
            const { relay: t, controller: s, namespaces: i, expiry: r } = e;
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidRelay"])(t)) {
                const { message: l } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("MISSING_OR_INVALID", "onSessionSettleRequest() relay protocol should be a string");
                throw new Error(l);
            }
            const o = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidController"])(s, "onSessionSettleRequest()");
            if (o) throw new Error(o.message);
            const a = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidNamespaces"])(i, "onSessionSettleRequest()");
            if (a) throw new Error(a.message);
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isExpired"])(r)) {
                const { message: l } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("EXPIRED", "onSessionSettleRequest()");
                throw new Error(l);
            }
        }), c(this, "isValidUpdate", async (e)=>{
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidParams"])(e)) {
                const { message: a } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("MISSING_OR_INVALID", `update() params: ${e}`);
                throw new Error(a);
            }
            const { topic: t, namespaces: s } = e;
            this.checkRecentlyDeleted(t), await this.isValidSessionTopic(t);
            const i = this.client.session.get(t), r = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidNamespaces"])(s, "update()");
            if (r) throw new Error(r.message);
            const o = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isConformingNamespaces"])(i.requiredNamespaces, s, "update()");
            if (o) throw new Error(o.message);
        }), c(this, "isValidExtend", async (e)=>{
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidParams"])(e)) {
                const { message: s } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("MISSING_OR_INVALID", `extend() params: ${e}`);
                throw new Error(s);
            }
            const { topic: t } = e;
            this.checkRecentlyDeleted(t), await this.isValidSessionTopic(t);
        }), c(this, "isValidRequest", async (e)=>{
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidParams"])(e)) {
                const { message: a } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("MISSING_OR_INVALID", `request() params: ${e}`);
                throw new Error(a);
            }
            const { topic: t, request: s, chainId: i, expiry: r } = e;
            this.checkRecentlyDeleted(t), await this.isValidSessionTopic(t);
            const { namespaces: o } = this.client.session.get(t);
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidNamespacesChainId"])(o, i)) {
                const { message: a } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("MISSING_OR_INVALID", `request() chainId: ${i}`);
                throw new Error(a);
            }
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidRequest"])(s)) {
                const { message: a } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("MISSING_OR_INVALID", `request() ${JSON.stringify(s)}`);
                throw new Error(a);
            }
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidNamespacesRequest"])(o, i, s.method)) {
                const { message: a } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("MISSING_OR_INVALID", `request() method: ${s.method}`);
                throw new Error(a);
            }
            if (r && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidRequestExpiry"])(r, _e)) {
                const { message: a } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("MISSING_OR_INVALID", `request() expiry: ${r}. Expiry must be a number (in seconds) between ${_e.min} and ${_e.max}`);
                throw new Error(a);
            }
        }), c(this, "isValidRespond", async (e)=>{
            var t;
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidParams"])(e)) {
                const { message: r } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("MISSING_OR_INVALID", `respond() params: ${e}`);
                throw new Error(r);
            }
            const { topic: s, response: i } = e;
            try {
                await this.isValidSessionTopic(s);
            } catch (r) {
                throw (t = e?.response) != null && t.id && this.cleanupAfterResponse(e), r;
            }
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidResponse"])(i)) {
                const { message: r } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("MISSING_OR_INVALID", `respond() response: ${JSON.stringify(i)}`);
                throw new Error(r);
            }
        }), c(this, "isValidPing", async (e)=>{
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidParams"])(e)) {
                const { message: s } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("MISSING_OR_INVALID", `ping() params: ${e}`);
                throw new Error(s);
            }
            const { topic: t } = e;
            await this.isValidSessionOrPairingTopic(t);
        }), c(this, "isValidEmit", async (e)=>{
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidParams"])(e)) {
                const { message: o } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("MISSING_OR_INVALID", `emit() params: ${e}`);
                throw new Error(o);
            }
            const { topic: t, event: s, chainId: i } = e;
            await this.isValidSessionTopic(t);
            const { namespaces: r } = this.client.session.get(t);
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidNamespacesChainId"])(r, i)) {
                const { message: o } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("MISSING_OR_INVALID", `emit() chainId: ${i}`);
                throw new Error(o);
            }
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidEvent"])(s)) {
                const { message: o } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("MISSING_OR_INVALID", `emit() event: ${JSON.stringify(s)}`);
                throw new Error(o);
            }
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidNamespacesEvent"])(r, i, s.name)) {
                const { message: o } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("MISSING_OR_INVALID", `emit() event: ${JSON.stringify(s)}`);
                throw new Error(o);
            }
        }), c(this, "isValidDisconnect", async (e)=>{
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidParams"])(e)) {
                const { message: s } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("MISSING_OR_INVALID", `disconnect() params: ${e}`);
                throw new Error(s);
            }
            const { topic: t } = e;
            await this.isValidSessionOrPairingTopic(t);
        }), c(this, "isValidAuthenticate", (e)=>{
            const { chains: t, uri: s, domain: i, nonce: r } = e;
            if (!Array.isArray(t) || t.length === 0) throw new Error("chains is required and must be a non-empty array");
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidString"])(s, !1)) throw new Error("uri is required parameter");
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidString"])(i, !1)) throw new Error("domain is required parameter");
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidString"])(r, !1)) throw new Error("nonce is required parameter");
            if ([
                ...new Set(t.map((a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseChainId"])(a).namespace))
            ].length > 1) throw new Error("Multi-namespace requests are not supported. Please request single namespace only.");
            const { namespace: o } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseChainId"])(t[0]);
            if (o !== "eip155") throw new Error("Only eip155 namespace is supported for authenticated sessions. Please use .connect() for non-eip155 chains.");
        }), c(this, "getVerifyContext", async (e)=>{
            const { attestationId: t, hash: s, encryptedId: i, metadata: r, transportType: o } = e, a = {
                verified: {
                    verifyUrl: r.verifyUrl || __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["VERIFY_SERVER"],
                    validation: "UNKNOWN",
                    origin: r.url || ""
                }
            };
            try {
                if (o === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TRANSPORT_TYPES"].link_mode) {
                    const p = this.getAppLinkIfEnabled(r, o);
                    return a.verified.validation = p && new URL(p).origin === new URL(r.url).origin ? "VALID" : "INVALID", a;
                }
                const l = await this.client.core.verify.resolve({
                    attestationId: t,
                    hash: s,
                    encryptedId: i,
                    verifyUrl: r.verifyUrl
                });
                l && (a.verified.origin = l.origin, a.verified.isScam = l.isScam, a.verified.validation = l.origin === new URL(r.url).origin ? "VALID" : "INVALID");
            } catch (l) {
                this.client.logger.warn(l);
            }
            return this.client.logger.debug(`Verify context: ${JSON.stringify(a)}`), a;
        }), c(this, "validateSessionProps", (e, t)=>{
            Object.values(e).forEach((s, i)=>{
                if (s == null) {
                    const { message: r } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("MISSING_OR_INVALID", `${t} must contain an existing value for each key. Received: ${s} for key ${Object.keys(e)[i]}`);
                    throw new Error(r);
                }
            });
        }), c(this, "getPendingAuthRequest", (e)=>{
            const t = this.client.auth.requests.get(e);
            return typeof t == "object" ? t : void 0;
        }), c(this, "addToRecentlyDeleted", (e, t)=>{
            if (this.recentlyDeletedMap.set(e, t), this.recentlyDeletedMap.size >= this.recentlyDeletedLimit) {
                let s = 0;
                const i = this.recentlyDeletedLimit / 2;
                for (const r of this.recentlyDeletedMap.keys()){
                    if (s++ >= i) break;
                    this.recentlyDeletedMap.delete(r);
                }
            }
        }), c(this, "checkRecentlyDeleted", (e)=>{
            const t = this.recentlyDeletedMap.get(e);
            if (t) {
                const { message: s } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("MISSING_OR_INVALID", `Record was recently deleted - ${t}: ${e}`);
                throw new Error(s);
            }
        }), c(this, "isLinkModeEnabled", (e, t)=>{
            var s, i, r, o, a, l, p, h, u;
            return !e || t !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TRANSPORT_TYPES"].link_mode ? !1 : ((i = (s = this.client.metadata) == null ? void 0 : s.redirect) == null ? void 0 : i.linkMode) === !0 && ((o = (r = this.client.metadata) == null ? void 0 : r.redirect) == null ? void 0 : o.universal) !== void 0 && ((l = (a = this.client.metadata) == null ? void 0 : a.redirect) == null ? void 0 : l.universal) !== "" && ((p = e?.redirect) == null ? void 0 : p.universal) !== void 0 && ((h = e?.redirect) == null ? void 0 : h.universal) !== "" && ((u = e?.redirect) == null ? void 0 : u.linkMode) === !0 && this.client.core.linkModeSupportedApps.includes(e.redirect.universal) && typeof (global == null ? void 0 : global.Linking) < "u";
        }), c(this, "getAppLinkIfEnabled", (e, t)=>{
            var s;
            return this.isLinkModeEnabled(e, t) ? (s = e?.redirect) == null ? void 0 : s.universal : void 0;
        }), c(this, "handleLinkModeMessage", ({ url: e })=>{
            if (!e || !e.includes("wc_ev") || !e.includes("topic")) return;
            const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getSearchParamFromURL"])(e, "topic") || "", s = decodeURIComponent((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getSearchParamFromURL"])(e, "wc_ev") || ""), i = this.client.session.keys.includes(t);
            i && this.client.session.update(t, {
                transportType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TRANSPORT_TYPES"].link_mode
            }), this.client.core.dispatchEnvelope({
                topic: t,
                message: s,
                sessionExists: i
            });
        }), c(this, "registerLinkModeListeners", async ()=>{
            var e;
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isTestRun"])() || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isReactNative"])() && (e = this.client.metadata.redirect) != null && e.linkMode) {
                const t = global == null ? void 0 : global.Linking;
                if (typeof t < "u") {
                    t.addEventListener("url", this.handleLinkModeMessage, this.client.name);
                    const s = await t.getInitialURL();
                    s && setTimeout(()=>{
                        this.handleLinkModeMessage({
                            url: s
                        });
                    }, 50);
                }
            }
        }), c(this, "shouldSetTVF", (e, t)=>{
            if (!t || e !== "wc_sessionRequest") return !1;
            const { request: s } = t;
            return Object.keys(Ke).includes(s.method);
        }), c(this, "getTVFParams", (e, t, s)=>{
            var i, r;
            try {
                const o = t.request.method, a = this.extractTxHashesFromResult(o, s);
                return b(v({
                    correlationId: e,
                    rpcMethods: [
                        o
                    ],
                    chainId: t.chainId
                }, this.isValidContractData(t.request.params) && {
                    contractAddresses: [
                        (r = (i = t.request.params) == null ? void 0 : i[0]) == null ? void 0 : r.to
                    ]
                }), {
                    txHashes: a
                });
            } catch (o) {
                this.client.logger.warn("Error getting TVF params", o);
            }
            return {};
        }), c(this, "isValidContractData", (e)=>{
            var t;
            if (!e) return !1;
            try {
                const s = e?.data || ((t = e?.[0]) == null ? void 0 : t.data);
                if (!s.startsWith("0x")) return !1;
                const i = s.slice(2);
                return /^[0-9a-fA-F]*$/.test(i) ? i.length % 2 === 0 : !1;
            } catch  {}
            return !1;
        }), c(this, "extractTxHashesFromResult", (e, t)=>{
            try {
                const s = Ke[e];
                if (typeof t == "string") return [
                    t
                ];
                const i = t[s.key];
                if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidArray"])(i)) return e === "solana_signAllTransactions" ? i.map((r)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extractSolanaTransactionId"])(r)) : i;
                if (typeof i == "string") return [
                    i
                ];
            } catch (s) {
                this.client.logger.warn("Error extracting tx hashes from result", s);
            }
            return [];
        });
    }
    async processPendingMessageEvents() {
        try {
            const n = this.client.session.keys, e = this.client.core.relayer.messages.getWithoutAck(n);
            for (const [t, s] of Object.entries(e))for (const i of s)try {
                await this.onProviderMessageEvent({
                    topic: t,
                    message: i,
                    publishedAt: Date.now()
                });
            } catch  {
                this.client.logger.warn(`Error processing pending message event for topic: ${t}, message: ${i}`);
            }
        } catch (n) {
            this.client.logger.warn("processPendingMessageEvents failed", n);
        }
    }
    isInitialized() {
        if (!this.initialized) {
            const { message: n } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("NOT_INITIALIZED", this.name);
            throw new Error(n);
        }
    }
    async confirmOnlineStateOrThrow() {
        await this.client.core.relayer.confirmOnlineStateOrThrow();
    }
    registerRelayerEvents() {
        this.client.core.relayer.on(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RELAYER_EVENTS"].message, (n)=>{
            this.onProviderMessageEvent(n);
        });
    }
    async onRelayMessage(n) {
        const { topic: e, message: t, attestation: s, transportType: i } = n, { publicKey: r } = this.client.auth.authKeys.keys.includes(ce) ? this.client.auth.authKeys.get(ce) : {
            responseTopic: void 0,
            publicKey: void 0
        };
        try {
            const o = await this.client.core.crypto.decode(e, t, {
                receiverPublicKey: r,
                encoding: i === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TRANSPORT_TYPES"].link_mode ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BASE64URL"] : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BASE64"]
            });
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$validators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isJsonRpcRequest"])(o) ? (this.client.core.history.set(e, o), await this.onRelayEventRequest({
                topic: e,
                payload: o,
                attestation: s,
                transportType: i,
                encryptedId: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["hashMessage"])(t)
            })) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$validators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isJsonRpcResponse"])(o) ? (await this.client.core.history.resolve(o), await this.onRelayEventResponse({
                topic: e,
                payload: o,
                transportType: i
            }), this.client.core.history.delete(e, o.id)) : await this.onRelayEventUnknownPayload({
                topic: e,
                payload: o,
                transportType: i
            }), await this.client.core.relayer.messages.ack(e, t);
        } catch (o) {
            this.client.logger.error(o);
        }
    }
    registerExpirerEvents() {
        this.client.core.expirer.on(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EXPIRER_EVENTS"].expired, async (n)=>{
            const { topic: e, id: t } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseExpirerTarget"])(n.target);
            if (t && this.client.pendingRequest.keys.includes(t)) return await this.deletePendingSessionRequest(t, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("EXPIRED"), !0);
            if (t && this.client.auth.requests.keys.includes(t)) return await this.deletePendingAuthRequest(t, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("EXPIRED"), !0);
            e ? this.client.session.keys.includes(e) && (await this.deleteSession({
                topic: e,
                expirerHasDeleted: !0
            }), this.client.events.emit("session_expire", {
                topic: e
            })) : t && (await this.deleteProposal(t, !0), this.client.events.emit("proposal_expire", {
                id: t
            }));
        });
    }
    registerPairingEvents() {
        this.client.core.pairing.events.on(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PAIRING_EVENTS"].create, (n)=>this.onPairingCreated(n)), this.client.core.pairing.events.on(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PAIRING_EVENTS"].delete, (n)=>{
            this.addToRecentlyDeleted(n.topic, "pairing");
        });
    }
    isValidPairingTopic(n) {
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidString"])(n, !1)) {
            const { message: e } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("MISSING_OR_INVALID", `pairing topic should be a string: ${n}`);
            throw new Error(e);
        }
        if (!this.client.core.pairing.pairings.keys.includes(n)) {
            const { message: e } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("NO_MATCHING_KEY", `pairing topic doesn't exist: ${n}`);
            throw new Error(e);
        }
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isExpired"])(this.client.core.pairing.pairings.get(n).expiry)) {
            const { message: e } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("EXPIRED", `pairing topic: ${n}`);
            throw new Error(e);
        }
    }
    async isValidSessionTopic(n) {
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidString"])(n, !1)) {
            const { message: e } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("MISSING_OR_INVALID", `session topic should be a string: ${n}`);
            throw new Error(e);
        }
        if (this.checkRecentlyDeleted(n), !this.client.session.keys.includes(n)) {
            const { message: e } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("NO_MATCHING_KEY", `session topic doesn't exist: ${n}`);
            throw new Error(e);
        }
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isExpired"])(this.client.session.get(n).expiry)) {
            await this.deleteSession({
                topic: n
            });
            const { message: e } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("EXPIRED", `session topic: ${n}`);
            throw new Error(e);
        }
        if (!this.client.core.crypto.keychain.has(n)) {
            const { message: e } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("MISSING_OR_INVALID", `session topic does not exist in keychain: ${n}`);
            throw await this.deleteSession({
                topic: n
            }), new Error(e);
        }
    }
    async isValidSessionOrPairingTopic(n) {
        if (this.checkRecentlyDeleted(n), this.client.session.keys.includes(n)) await this.isValidSessionTopic(n);
        else if (this.client.core.pairing.pairings.keys.includes(n)) this.isValidPairingTopic(n);
        else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidString"])(n, !1)) {
            const { message: e } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("NO_MATCHING_KEY", `session or pairing topic doesn't exist: ${n}`);
            throw new Error(e);
        } else {
            const { message: e } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("MISSING_OR_INVALID", `session or pairing topic should be a string: ${n}`);
            throw new Error(e);
        }
    }
    async isValidProposalId(n) {
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidId"])(n)) {
            const { message: e } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("MISSING_OR_INVALID", `proposal id should be a number: ${n}`);
            throw new Error(e);
        }
        if (!this.client.proposal.keys.includes(n)) {
            const { message: e } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("NO_MATCHING_KEY", `proposal id doesn't exist: ${n}`);
            throw new Error(e);
        }
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isExpired"])(this.client.proposal.get(n).expiryTimestamp)) {
            await this.deleteProposal(n);
            const { message: e } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("EXPIRED", `proposal id: ${n}`);
            throw new Error(e);
        }
    }
}
class Os extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Store"] {
    constructor(n, e){
        super(n, e, pt, we), this.core = n, this.logger = e;
    }
}
class St extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Store"] {
    constructor(n, e){
        super(n, e, ht, we), this.core = n, this.logger = e;
    }
}
class bs extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Store"] {
    constructor(n, e){
        super(n, e, ut, we, (t)=>t.id), this.core = n, this.logger = e;
    }
}
class As extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Store"] {
    constructor(n, e){
        super(n, e, mt, ae, ()=>ce), this.core = n, this.logger = e;
    }
}
class xs extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Store"] {
    constructor(n, e){
        super(n, e, _t, ae), this.core = n, this.logger = e;
    }
}
class Cs extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Store"] {
    constructor(n, e){
        super(n, e, Et, ae, (t)=>t.id), this.core = n, this.logger = e;
    }
}
var Vs = Object.defineProperty, Ds = (S, n, e)=>n in S ? Vs(S, n, {
        enumerable: !0,
        configurable: !0,
        writable: !0,
        value: e
    }) : S[n] = e, Ge = (S, n, e)=>Ds(S, typeof n != "symbol" ? n + "" : n, e);
class Ls {
    constructor(n, e){
        this.core = n, this.logger = e, Ge(this, "authKeys"), Ge(this, "pairingTopics"), Ge(this, "requests"), this.authKeys = new As(this.core, this.logger), this.pairingTopics = new xs(this.core, this.logger), this.requests = new Cs(this.core, this.logger);
    }
    async init() {
        await this.authKeys.init(), await this.pairingTopics.init(), await this.requests.init();
    }
}
var ks = Object.defineProperty, Ms = (S, n, e)=>n in S ? ks(S, n, {
        enumerable: !0,
        configurable: !0,
        writable: !0,
        value: e
    }) : S[n] = e, E = (S, n, e)=>Ms(S, typeof n != "symbol" ? n + "" : n, e);
class Ee extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$types$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ISignClient"] {
    constructor(n){
        super(n), E(this, "protocol", De), E(this, "version", Le), E(this, "name", me.name), E(this, "metadata"), E(this, "core"), E(this, "logger"), E(this, "events", new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$events$2f$events$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EventEmitter"]), E(this, "engine"), E(this, "session"), E(this, "proposal"), E(this, "pendingRequest"), E(this, "auth"), E(this, "signConfig"), E(this, "on", (t, s)=>this.events.on(t, s)), E(this, "once", (t, s)=>this.events.once(t, s)), E(this, "off", (t, s)=>this.events.off(t, s)), E(this, "removeListener", (t, s)=>this.events.removeListener(t, s)), E(this, "removeAllListeners", (t)=>this.events.removeAllListeners(t)), E(this, "connect", async (t)=>{
            try {
                return await this.engine.connect(t);
            } catch (s) {
                throw this.logger.error(s.message), s;
            }
        }), E(this, "pair", async (t)=>{
            try {
                return await this.engine.pair(t);
            } catch (s) {
                throw this.logger.error(s.message), s;
            }
        }), E(this, "approve", async (t)=>{
            try {
                return await this.engine.approve(t);
            } catch (s) {
                throw this.logger.error(s.message), s;
            }
        }), E(this, "reject", async (t)=>{
            try {
                return await this.engine.reject(t);
            } catch (s) {
                throw this.logger.error(s.message), s;
            }
        }), E(this, "update", async (t)=>{
            try {
                return await this.engine.update(t);
            } catch (s) {
                throw this.logger.error(s.message), s;
            }
        }), E(this, "extend", async (t)=>{
            try {
                return await this.engine.extend(t);
            } catch (s) {
                throw this.logger.error(s.message), s;
            }
        }), E(this, "request", async (t)=>{
            try {
                return await this.engine.request(t);
            } catch (s) {
                throw this.logger.error(s.message), s;
            }
        }), E(this, "respond", async (t)=>{
            try {
                return await this.engine.respond(t);
            } catch (s) {
                throw this.logger.error(s.message), s;
            }
        }), E(this, "ping", async (t)=>{
            try {
                return await this.engine.ping(t);
            } catch (s) {
                throw this.logger.error(s.message), s;
            }
        }), E(this, "emit", async (t)=>{
            try {
                return await this.engine.emit(t);
            } catch (s) {
                throw this.logger.error(s.message), s;
            }
        }), E(this, "disconnect", async (t)=>{
            try {
                return await this.engine.disconnect(t);
            } catch (s) {
                throw this.logger.error(s.message), s;
            }
        }), E(this, "find", (t)=>{
            try {
                return this.engine.find(t);
            } catch (s) {
                throw this.logger.error(s.message), s;
            }
        }), E(this, "getPendingSessionRequests", ()=>{
            try {
                return this.engine.getPendingSessionRequests();
            } catch (t) {
                throw this.logger.error(t.message), t;
            }
        }), E(this, "authenticate", async (t, s)=>{
            try {
                return await this.engine.authenticate(t, s);
            } catch (i) {
                throw this.logger.error(i.message), i;
            }
        }), E(this, "formatAuthMessage", (t)=>{
            try {
                return this.engine.formatAuthMessage(t);
            } catch (s) {
                throw this.logger.error(s.message), s;
            }
        }), E(this, "approveSessionAuthenticate", async (t)=>{
            try {
                return await this.engine.approveSessionAuthenticate(t);
            } catch (s) {
                throw this.logger.error(s.message), s;
            }
        }), E(this, "rejectSessionAuthenticate", async (t)=>{
            try {
                return await this.engine.rejectSessionAuthenticate(t);
            } catch (s) {
                throw this.logger.error(s.message), s;
            }
        }), this.name = n?.name || me.name, this.metadata = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["populateAppMetadata"])(n?.metadata), this.signConfig = n?.signConfig;
        const e = typeof n?.logger < "u" && typeof n?.logger != "string" ? n.logger : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$pino$2f$browser$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__pino$3e$__["pino"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$logger$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getDefaultLoggerOptions"])({
            level: n?.logger || me.logger
        }));
        this.core = n?.core || new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Core"](n), this.logger = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$logger$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["generateChildLogger"])(e, this.name), this.session = new St(this.core, this.logger), this.proposal = new Os(this.core, this.logger), this.pendingRequest = new bs(this.core, this.logger), this.engine = new Ns(this), this.auth = new Ls(this.core, this.logger);
    }
    static async init(n) {
        const e = new Ee(n);
        return await e.initialize(), e;
    }
    get context() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$logger$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getLoggerContext"])(this.logger);
    }
    get pairing() {
        return this.core.pairing.pairings;
    }
    async initialize() {
        this.logger.trace("Initialized");
        try {
            await this.core.start(), await this.session.init(), await this.proposal.init(), await this.pendingRequest.init(), await this.auth.init(), await this.engine.init(), this.logger.info("SignClient Initialization Success"), setTimeout(()=>{
                this.engine.processRelayMessageCache();
            }, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$time$2f$dist$2f$cjs$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toMiliseconds"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$time$2f$dist$2f$cjs$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ONE_SECOND"]));
        } catch (n) {
            throw this.logger.info("SignClient Initialization Failure"), this.logger.error(n.message), n;
        }
    }
}
const $s = St, Ks = Ee;
;
 //# sourceMappingURL=index.es.js.map
}}),
"[project]/node_modules/@reown/appkit-controllers/node_modules/@walletconnect/sign-client/dist/index.es.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "AUTH_CONTEXT": (()=>wt),
    "AUTH_KEYS_CONTEXT": (()=>mt),
    "AUTH_PAIRING_TOPIC_CONTEXT": (()=>_t),
    "AUTH_PROTOCOL": (()=>yt),
    "AUTH_PUBLIC_KEY_NAME": (()=>ce),
    "AUTH_REQUEST_CONTEXT": (()=>Et),
    "AUTH_STORAGE_PREFIX": (()=>ae),
    "AUTH_VERSION": (()=>Rs),
    "ENGINE_CONTEXT": (()=>dt),
    "ENGINE_QUEUE_STATES": (()=>$),
    "ENGINE_RPC_OPTS": (()=>N),
    "HISTORY_CONTEXT": (()=>Es),
    "HISTORY_EVENTS": (()=>_s),
    "HISTORY_STORAGE_VERSION": (()=>fs),
    "METHODS_TO_VERIFY": (()=>gt),
    "PROPOSAL_CONTEXT": (()=>pt),
    "PROPOSAL_EXPIRY": (()=>Ss),
    "PROPOSAL_EXPIRY_MESSAGE": (()=>$e),
    "REQUEST_CONTEXT": (()=>ut),
    "SESSION_CONTEXT": (()=>ht),
    "SESSION_EXPIRY": (()=>J),
    "SESSION_REQUEST_EXPIRY_BOUNDARIES": (()=>_e),
    "SIGN_CLIENT_CONTEXT": (()=>ke),
    "SIGN_CLIENT_DEFAULT": (()=>me),
    "SIGN_CLIENT_EVENTS": (()=>ws),
    "SIGN_CLIENT_PROTOCOL": (()=>De),
    "SIGN_CLIENT_STORAGE_OPTIONS": (()=>ms),
    "SIGN_CLIENT_STORAGE_PREFIX": (()=>we),
    "SIGN_CLIENT_VERSION": (()=>Le),
    "SessionStore": (()=>$s),
    "SignClient": (()=>Ks),
    "TVF_METHODS": (()=>Ke),
    "WALLETCONNECT_DEEPLINK_CHOICE": (()=>Me),
    "default": (()=>Ee)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-controllers/node_modules/@walletconnect/core/dist/index.es.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$logger$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@walletconnect/logger/dist/index.es.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$pino$2f$browser$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__pino$3e$__ = __turbopack_context__.i("[project]/node_modules/pino/browser.js [app-client] (ecmascript) <export default as pino>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$logger$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@walletconnect/logger/dist/index.es.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$types$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-controllers/node_modules/@walletconnect/types/dist/index.es.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$time$2f$dist$2f$cjs$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@walletconnect/time/dist/cjs/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reown/appkit-controllers/node_modules/@walletconnect/utils/dist/index.es.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$events$2f$events$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/events/events.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@walletconnect/jsonrpc-utils/dist/esm/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$validators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@walletconnect/jsonrpc-utils/dist/esm/validators.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@walletconnect/jsonrpc-utils/dist/esm/format.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
const De = "wc", Le = 2, ke = "client", we = `${De}@${Le}:${ke}:`, me = {
    name: ke,
    logger: "error",
    controller: !1,
    relayUrl: "wss://relay.walletconnect.org"
}, ws = {
    session_proposal: "session_proposal",
    session_update: "session_update",
    session_extend: "session_extend",
    session_ping: "session_ping",
    session_delete: "session_delete",
    session_expire: "session_expire",
    session_request: "session_request",
    session_request_sent: "session_request_sent",
    session_event: "session_event",
    proposal_expire: "proposal_expire",
    session_authenticate: "session_authenticate",
    session_request_expire: "session_request_expire",
    session_connect: "session_connect"
}, ms = {
    database: ":memory:"
}, Me = "WALLETCONNECT_DEEPLINK_CHOICE", _s = {
    created: "history_created",
    updated: "history_updated",
    deleted: "history_deleted",
    sync: "history_sync"
}, Es = "history", fs = "0.3", pt = "proposal", Ss = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$time$2f$dist$2f$cjs$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["THIRTY_DAYS"], $e = "Proposal expired", ht = "session", J = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$time$2f$dist$2f$cjs$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SEVEN_DAYS"], dt = "engine", N = {
    wc_sessionPropose: {
        req: {
            ttl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$time$2f$dist$2f$cjs$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FIVE_MINUTES"],
            prompt: !0,
            tag: 1100
        },
        res: {
            ttl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$time$2f$dist$2f$cjs$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FIVE_MINUTES"],
            prompt: !1,
            tag: 1101
        },
        reject: {
            ttl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$time$2f$dist$2f$cjs$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FIVE_MINUTES"],
            prompt: !1,
            tag: 1120
        },
        autoReject: {
            ttl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$time$2f$dist$2f$cjs$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FIVE_MINUTES"],
            prompt: !1,
            tag: 1121
        }
    },
    wc_sessionSettle: {
        req: {
            ttl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$time$2f$dist$2f$cjs$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FIVE_MINUTES"],
            prompt: !1,
            tag: 1102
        },
        res: {
            ttl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$time$2f$dist$2f$cjs$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FIVE_MINUTES"],
            prompt: !1,
            tag: 1103
        }
    },
    wc_sessionUpdate: {
        req: {
            ttl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$time$2f$dist$2f$cjs$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ONE_DAY"],
            prompt: !1,
            tag: 1104
        },
        res: {
            ttl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$time$2f$dist$2f$cjs$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ONE_DAY"],
            prompt: !1,
            tag: 1105
        }
    },
    wc_sessionExtend: {
        req: {
            ttl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$time$2f$dist$2f$cjs$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ONE_DAY"],
            prompt: !1,
            tag: 1106
        },
        res: {
            ttl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$time$2f$dist$2f$cjs$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ONE_DAY"],
            prompt: !1,
            tag: 1107
        }
    },
    wc_sessionRequest: {
        req: {
            ttl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$time$2f$dist$2f$cjs$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FIVE_MINUTES"],
            prompt: !0,
            tag: 1108
        },
        res: {
            ttl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$time$2f$dist$2f$cjs$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FIVE_MINUTES"],
            prompt: !1,
            tag: 1109
        }
    },
    wc_sessionEvent: {
        req: {
            ttl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$time$2f$dist$2f$cjs$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FIVE_MINUTES"],
            prompt: !0,
            tag: 1110
        },
        res: {
            ttl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$time$2f$dist$2f$cjs$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FIVE_MINUTES"],
            prompt: !1,
            tag: 1111
        }
    },
    wc_sessionDelete: {
        req: {
            ttl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$time$2f$dist$2f$cjs$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ONE_DAY"],
            prompt: !1,
            tag: 1112
        },
        res: {
            ttl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$time$2f$dist$2f$cjs$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ONE_DAY"],
            prompt: !1,
            tag: 1113
        }
    },
    wc_sessionPing: {
        req: {
            ttl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$time$2f$dist$2f$cjs$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ONE_DAY"],
            prompt: !1,
            tag: 1114
        },
        res: {
            ttl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$time$2f$dist$2f$cjs$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ONE_DAY"],
            prompt: !1,
            tag: 1115
        }
    },
    wc_sessionAuthenticate: {
        req: {
            ttl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$time$2f$dist$2f$cjs$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ONE_HOUR"],
            prompt: !0,
            tag: 1116
        },
        res: {
            ttl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$time$2f$dist$2f$cjs$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ONE_HOUR"],
            prompt: !1,
            tag: 1117
        },
        reject: {
            ttl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$time$2f$dist$2f$cjs$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FIVE_MINUTES"],
            prompt: !1,
            tag: 1118
        },
        autoReject: {
            ttl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$time$2f$dist$2f$cjs$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FIVE_MINUTES"],
            prompt: !1,
            tag: 1119
        }
    }
}, _e = {
    min: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$time$2f$dist$2f$cjs$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FIVE_MINUTES"],
    max: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$time$2f$dist$2f$cjs$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SEVEN_DAYS"]
}, $ = {
    idle: "IDLE",
    active: "ACTIVE"
}, Ke = {
    eth_sendTransaction: {
        key: ""
    },
    eth_sendRawTransaction: {
        key: ""
    },
    wallet_sendCalls: {
        key: ""
    },
    solana_signTransaction: {
        key: "signature"
    },
    solana_signAllTransactions: {
        key: "transactions"
    },
    solana_signAndSendTransaction: {
        key: "signature"
    }
}, ut = "request", gt = [
    "wc_sessionPropose",
    "wc_sessionRequest",
    "wc_authRequest",
    "wc_sessionAuthenticate"
], yt = "wc", Rs = 1.5, wt = "auth", mt = "authKeys", _t = "pairingTopics", Et = "requests", ae = `${yt}@${1.5}:${wt}:`, ce = `${ae}:PUB_KEY`;
var vs = Object.defineProperty, Is = Object.defineProperties, Ts = Object.getOwnPropertyDescriptors, ft = Object.getOwnPropertySymbols, qs = Object.prototype.hasOwnProperty, Ps = Object.prototype.propertyIsEnumerable, Ue = (S, n, e)=>n in S ? vs(S, n, {
        enumerable: !0,
        configurable: !0,
        writable: !0,
        value: e
    }) : S[n] = e, v = (S, n)=>{
    for(var e in n || (n = {}))qs.call(n, e) && Ue(S, e, n[e]);
    if (ft) for (var e of ft(n))Ps.call(n, e) && Ue(S, e, n[e]);
    return S;
}, b = (S, n)=>Is(S, Ts(n)), c = (S, n, e)=>Ue(S, typeof n != "symbol" ? n + "" : n, e);
class Ns extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$types$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["IEngine"] {
    constructor(n){
        super(n), c(this, "name", dt), c(this, "events", new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$events$2f$events$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]), c(this, "initialized", !1), c(this, "requestQueue", {
            state: $.idle,
            queue: []
        }), c(this, "sessionRequestQueue", {
            state: $.idle,
            queue: []
        }), c(this, "requestQueueDelay", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$time$2f$dist$2f$cjs$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ONE_SECOND"]), c(this, "expectedPairingMethodMap", new Map), c(this, "recentlyDeletedMap", new Map), c(this, "recentlyDeletedLimit", 200), c(this, "relayMessageCache", []), c(this, "pendingSessions", new Map), c(this, "init", async ()=>{
            this.initialized || (await this.cleanup(), this.registerRelayerEvents(), this.registerExpirerEvents(), this.registerPairingEvents(), await this.registerLinkModeListeners(), this.client.core.pairing.register({
                methods: Object.keys(N)
            }), this.initialized = !0, setTimeout(async ()=>{
                await this.processPendingMessageEvents(), this.sessionRequestQueue.queue = this.getPendingSessionRequests(), this.processSessionRequestQueue();
            }, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$time$2f$dist$2f$cjs$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toMiliseconds"])(this.requestQueueDelay)));
        }), c(this, "connect", async (e)=>{
            this.isInitialized(), await this.confirmOnlineStateOrThrow();
            const t = b(v({}, e), {
                requiredNamespaces: e.requiredNamespaces || {},
                optionalNamespaces: e.optionalNamespaces || {}
            });
            await this.isValidConnect(t), t.optionalNamespaces = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mergeRequiredAndOptionalNamespaces"])(t.requiredNamespaces, t.optionalNamespaces), t.requiredNamespaces = {};
            const { pairingTopic: s, requiredNamespaces: i, optionalNamespaces: r, sessionProperties: o, scopedProperties: a, relays: l } = t;
            let p = s, h, u = !1;
            try {
                if (p) {
                    const T = this.client.core.pairing.pairings.get(p);
                    this.client.logger.warn("connect() with existing pairing topic is deprecated and will be removed in the next major release."), u = T.active;
                }
            } catch (T) {
                throw this.client.logger.error(`connect() -> pairing.get(${p}) failed`), T;
            }
            if (!p || !u) {
                const { topic: T, uri: K } = await this.client.core.pairing.create();
                p = T, h = K;
            }
            if (!p) {
                const { message: T } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("NO_MATCHING_KEY", `connect() pairing topic: ${p}`);
                throw new Error(T);
            }
            const d = await this.client.core.crypto.generateKeyPair(), w = N.wc_sessionPropose.req.ttl || __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$time$2f$dist$2f$cjs$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FIVE_MINUTES"], m = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["calcExpiry"])(w), f = b(v(v({
                requiredNamespaces: i,
                optionalNamespaces: r,
                relays: l ?? [
                    {
                        protocol: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RELAYER_DEFAULT_PROTOCOL"]
                    }
                ],
                proposer: {
                    publicKey: d,
                    metadata: this.client.metadata
                },
                expiryTimestamp: m,
                pairingTopic: p
            }, o && {
                sessionProperties: o
            }), a && {
                scopedProperties: a
            }), {
                id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["payloadId"])()
            }), _ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["engineEvent"])("session_connect", f.id), { reject: g, resolve: A, done: D } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createDelayedPromise"])(w, $e), I = ({ id: T })=>{
                T === f.id && (this.client.events.off("proposal_expire", I), this.pendingSessions.delete(f.id), this.events.emit(_, {
                    error: {
                        message: $e,
                        code: 0
                    }
                }));
            };
            return this.client.events.on("proposal_expire", I), this.events.once(_, ({ error: T, session: K })=>{
                this.client.events.off("proposal_expire", I), T ? g(T) : K && A(K);
            }), await this.sendRequest({
                topic: p,
                method: "wc_sessionPropose",
                params: f,
                throwOnFailedPublish: !0,
                clientRpcId: f.id
            }), await this.setProposal(f.id, f), {
                uri: h,
                approval: D
            };
        }), c(this, "pair", async (e)=>{
            this.isInitialized(), await this.confirmOnlineStateOrThrow();
            try {
                return await this.client.core.pairing.pair(e);
            } catch (t) {
                throw this.client.logger.error("pair() failed"), t;
            }
        }), c(this, "approve", async (e)=>{
            var t, s, i;
            const r = this.client.core.eventClient.createEvent({
                properties: {
                    topic: (t = e?.id) == null ? void 0 : t.toString(),
                    trace: [
                        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_CLIENT_SESSION_TRACES"].session_approve_started
                    ]
                }
            });
            try {
                this.isInitialized(), await this.confirmOnlineStateOrThrow();
            } catch (q) {
                throw r.setError(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_CLIENT_SESSION_ERRORS"].no_internet_connection), q;
            }
            try {
                await this.isValidProposalId(e?.id);
            } catch (q) {
                throw this.client.logger.error(`approve() -> proposal.get(${e?.id}) failed`), r.setError(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_CLIENT_SESSION_ERRORS"].proposal_not_found), q;
            }
            try {
                await this.isValidApprove(e);
            } catch (q) {
                throw this.client.logger.error("approve() -> isValidApprove() failed"), r.setError(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_CLIENT_SESSION_ERRORS"].session_approve_namespace_validation_failure), q;
            }
            const { id: o, relayProtocol: a, namespaces: l, sessionProperties: p, scopedProperties: h, sessionConfig: u } = e, d = this.client.proposal.get(o);
            this.client.core.eventClient.deleteEvent({
                eventId: r.eventId
            });
            const { pairingTopic: w, proposer: m, requiredNamespaces: f, optionalNamespaces: _ } = d;
            let g = (s = this.client.core.eventClient) == null ? void 0 : s.getEvent({
                topic: w
            });
            g || (g = (i = this.client.core.eventClient) == null ? void 0 : i.createEvent({
                type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_CLIENT_SESSION_TRACES"].session_approve_started,
                properties: {
                    topic: w,
                    trace: [
                        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_CLIENT_SESSION_TRACES"].session_approve_started,
                        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_CLIENT_SESSION_TRACES"].session_namespaces_validation_success
                    ]
                }
            }));
            const A = await this.client.core.crypto.generateKeyPair(), D = m.publicKey, I = await this.client.core.crypto.generateSharedKey(A, D), T = v(v(v({
                relay: {
                    protocol: a ?? "irn"
                },
                namespaces: l,
                controller: {
                    publicKey: A,
                    metadata: this.client.metadata
                },
                expiry: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["calcExpiry"])(J)
            }, p && {
                sessionProperties: p
            }), h && {
                scopedProperties: h
            }), u && {
                sessionConfig: u
            }), K = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TRANSPORT_TYPES"].relay;
            g.addTrace(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_CLIENT_SESSION_TRACES"].subscribing_session_topic);
            try {
                await this.client.core.relayer.subscribe(I, {
                    transportType: K
                });
            } catch (q) {
                throw g.setError(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_CLIENT_SESSION_ERRORS"].subscribe_session_topic_failure), q;
            }
            g.addTrace(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_CLIENT_SESSION_TRACES"].subscribe_session_topic_success);
            const fe = b(v({}, T), {
                topic: I,
                requiredNamespaces: f,
                optionalNamespaces: _,
                pairingTopic: w,
                acknowledged: !1,
                self: T.controller,
                peer: {
                    publicKey: m.publicKey,
                    metadata: m.metadata
                },
                controller: A,
                transportType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TRANSPORT_TYPES"].relay
            });
            await this.client.session.set(I, fe), g.addTrace(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_CLIENT_SESSION_TRACES"].store_session);
            try {
                g.addTrace(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_CLIENT_SESSION_TRACES"].publishing_session_settle), await this.sendRequest({
                    topic: I,
                    method: "wc_sessionSettle",
                    params: T,
                    throwOnFailedPublish: !0
                }).catch((q)=>{
                    throw g?.setError(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_CLIENT_SESSION_ERRORS"].session_settle_publish_failure), q;
                }), g.addTrace(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_CLIENT_SESSION_TRACES"].session_settle_publish_success), g.addTrace(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_CLIENT_SESSION_TRACES"].publishing_session_approve), await this.sendResult({
                    id: o,
                    topic: w,
                    result: {
                        relay: {
                            protocol: a ?? "irn"
                        },
                        responderPublicKey: A
                    },
                    throwOnFailedPublish: !0
                }).catch((q)=>{
                    throw g?.setError(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_CLIENT_SESSION_ERRORS"].session_approve_publish_failure), q;
                }), g.addTrace(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_CLIENT_SESSION_TRACES"].session_approve_publish_success);
            } catch (q) {
                throw this.client.logger.error(q), this.client.session.delete(I, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getSdkError"])("USER_DISCONNECTED")), await this.client.core.relayer.unsubscribe(I), q;
            }
            return this.client.core.eventClient.deleteEvent({
                eventId: g.eventId
            }), await this.client.core.pairing.updateMetadata({
                topic: w,
                metadata: m.metadata
            }), await this.client.proposal.delete(o, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getSdkError"])("USER_DISCONNECTED")), await this.client.core.pairing.activate({
                topic: w
            }), await this.setExpiry(I, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["calcExpiry"])(J)), {
                topic: I,
                acknowledged: ()=>Promise.resolve(this.client.session.get(I))
            };
        }), c(this, "reject", async (e)=>{
            this.isInitialized(), await this.confirmOnlineStateOrThrow();
            try {
                await this.isValidReject(e);
            } catch (r) {
                throw this.client.logger.error("reject() -> isValidReject() failed"), r;
            }
            const { id: t, reason: s } = e;
            let i;
            try {
                i = this.client.proposal.get(t).pairingTopic;
            } catch (r) {
                throw this.client.logger.error(`reject() -> proposal.get(${t}) failed`), r;
            }
            i && (await this.sendError({
                id: t,
                topic: i,
                error: s,
                rpcOpts: N.wc_sessionPropose.reject
            }), await this.client.proposal.delete(t, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getSdkError"])("USER_DISCONNECTED")));
        }), c(this, "update", async (e)=>{
            this.isInitialized(), await this.confirmOnlineStateOrThrow();
            try {
                await this.isValidUpdate(e);
            } catch (h) {
                throw this.client.logger.error("update() -> isValidUpdate() failed"), h;
            }
            const { topic: t, namespaces: s } = e, { done: i, resolve: r, reject: o } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createDelayedPromise"])(), a = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["payloadId"])(), l = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getBigIntRpcId"])().toString(), p = this.client.session.get(t).namespaces;
            return this.events.once((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["engineEvent"])("session_update", a), ({ error: h })=>{
                h ? o(h) : r();
            }), await this.client.session.update(t, {
                namespaces: s
            }), await this.sendRequest({
                topic: t,
                method: "wc_sessionUpdate",
                params: {
                    namespaces: s
                },
                throwOnFailedPublish: !0,
                clientRpcId: a,
                relayRpcId: l
            }).catch((h)=>{
                this.client.logger.error(h), this.client.session.update(t, {
                    namespaces: p
                }), o(h);
            }), {
                acknowledged: i
            };
        }), c(this, "extend", async (e)=>{
            this.isInitialized(), await this.confirmOnlineStateOrThrow();
            try {
                await this.isValidExtend(e);
            } catch (a) {
                throw this.client.logger.error("extend() -> isValidExtend() failed"), a;
            }
            const { topic: t } = e, s = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["payloadId"])(), { done: i, resolve: r, reject: o } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createDelayedPromise"])();
            return this.events.once((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["engineEvent"])("session_extend", s), ({ error: a })=>{
                a ? o(a) : r();
            }), await this.setExpiry(t, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["calcExpiry"])(J)), this.sendRequest({
                topic: t,
                method: "wc_sessionExtend",
                params: {},
                clientRpcId: s,
                throwOnFailedPublish: !0
            }).catch((a)=>{
                o(a);
            }), {
                acknowledged: i
            };
        }), c(this, "request", async (e)=>{
            this.isInitialized();
            try {
                await this.isValidRequest(e);
            } catch (_) {
                throw this.client.logger.error("request() -> isValidRequest() failed"), _;
            }
            const { chainId: t, request: s, topic: i, expiry: r = N.wc_sessionRequest.req.ttl } = e, o = this.client.session.get(i);
            o?.transportType === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TRANSPORT_TYPES"].relay && await this.confirmOnlineStateOrThrow();
            const a = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["payloadId"])(), l = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getBigIntRpcId"])().toString(), { done: p, resolve: h, reject: u } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createDelayedPromise"])(r, "Request expired. Please try again.");
            this.events.once((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["engineEvent"])("session_request", a), ({ error: _, result: g })=>{
                _ ? u(_) : h(g);
            });
            const d = "wc_sessionRequest", w = this.getAppLinkIfEnabled(o.peer.metadata, o.transportType);
            if (w) return await this.sendRequest({
                clientRpcId: a,
                relayRpcId: l,
                topic: i,
                method: d,
                params: {
                    request: b(v({}, s), {
                        expiryTimestamp: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["calcExpiry"])(r)
                    }),
                    chainId: t
                },
                expiry: r,
                throwOnFailedPublish: !0,
                appLink: w
            }).catch((_)=>u(_)), this.client.events.emit("session_request_sent", {
                topic: i,
                request: s,
                chainId: t,
                id: a
            }), await p();
            const m = {
                request: b(v({}, s), {
                    expiryTimestamp: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["calcExpiry"])(r)
                }),
                chainId: t
            }, f = this.shouldSetTVF(d, m);
            return await Promise.all([
                new Promise(async (_)=>{
                    await this.sendRequest(v({
                        clientRpcId: a,
                        relayRpcId: l,
                        topic: i,
                        method: d,
                        params: m,
                        expiry: r,
                        throwOnFailedPublish: !0
                    }, f && {
                        tvf: this.getTVFParams(a, m)
                    })).catch((g)=>u(g)), this.client.events.emit("session_request_sent", {
                        topic: i,
                        request: s,
                        chainId: t,
                        id: a
                    }), _();
                }),
                new Promise(async (_)=>{
                    var g;
                    if (!((g = o.sessionConfig) != null && g.disableDeepLink)) {
                        const A = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getDeepLink"])(this.client.core.storage, Me);
                        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["handleDeeplinkRedirect"])({
                            id: a,
                            topic: i,
                            wcDeepLink: A
                        });
                    }
                    _();
                }),
                p()
            ]).then((_)=>_[2]);
        }), c(this, "respond", async (e)=>{
            this.isInitialized(), await this.isValidRespond(e);
            const { topic: t, response: s } = e, { id: i } = s, r = this.client.session.get(t);
            r.transportType === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TRANSPORT_TYPES"].relay && await this.confirmOnlineStateOrThrow();
            const o = this.getAppLinkIfEnabled(r.peer.metadata, r.transportType);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$validators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isJsonRpcResult"])(s) ? await this.sendResult({
                id: i,
                topic: t,
                result: s.result,
                throwOnFailedPublish: !0,
                appLink: o
            }) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$validators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isJsonRpcError"])(s) && await this.sendError({
                id: i,
                topic: t,
                error: s.error,
                appLink: o
            }), this.cleanupAfterResponse(e);
        }), c(this, "ping", async (e)=>{
            this.isInitialized(), await this.confirmOnlineStateOrThrow();
            try {
                await this.isValidPing(e);
            } catch (s) {
                throw this.client.logger.error("ping() -> isValidPing() failed"), s;
            }
            const { topic: t } = e;
            if (this.client.session.keys.includes(t)) {
                const s = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["payloadId"])(), i = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getBigIntRpcId"])().toString(), { done: r, resolve: o, reject: a } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createDelayedPromise"])();
                this.events.once((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["engineEvent"])("session_ping", s), ({ error: l })=>{
                    l ? a(l) : o();
                }), await Promise.all([
                    this.sendRequest({
                        topic: t,
                        method: "wc_sessionPing",
                        params: {},
                        throwOnFailedPublish: !0,
                        clientRpcId: s,
                        relayRpcId: i
                    }),
                    r()
                ]);
            } else this.client.core.pairing.pairings.keys.includes(t) && (this.client.logger.warn("ping() on pairing topic is deprecated and will be removed in the next major release."), await this.client.core.pairing.ping({
                topic: t
            }));
        }), c(this, "emit", async (e)=>{
            this.isInitialized(), await this.confirmOnlineStateOrThrow(), await this.isValidEmit(e);
            const { topic: t, event: s, chainId: i } = e, r = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getBigIntRpcId"])().toString(), o = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["payloadId"])();
            await this.sendRequest({
                topic: t,
                method: "wc_sessionEvent",
                params: {
                    event: s,
                    chainId: i
                },
                throwOnFailedPublish: !0,
                relayRpcId: r,
                clientRpcId: o
            });
        }), c(this, "disconnect", async (e)=>{
            this.isInitialized(), await this.confirmOnlineStateOrThrow(), await this.isValidDisconnect(e);
            const { topic: t } = e;
            if (this.client.session.keys.includes(t)) await this.sendRequest({
                topic: t,
                method: "wc_sessionDelete",
                params: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getSdkError"])("USER_DISCONNECTED"),
                throwOnFailedPublish: !0
            }), await this.deleteSession({
                topic: t,
                emitEvent: !1
            });
            else if (this.client.core.pairing.pairings.keys.includes(t)) await this.client.core.pairing.disconnect({
                topic: t
            });
            else {
                const { message: s } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("MISMATCHED_TOPIC", `Session or pairing topic not found: ${t}`);
                throw new Error(s);
            }
        }), c(this, "find", (e)=>(this.isInitialized(), this.client.session.getAll().filter((t)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isSessionCompatible"])(t, e)))), c(this, "getPendingSessionRequests", ()=>this.client.pendingRequest.getAll()), c(this, "authenticate", async (e, t)=>{
            var s;
            this.isInitialized(), this.isValidAuthenticate(e);
            const i = t && this.client.core.linkModeSupportedApps.includes(t) && ((s = this.client.metadata.redirect) == null ? void 0 : s.linkMode), r = i ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TRANSPORT_TYPES"].link_mode : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TRANSPORT_TYPES"].relay;
            r === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TRANSPORT_TYPES"].relay && await this.confirmOnlineStateOrThrow();
            const { chains: o, statement: a = "", uri: l, domain: p, nonce: h, type: u, exp: d, nbf: w, methods: m = [], expiry: f } = e, _ = [
                ...e.resources || []
            ], { topic: g, uri: A } = await this.client.core.pairing.create({
                methods: [
                    "wc_sessionAuthenticate"
                ],
                transportType: r
            });
            this.client.logger.info({
                message: "Generated new pairing",
                pairing: {
                    topic: g,
                    uri: A
                }
            });
            const D = await this.client.core.crypto.generateKeyPair(), I = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["hashKey"])(D);
            if (await Promise.all([
                this.client.auth.authKeys.set(ce, {
                    responseTopic: I,
                    publicKey: D
                }),
                this.client.auth.pairingTopics.set(I, {
                    topic: I,
                    pairingTopic: g
                })
            ]), await this.client.core.relayer.subscribe(I, {
                transportType: r
            }), this.client.logger.info(`sending request to new pairing topic: ${g}`), m.length > 0) {
                const { namespace: x } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseChainId"])(o[0]);
                let L = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createEncodedRecap"])(x, "request", m);
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getRecapFromResources"])(_) && (L = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mergeEncodedRecaps"])(L, _.pop())), _.push(L);
            }
            const T = f && f > N.wc_sessionAuthenticate.req.ttl ? f : N.wc_sessionAuthenticate.req.ttl, K = {
                authPayload: {
                    type: u ?? "caip122",
                    chains: o,
                    statement: a,
                    aud: l,
                    domain: p,
                    version: "1",
                    nonce: h,
                    iat: new Date().toISOString(),
                    exp: d,
                    nbf: w,
                    resources: _
                },
                requester: {
                    publicKey: D,
                    metadata: this.client.metadata
                },
                expiryTimestamp: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["calcExpiry"])(T)
            }, fe = {
                eip155: {
                    chains: o,
                    methods: [
                        ...new Set([
                            "personal_sign",
                            ...m
                        ])
                    ],
                    events: [
                        "chainChanged",
                        "accountsChanged"
                    ]
                }
            }, q = {
                requiredNamespaces: {},
                optionalNamespaces: fe,
                relays: [
                    {
                        protocol: "irn"
                    }
                ],
                pairingTopic: g,
                proposer: {
                    publicKey: D,
                    metadata: this.client.metadata
                },
                expiryTimestamp: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["calcExpiry"])(N.wc_sessionPropose.req.ttl),
                id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["payloadId"])()
            }, { done: Rt, resolve: je, reject: Se } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createDelayedPromise"])(T, "Request expired"), te = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["payloadId"])(), le = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["engineEvent"])("session_connect", q.id), Re = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["engineEvent"])("session_request", te), pe = async ({ error: x, session: L })=>{
                this.events.off(Re, ve), x ? Se(x) : L && je({
                    session: L
                });
            }, ve = async (x)=>{
                var L, Fe, Qe;
                if (await this.deletePendingAuthRequest(te, {
                    message: "fulfilled",
                    code: 0
                }), x.error) {
                    const ie = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getSdkError"])("WC_METHOD_UNSUPPORTED", "wc_sessionAuthenticate");
                    return x.error.code === ie.code ? void 0 : (this.events.off(le, pe), Se(x.error.message));
                }
                await this.deleteProposal(q.id), this.events.off(le, pe);
                const { cacaos: He, responder: Q } = x.result, Te = [], ze = [];
                for (const ie of He){
                    await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["validateSignedCacao"])({
                        cacao: ie,
                        projectId: this.client.core.projectId
                    }) || (this.client.logger.error(ie, "Signature verification failed"), Se((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getSdkError"])("SESSION_SETTLEMENT_FAILED", "Signature verification failed")));
                    const { p: qe } = ie, Pe = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getRecapFromResources"])(qe.resources), Ye = [
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getNamespacedDidChainId"])(qe.iss)
                    ], vt = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getDidAddress"])(qe.iss);
                    if (Pe) {
                        const Ne = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getMethodsFromRecap"])(Pe), It = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getChainsFromRecap"])(Pe);
                        Te.push(...Ne), Ye.push(...It);
                    }
                    for (const Ne of Ye)ze.push(`${Ne}:${vt}`);
                }
                const se = await this.client.core.crypto.generateSharedKey(D, Q.publicKey);
                let he;
                Te.length > 0 && (he = {
                    topic: se,
                    acknowledged: !0,
                    self: {
                        publicKey: D,
                        metadata: this.client.metadata
                    },
                    peer: Q,
                    controller: Q.publicKey,
                    expiry: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["calcExpiry"])(J),
                    requiredNamespaces: {},
                    optionalNamespaces: {},
                    relay: {
                        protocol: "irn"
                    },
                    pairingTopic: g,
                    namespaces: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["buildNamespacesFromAuth"])([
                        ...new Set(Te)
                    ], [
                        ...new Set(ze)
                    ]),
                    transportType: r
                }, await this.client.core.relayer.subscribe(se, {
                    transportType: r
                }), await this.client.session.set(se, he), g && await this.client.core.pairing.updateMetadata({
                    topic: g,
                    metadata: Q.metadata
                }), he = this.client.session.get(se)), (L = this.client.metadata.redirect) != null && L.linkMode && (Fe = Q.metadata.redirect) != null && Fe.linkMode && (Qe = Q.metadata.redirect) != null && Qe.universal && t && (this.client.core.addLinkModeSupportedApp(Q.metadata.redirect.universal), this.client.session.update(se, {
                    transportType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TRANSPORT_TYPES"].link_mode
                })), je({
                    auths: He,
                    session: he
                });
            };
            this.events.once(le, pe), this.events.once(Re, ve);
            let Ie;
            try {
                if (i) {
                    const x = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatJsonRpcRequest"])("wc_sessionAuthenticate", K, te);
                    this.client.core.history.set(g, x);
                    const L = await this.client.core.crypto.encode("", x, {
                        type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TYPE_2"],
                        encoding: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BASE64URL"]
                    });
                    Ie = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getLinkModeURL"])(t, g, L);
                } else await Promise.all([
                    this.sendRequest({
                        topic: g,
                        method: "wc_sessionAuthenticate",
                        params: K,
                        expiry: e.expiry,
                        throwOnFailedPublish: !0,
                        clientRpcId: te
                    }),
                    this.sendRequest({
                        topic: g,
                        method: "wc_sessionPropose",
                        params: q,
                        expiry: N.wc_sessionPropose.req.ttl,
                        throwOnFailedPublish: !0,
                        clientRpcId: q.id
                    })
                ]);
            } catch (x) {
                throw this.events.off(le, pe), this.events.off(Re, ve), x;
            }
            return await this.setProposal(q.id, q), await this.setAuthRequest(te, {
                request: b(v({}, K), {
                    verifyContext: {}
                }),
                pairingTopic: g,
                transportType: r
            }), {
                uri: Ie ?? A,
                response: Rt
            };
        }), c(this, "approveSessionAuthenticate", async (e)=>{
            const { id: t, auths: s } = e, i = this.client.core.eventClient.createEvent({
                properties: {
                    topic: t.toString(),
                    trace: [
                        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_CLIENT_AUTHENTICATE_TRACES"].authenticated_session_approve_started
                    ]
                }
            });
            try {
                this.isInitialized();
            } catch (f) {
                throw i.setError(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_CLIENT_AUTHENTICATE_ERRORS"].no_internet_connection), f;
            }
            const r = this.getPendingAuthRequest(t);
            if (!r) throw i.setError(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_CLIENT_AUTHENTICATE_ERRORS"].authenticated_session_pending_request_not_found), new Error(`Could not find pending auth request with id ${t}`);
            const o = r.transportType || __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TRANSPORT_TYPES"].relay;
            o === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TRANSPORT_TYPES"].relay && await this.confirmOnlineStateOrThrow();
            const a = r.requester.publicKey, l = await this.client.core.crypto.generateKeyPair(), p = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["hashKey"])(a), h = {
                type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TYPE_1"],
                receiverPublicKey: a,
                senderPublicKey: l
            }, u = [], d = [];
            for (const f of s){
                if (!await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["validateSignedCacao"])({
                    cacao: f,
                    projectId: this.client.core.projectId
                })) {
                    i.setError(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_CLIENT_AUTHENTICATE_ERRORS"].invalid_cacao);
                    const I = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getSdkError"])("SESSION_SETTLEMENT_FAILED", "Signature verification failed");
                    throw await this.sendError({
                        id: t,
                        topic: p,
                        error: I,
                        encodeOpts: h
                    }), new Error(I.message);
                }
                i.addTrace(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_CLIENT_AUTHENTICATE_TRACES"].cacaos_verified);
                const { p: _ } = f, g = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getRecapFromResources"])(_.resources), A = [
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getNamespacedDidChainId"])(_.iss)
                ], D = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getDidAddress"])(_.iss);
                if (g) {
                    const I = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getMethodsFromRecap"])(g), T = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getChainsFromRecap"])(g);
                    u.push(...I), A.push(...T);
                }
                for (const I of A)d.push(`${I}:${D}`);
            }
            const w = await this.client.core.crypto.generateSharedKey(l, a);
            i.addTrace(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_CLIENT_AUTHENTICATE_TRACES"].create_authenticated_session_topic);
            let m;
            if (u?.length > 0) {
                m = {
                    topic: w,
                    acknowledged: !0,
                    self: {
                        publicKey: l,
                        metadata: this.client.metadata
                    },
                    peer: {
                        publicKey: a,
                        metadata: r.requester.metadata
                    },
                    controller: a,
                    expiry: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["calcExpiry"])(J),
                    authentication: s,
                    requiredNamespaces: {},
                    optionalNamespaces: {},
                    relay: {
                        protocol: "irn"
                    },
                    pairingTopic: r.pairingTopic,
                    namespaces: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["buildNamespacesFromAuth"])([
                        ...new Set(u)
                    ], [
                        ...new Set(d)
                    ]),
                    transportType: o
                }, i.addTrace(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_CLIENT_AUTHENTICATE_TRACES"].subscribing_authenticated_session_topic);
                try {
                    await this.client.core.relayer.subscribe(w, {
                        transportType: o
                    });
                } catch (f) {
                    throw i.setError(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_CLIENT_AUTHENTICATE_ERRORS"].subscribe_authenticated_session_topic_failure), f;
                }
                i.addTrace(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_CLIENT_AUTHENTICATE_TRACES"].subscribe_authenticated_session_topic_success), await this.client.session.set(w, m), i.addTrace(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_CLIENT_AUTHENTICATE_TRACES"].store_authenticated_session), await this.client.core.pairing.updateMetadata({
                    topic: r.pairingTopic,
                    metadata: r.requester.metadata
                });
            }
            i.addTrace(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_CLIENT_AUTHENTICATE_TRACES"].publishing_authenticated_session_approve);
            try {
                await this.sendResult({
                    topic: p,
                    id: t,
                    result: {
                        cacaos: s,
                        responder: {
                            publicKey: l,
                            metadata: this.client.metadata
                        }
                    },
                    encodeOpts: h,
                    throwOnFailedPublish: !0,
                    appLink: this.getAppLinkIfEnabled(r.requester.metadata, o)
                });
            } catch (f) {
                throw i.setError(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_CLIENT_AUTHENTICATE_ERRORS"].authenticated_session_approve_publish_failure), f;
            }
            return await this.client.auth.requests.delete(t, {
                message: "fulfilled",
                code: 0
            }), await this.client.core.pairing.activate({
                topic: r.pairingTopic
            }), this.client.core.eventClient.deleteEvent({
                eventId: i.eventId
            }), {
                session: m
            };
        }), c(this, "rejectSessionAuthenticate", async (e)=>{
            this.isInitialized();
            const { id: t, reason: s } = e, i = this.getPendingAuthRequest(t);
            if (!i) throw new Error(`Could not find pending auth request with id ${t}`);
            i.transportType === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TRANSPORT_TYPES"].relay && await this.confirmOnlineStateOrThrow();
            const r = i.requester.publicKey, o = await this.client.core.crypto.generateKeyPair(), a = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["hashKey"])(r), l = {
                type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TYPE_1"],
                receiverPublicKey: r,
                senderPublicKey: o
            };
            await this.sendError({
                id: t,
                topic: a,
                error: s,
                encodeOpts: l,
                rpcOpts: N.wc_sessionAuthenticate.reject,
                appLink: this.getAppLinkIfEnabled(i.requester.metadata, i.transportType)
            }), await this.client.auth.requests.delete(t, {
                message: "rejected",
                code: 0
            }), await this.client.proposal.delete(t, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getSdkError"])("USER_DISCONNECTED"));
        }), c(this, "formatAuthMessage", (e)=>{
            this.isInitialized();
            const { request: t, iss: s } = e;
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatMessage"])(t, s);
        }), c(this, "processRelayMessageCache", ()=>{
            setTimeout(async ()=>{
                if (this.relayMessageCache.length !== 0) for(; this.relayMessageCache.length > 0;)try {
                    const e = this.relayMessageCache.shift();
                    e && await this.onRelayMessage(e);
                } catch (e) {
                    this.client.logger.error(e);
                }
            }, 50);
        }), c(this, "cleanupDuplicatePairings", async (e)=>{
            if (e.pairingTopic) try {
                const t = this.client.core.pairing.pairings.get(e.pairingTopic), s = this.client.core.pairing.pairings.getAll().filter((i)=>{
                    var r, o;
                    return ((r = i.peerMetadata) == null ? void 0 : r.url) && ((o = i.peerMetadata) == null ? void 0 : o.url) === e.peer.metadata.url && i.topic && i.topic !== t.topic;
                });
                if (s.length === 0) return;
                this.client.logger.info(`Cleaning up ${s.length} duplicate pairing(s)`), await Promise.all(s.map((i)=>this.client.core.pairing.disconnect({
                        topic: i.topic
                    }))), this.client.logger.info("Duplicate pairings clean up finished");
            } catch (t) {
                this.client.logger.error(t);
            }
        }), c(this, "deleteSession", async (e)=>{
            var t;
            const { topic: s, expirerHasDeleted: i = !1, emitEvent: r = !0, id: o = 0 } = e, { self: a } = this.client.session.get(s);
            await this.client.core.relayer.unsubscribe(s), await this.client.session.delete(s, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getSdkError"])("USER_DISCONNECTED")), this.addToRecentlyDeleted(s, "session"), this.client.core.crypto.keychain.has(a.publicKey) && await this.client.core.crypto.deleteKeyPair(a.publicKey), this.client.core.crypto.keychain.has(s) && await this.client.core.crypto.deleteSymKey(s), i || this.client.core.expirer.del(s), this.client.core.storage.removeItem(Me).catch((l)=>this.client.logger.warn(l)), this.getPendingSessionRequests().forEach((l)=>{
                l.topic === s && this.deletePendingSessionRequest(l.id, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getSdkError"])("USER_DISCONNECTED"));
            }), s === ((t = this.sessionRequestQueue.queue[0]) == null ? void 0 : t.topic) && (this.sessionRequestQueue.state = $.idle), r && this.client.events.emit("session_delete", {
                id: o,
                topic: s
            });
        }), c(this, "deleteProposal", async (e, t)=>{
            if (t) try {
                const s = this.client.proposal.get(e), i = this.client.core.eventClient.getEvent({
                    topic: s.pairingTopic
                });
                i?.setError(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_CLIENT_SESSION_ERRORS"].proposal_expired);
            } catch  {}
            await Promise.all([
                this.client.proposal.delete(e, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getSdkError"])("USER_DISCONNECTED")),
                t ? Promise.resolve() : this.client.core.expirer.del(e)
            ]), this.addToRecentlyDeleted(e, "proposal");
        }), c(this, "deletePendingSessionRequest", async (e, t, s = !1)=>{
            await Promise.all([
                this.client.pendingRequest.delete(e, t),
                s ? Promise.resolve() : this.client.core.expirer.del(e)
            ]), this.addToRecentlyDeleted(e, "request"), this.sessionRequestQueue.queue = this.sessionRequestQueue.queue.filter((i)=>i.id !== e), s && (this.sessionRequestQueue.state = $.idle, this.client.events.emit("session_request_expire", {
                id: e
            }));
        }), c(this, "deletePendingAuthRequest", async (e, t, s = !1)=>{
            await Promise.all([
                this.client.auth.requests.delete(e, t),
                s ? Promise.resolve() : this.client.core.expirer.del(e)
            ]);
        }), c(this, "setExpiry", async (e, t)=>{
            this.client.session.keys.includes(e) && (this.client.core.expirer.set(e, t), await this.client.session.update(e, {
                expiry: t
            }));
        }), c(this, "setProposal", async (e, t)=>{
            this.client.core.expirer.set(e, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["calcExpiry"])(N.wc_sessionPropose.req.ttl)), await this.client.proposal.set(e, t);
        }), c(this, "setAuthRequest", async (e, t)=>{
            const { request: s, pairingTopic: i, transportType: r = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TRANSPORT_TYPES"].relay } = t;
            this.client.core.expirer.set(e, s.expiryTimestamp), await this.client.auth.requests.set(e, {
                authPayload: s.authPayload,
                requester: s.requester,
                expiryTimestamp: s.expiryTimestamp,
                id: e,
                pairingTopic: i,
                verifyContext: s.verifyContext,
                transportType: r
            });
        }), c(this, "setPendingSessionRequest", async (e)=>{
            const { id: t, topic: s, params: i, verifyContext: r } = e, o = i.request.expiryTimestamp || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["calcExpiry"])(N.wc_sessionRequest.req.ttl);
            this.client.core.expirer.set(t, o), await this.client.pendingRequest.set(t, {
                id: t,
                topic: s,
                params: i,
                verifyContext: r
            });
        }), c(this, "sendRequest", async (e)=>{
            const { topic: t, method: s, params: i, expiry: r, relayRpcId: o, clientRpcId: a, throwOnFailedPublish: l, appLink: p, tvf: h } = e, u = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatJsonRpcRequest"])(s, i, a);
            let d;
            const w = !!p;
            try {
                const _ = w ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BASE64URL"] : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BASE64"];
                d = await this.client.core.crypto.encode(t, u, {
                    encoding: _
                });
            } catch (_) {
                throw await this.cleanup(), this.client.logger.error(`sendRequest() -> core.crypto.encode() for topic ${t} failed`), _;
            }
            let m;
            if (gt.includes(s)) {
                const _ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["hashMessage"])(JSON.stringify(u)), g = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["hashMessage"])(d);
                m = await this.client.core.verify.register({
                    id: g,
                    decryptedId: _
                });
            }
            const f = N[s].req;
            if (f.attestation = m, r && (f.ttl = r), o && (f.id = o), this.client.core.history.set(t, u), w) {
                const _ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getLinkModeURL"])(p, t, d);
                await global.Linking.openURL(_, this.client.name);
            } else {
                const _ = N[s].req;
                r && (_.ttl = r), o && (_.id = o), _.tvf = b(v({}, h), {
                    correlationId: u.id
                }), l ? (_.internal = b(v({}, _.internal), {
                    throwOnFailedPublish: !0
                }), await this.client.core.relayer.publish(t, d, _)) : this.client.core.relayer.publish(t, d, _).catch((g)=>this.client.logger.error(g));
            }
            return u.id;
        }), c(this, "sendResult", async (e)=>{
            const { id: t, topic: s, result: i, throwOnFailedPublish: r, encodeOpts: o, appLink: a } = e, l = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatJsonRpcResult"])(t, i);
            let p;
            const h = a && typeof (global == null ? void 0 : global.Linking) < "u";
            try {
                const w = h ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BASE64URL"] : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BASE64"];
                p = await this.client.core.crypto.encode(s, l, b(v({}, o || {}), {
                    encoding: w
                }));
            } catch (w) {
                throw await this.cleanup(), this.client.logger.error(`sendResult() -> core.crypto.encode() for topic ${s} failed`), w;
            }
            let u, d;
            try {
                u = await this.client.core.history.get(s, t);
                const w = u.request;
                try {
                    this.shouldSetTVF(w.method, w.params) && (d = this.getTVFParams(t, w.params, i));
                } catch (m) {
                    this.client.logger.warn("sendResult() -> getTVFParams() failed", m);
                }
            } catch (w) {
                throw this.client.logger.error(`sendResult() -> history.get(${s}, ${t}) failed`), w;
            }
            if (h) {
                const w = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getLinkModeURL"])(a, s, p);
                await global.Linking.openURL(w, this.client.name);
            } else {
                const w = u.request.method, m = N[w].res;
                m.tvf = b(v({}, d), {
                    correlationId: t
                }), r ? (m.internal = b(v({}, m.internal), {
                    throwOnFailedPublish: !0
                }), await this.client.core.relayer.publish(s, p, m)) : this.client.core.relayer.publish(s, p, m).catch((f)=>this.client.logger.error(f));
            }
            await this.client.core.history.resolve(l);
        }), c(this, "sendError", async (e)=>{
            const { id: t, topic: s, error: i, encodeOpts: r, rpcOpts: o, appLink: a } = e, l = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatJsonRpcError"])(t, i);
            let p;
            const h = a && typeof (global == null ? void 0 : global.Linking) < "u";
            try {
                const d = h ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BASE64URL"] : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BASE64"];
                p = await this.client.core.crypto.encode(s, l, b(v({}, r || {}), {
                    encoding: d
                }));
            } catch (d) {
                throw await this.cleanup(), this.client.logger.error(`sendError() -> core.crypto.encode() for topic ${s} failed`), d;
            }
            let u;
            try {
                u = await this.client.core.history.get(s, t);
            } catch (d) {
                throw this.client.logger.error(`sendError() -> history.get(${s}, ${t}) failed`), d;
            }
            if (h) {
                const d = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getLinkModeURL"])(a, s, p);
                await global.Linking.openURL(d, this.client.name);
            } else {
                const d = u.request.method, w = o || N[d].res;
                this.client.core.relayer.publish(s, p, w);
            }
            await this.client.core.history.resolve(l);
        }), c(this, "cleanup", async ()=>{
            const e = [], t = [];
            this.client.session.getAll().forEach((s)=>{
                let i = !1;
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isExpired"])(s.expiry) && (i = !0), this.client.core.crypto.keychain.has(s.topic) || (i = !0), i && e.push(s.topic);
            }), this.client.proposal.getAll().forEach((s)=>{
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isExpired"])(s.expiryTimestamp) && t.push(s.id);
            }), await Promise.all([
                ...e.map((s)=>this.deleteSession({
                        topic: s
                    })),
                ...t.map((s)=>this.deleteProposal(s))
            ]);
        }), c(this, "onProviderMessageEvent", async (e)=>{
            !this.initialized || this.relayMessageCache.length > 0 ? this.relayMessageCache.push(e) : await this.onRelayMessage(e);
        }), c(this, "onRelayEventRequest", async (e)=>{
            this.requestQueue.queue.push(e), await this.processRequestsQueue();
        }), c(this, "processRequestsQueue", async ()=>{
            if (this.requestQueue.state === $.active) {
                this.client.logger.info("Request queue already active, skipping...");
                return;
            }
            for(this.client.logger.info(`Request queue starting with ${this.requestQueue.queue.length} requests`); this.requestQueue.queue.length > 0;){
                this.requestQueue.state = $.active;
                const e = this.requestQueue.queue.shift();
                if (e) try {
                    await this.processRequest(e);
                } catch (t) {
                    this.client.logger.warn(t);
                }
            }
            this.requestQueue.state = $.idle;
        }), c(this, "processRequest", async (e)=>{
            const { topic: t, payload: s, attestation: i, transportType: r, encryptedId: o } = e, a = s.method;
            if (!this.shouldIgnorePairingRequest({
                topic: t,
                requestMethod: a
            })) switch(a){
                case "wc_sessionPropose":
                    return await this.onSessionProposeRequest({
                        topic: t,
                        payload: s,
                        attestation: i,
                        encryptedId: o
                    });
                case "wc_sessionSettle":
                    return await this.onSessionSettleRequest(t, s);
                case "wc_sessionUpdate":
                    return await this.onSessionUpdateRequest(t, s);
                case "wc_sessionExtend":
                    return await this.onSessionExtendRequest(t, s);
                case "wc_sessionPing":
                    return await this.onSessionPingRequest(t, s);
                case "wc_sessionDelete":
                    return await this.onSessionDeleteRequest(t, s);
                case "wc_sessionRequest":
                    return await this.onSessionRequest({
                        topic: t,
                        payload: s,
                        attestation: i,
                        encryptedId: o,
                        transportType: r
                    });
                case "wc_sessionEvent":
                    return await this.onSessionEventRequest(t, s);
                case "wc_sessionAuthenticate":
                    return await this.onSessionAuthenticateRequest({
                        topic: t,
                        payload: s,
                        attestation: i,
                        encryptedId: o,
                        transportType: r
                    });
                default:
                    return this.client.logger.info(`Unsupported request method ${a}`);
            }
        }), c(this, "onRelayEventResponse", async (e)=>{
            const { topic: t, payload: s, transportType: i } = e, r = (await this.client.core.history.get(t, s.id)).request.method;
            switch(r){
                case "wc_sessionPropose":
                    return this.onSessionProposeResponse(t, s, i);
                case "wc_sessionSettle":
                    return this.onSessionSettleResponse(t, s);
                case "wc_sessionUpdate":
                    return this.onSessionUpdateResponse(t, s);
                case "wc_sessionExtend":
                    return this.onSessionExtendResponse(t, s);
                case "wc_sessionPing":
                    return this.onSessionPingResponse(t, s);
                case "wc_sessionRequest":
                    return this.onSessionRequestResponse(t, s);
                case "wc_sessionAuthenticate":
                    return this.onSessionAuthenticateResponse(t, s);
                default:
                    return this.client.logger.info(`Unsupported response method ${r}`);
            }
        }), c(this, "onRelayEventUnknownPayload", (e)=>{
            const { topic: t } = e, { message: s } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("MISSING_OR_INVALID", `Decoded payload on topic ${t} is not identifiable as a JSON-RPC request or a response.`);
            throw new Error(s);
        }), c(this, "shouldIgnorePairingRequest", (e)=>{
            const { topic: t, requestMethod: s } = e, i = this.expectedPairingMethodMap.get(t);
            return !i || i.includes(s) ? !1 : !!(i.includes("wc_sessionAuthenticate") && this.client.events.listenerCount("session_authenticate") > 0);
        }), c(this, "onSessionProposeRequest", async (e)=>{
            const { topic: t, payload: s, attestation: i, encryptedId: r } = e, { params: o, id: a } = s;
            try {
                const l = this.client.core.eventClient.getEvent({
                    topic: t
                });
                this.client.events.listenerCount("session_proposal") === 0 && (console.warn("No listener for session_proposal event"), l?.setError(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_CLIENT_PAIRING_ERRORS"].proposal_listener_not_found)), this.isValidConnect(v({}, s.params));
                const p = o.expiryTimestamp || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["calcExpiry"])(N.wc_sessionPropose.req.ttl), h = v({
                    id: a,
                    pairingTopic: t,
                    expiryTimestamp: p
                }, o);
                await this.setProposal(a, h);
                const u = await this.getVerifyContext({
                    attestationId: i,
                    hash: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["hashMessage"])(JSON.stringify(s)),
                    encryptedId: r,
                    metadata: h.proposer.metadata
                });
                l?.addTrace(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EVENT_CLIENT_PAIRING_TRACES"].emit_session_proposal), this.client.events.emit("session_proposal", {
                    id: a,
                    params: h,
                    verifyContext: u
                });
            } catch (l) {
                await this.sendError({
                    id: a,
                    topic: t,
                    error: l,
                    rpcOpts: N.wc_sessionPropose.autoReject
                }), this.client.logger.error(l);
            }
        }), c(this, "onSessionProposeResponse", async (e, t, s)=>{
            const { id: i } = t;
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$validators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isJsonRpcResult"])(t)) {
                const { result: r } = t;
                this.client.logger.trace({
                    type: "method",
                    method: "onSessionProposeResponse",
                    result: r
                });
                const o = this.client.proposal.get(i);
                this.client.logger.trace({
                    type: "method",
                    method: "onSessionProposeResponse",
                    proposal: o
                });
                const a = o.proposer.publicKey;
                this.client.logger.trace({
                    type: "method",
                    method: "onSessionProposeResponse",
                    selfPublicKey: a
                });
                const l = r.responderPublicKey;
                this.client.logger.trace({
                    type: "method",
                    method: "onSessionProposeResponse",
                    peerPublicKey: l
                });
                const p = await this.client.core.crypto.generateSharedKey(a, l);
                this.pendingSessions.set(i, {
                    sessionTopic: p,
                    pairingTopic: e,
                    proposalId: i,
                    publicKey: a
                });
                const h = await this.client.core.relayer.subscribe(p, {
                    transportType: s
                });
                this.client.logger.trace({
                    type: "method",
                    method: "onSessionProposeResponse",
                    subscriptionId: h
                }), await this.client.core.pairing.activate({
                    topic: e
                });
            } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$validators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isJsonRpcError"])(t)) {
                await this.client.proposal.delete(i, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getSdkError"])("USER_DISCONNECTED"));
                const r = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["engineEvent"])("session_connect", i);
                if (this.events.listenerCount(r) === 0) throw new Error(`emitting ${r} without any listeners, 954`);
                this.events.emit(r, {
                    error: t.error
                });
            }
        }), c(this, "onSessionSettleRequest", async (e, t)=>{
            const { id: s, params: i } = t;
            try {
                this.isValidSessionSettleRequest(i);
                const { relay: r, controller: o, expiry: a, namespaces: l, sessionProperties: p, scopedProperties: h, sessionConfig: u } = t.params, d = [
                    ...this.pendingSessions.values()
                ].find((f)=>f.sessionTopic === e);
                if (!d) return this.client.logger.error(`Pending session not found for topic ${e}`);
                const w = this.client.proposal.get(d.proposalId), m = b(v(v(v({
                    topic: e,
                    relay: r,
                    expiry: a,
                    namespaces: l,
                    acknowledged: !0,
                    pairingTopic: d.pairingTopic,
                    requiredNamespaces: w.requiredNamespaces,
                    optionalNamespaces: w.optionalNamespaces,
                    controller: o.publicKey,
                    self: {
                        publicKey: d.publicKey,
                        metadata: this.client.metadata
                    },
                    peer: {
                        publicKey: o.publicKey,
                        metadata: o.metadata
                    }
                }, p && {
                    sessionProperties: p
                }), h && {
                    scopedProperties: h
                }), u && {
                    sessionConfig: u
                }), {
                    transportType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TRANSPORT_TYPES"].relay
                });
                await this.client.session.set(m.topic, m), await this.setExpiry(m.topic, m.expiry), await this.client.core.pairing.updateMetadata({
                    topic: d.pairingTopic,
                    metadata: m.peer.metadata
                }), this.client.events.emit("session_connect", {
                    session: m
                }), this.events.emit((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["engineEvent"])("session_connect", d.proposalId), {
                    session: m
                }), this.pendingSessions.delete(d.proposalId), this.deleteProposal(d.proposalId, !1), this.cleanupDuplicatePairings(m), await this.sendResult({
                    id: t.id,
                    topic: e,
                    result: !0,
                    throwOnFailedPublish: !0
                });
            } catch (r) {
                await this.sendError({
                    id: s,
                    topic: e,
                    error: r
                }), this.client.logger.error(r);
            }
        }), c(this, "onSessionSettleResponse", async (e, t)=>{
            const { id: s } = t;
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$validators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isJsonRpcResult"])(t) ? (await this.client.session.update(e, {
                acknowledged: !0
            }), this.events.emit((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["engineEvent"])("session_approve", s), {})) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$validators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isJsonRpcError"])(t) && (await this.client.session.delete(e, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getSdkError"])("USER_DISCONNECTED")), this.events.emit((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["engineEvent"])("session_approve", s), {
                error: t.error
            }));
        }), c(this, "onSessionUpdateRequest", async (e, t)=>{
            const { params: s, id: i } = t;
            try {
                const r = `${e}_session_update`, o = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MemoryStore"].get(r);
                if (o && this.isRequestOutOfSync(o, i)) {
                    this.client.logger.warn(`Discarding out of sync request - ${i}`), this.sendError({
                        id: i,
                        topic: e,
                        error: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getSdkError"])("INVALID_UPDATE_REQUEST")
                    });
                    return;
                }
                this.isValidUpdate(v({
                    topic: e
                }, s));
                try {
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MemoryStore"].set(r, i), await this.client.session.update(e, {
                        namespaces: s.namespaces
                    }), await this.sendResult({
                        id: i,
                        topic: e,
                        result: !0,
                        throwOnFailedPublish: !0
                    });
                } catch (a) {
                    throw __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MemoryStore"].delete(r), a;
                }
                this.client.events.emit("session_update", {
                    id: i,
                    topic: e,
                    params: s
                });
            } catch (r) {
                await this.sendError({
                    id: i,
                    topic: e,
                    error: r
                }), this.client.logger.error(r);
            }
        }), c(this, "isRequestOutOfSync", (e, t)=>t.toString().slice(0, -3) < e.toString().slice(0, -3)), c(this, "onSessionUpdateResponse", (e, t)=>{
            const { id: s } = t, i = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["engineEvent"])("session_update", s);
            if (this.events.listenerCount(i) === 0) throw new Error(`emitting ${i} without any listeners`);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$validators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isJsonRpcResult"])(t) ? this.events.emit((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["engineEvent"])("session_update", s), {}) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$validators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isJsonRpcError"])(t) && this.events.emit((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["engineEvent"])("session_update", s), {
                error: t.error
            });
        }), c(this, "onSessionExtendRequest", async (e, t)=>{
            const { id: s } = t;
            try {
                this.isValidExtend({
                    topic: e
                }), await this.setExpiry(e, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["calcExpiry"])(J)), await this.sendResult({
                    id: s,
                    topic: e,
                    result: !0,
                    throwOnFailedPublish: !0
                }), this.client.events.emit("session_extend", {
                    id: s,
                    topic: e
                });
            } catch (i) {
                await this.sendError({
                    id: s,
                    topic: e,
                    error: i
                }), this.client.logger.error(i);
            }
        }), c(this, "onSessionExtendResponse", (e, t)=>{
            const { id: s } = t, i = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["engineEvent"])("session_extend", s);
            if (this.events.listenerCount(i) === 0) throw new Error(`emitting ${i} without any listeners`);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$validators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isJsonRpcResult"])(t) ? this.events.emit((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["engineEvent"])("session_extend", s), {}) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$validators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isJsonRpcError"])(t) && this.events.emit((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["engineEvent"])("session_extend", s), {
                error: t.error
            });
        }), c(this, "onSessionPingRequest", async (e, t)=>{
            const { id: s } = t;
            try {
                this.isValidPing({
                    topic: e
                }), await this.sendResult({
                    id: s,
                    topic: e,
                    result: !0,
                    throwOnFailedPublish: !0
                }), this.client.events.emit("session_ping", {
                    id: s,
                    topic: e
                });
            } catch (i) {
                await this.sendError({
                    id: s,
                    topic: e,
                    error: i
                }), this.client.logger.error(i);
            }
        }), c(this, "onSessionPingResponse", (e, t)=>{
            const { id: s } = t, i = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["engineEvent"])("session_ping", s);
            setTimeout(()=>{
                if (this.events.listenerCount(i) === 0) throw new Error(`emitting ${i} without any listeners 2176`);
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$validators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isJsonRpcResult"])(t) ? this.events.emit((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["engineEvent"])("session_ping", s), {}) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$validators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isJsonRpcError"])(t) && this.events.emit((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["engineEvent"])("session_ping", s), {
                    error: t.error
                });
            }, 500);
        }), c(this, "onSessionDeleteRequest", async (e, t)=>{
            const { id: s } = t;
            try {
                this.isValidDisconnect({
                    topic: e,
                    reason: t.params
                }), Promise.all([
                    new Promise((i)=>{
                        this.client.core.relayer.once(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RELAYER_EVENTS"].publish, async ()=>{
                            i(await this.deleteSession({
                                topic: e,
                                id: s
                            }));
                        });
                    }),
                    this.sendResult({
                        id: s,
                        topic: e,
                        result: !0,
                        throwOnFailedPublish: !0
                    }),
                    this.cleanupPendingSentRequestsForTopic({
                        topic: e,
                        error: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getSdkError"])("USER_DISCONNECTED")
                    })
                ]).catch((i)=>this.client.logger.error(i));
            } catch (i) {
                this.client.logger.error(i);
            }
        }), c(this, "onSessionRequest", async (e)=>{
            var t, s, i;
            const { topic: r, payload: o, attestation: a, encryptedId: l, transportType: p } = e, { id: h, params: u } = o;
            try {
                await this.isValidRequest(v({
                    topic: r
                }, u));
                const d = this.client.session.get(r), w = await this.getVerifyContext({
                    attestationId: a,
                    hash: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["hashMessage"])(JSON.stringify((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatJsonRpcRequest"])("wc_sessionRequest", u, h))),
                    encryptedId: l,
                    metadata: d.peer.metadata,
                    transportType: p
                }), m = {
                    id: h,
                    topic: r,
                    params: u,
                    verifyContext: w
                };
                await this.setPendingSessionRequest(m), p === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TRANSPORT_TYPES"].link_mode && (t = d.peer.metadata.redirect) != null && t.universal && this.client.core.addLinkModeSupportedApp((s = d.peer.metadata.redirect) == null ? void 0 : s.universal), (i = this.client.signConfig) != null && i.disableRequestQueue ? this.emitSessionRequest(m) : (this.addSessionRequestToSessionRequestQueue(m), this.processSessionRequestQueue());
            } catch (d) {
                await this.sendError({
                    id: h,
                    topic: r,
                    error: d
                }), this.client.logger.error(d);
            }
        }), c(this, "onSessionRequestResponse", (e, t)=>{
            const { id: s } = t, i = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["engineEvent"])("session_request", s);
            if (this.events.listenerCount(i) === 0) throw new Error(`emitting ${i} without any listeners`);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$validators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isJsonRpcResult"])(t) ? this.events.emit((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["engineEvent"])("session_request", s), {
                result: t.result
            }) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$validators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isJsonRpcError"])(t) && this.events.emit((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["engineEvent"])("session_request", s), {
                error: t.error
            });
        }), c(this, "onSessionEventRequest", async (e, t)=>{
            const { id: s, params: i } = t;
            try {
                const r = `${e}_session_event_${i.event.name}`, o = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MemoryStore"].get(r);
                if (o && this.isRequestOutOfSync(o, s)) {
                    this.client.logger.info(`Discarding out of sync request - ${s}`);
                    return;
                }
                this.isValidEmit(v({
                    topic: e
                }, i)), this.client.events.emit("session_event", {
                    id: s,
                    topic: e,
                    params: i
                }), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MemoryStore"].set(r, s);
            } catch (r) {
                await this.sendError({
                    id: s,
                    topic: e,
                    error: r
                }), this.client.logger.error(r);
            }
        }), c(this, "onSessionAuthenticateResponse", (e, t)=>{
            const { id: s } = t;
            this.client.logger.trace({
                type: "method",
                method: "onSessionAuthenticateResponse",
                topic: e,
                payload: t
            }), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$validators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isJsonRpcResult"])(t) ? this.events.emit((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["engineEvent"])("session_request", s), {
                result: t.result
            }) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$validators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isJsonRpcError"])(t) && this.events.emit((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["engineEvent"])("session_request", s), {
                error: t.error
            });
        }), c(this, "onSessionAuthenticateRequest", async (e)=>{
            var t;
            const { topic: s, payload: i, attestation: r, encryptedId: o, transportType: a } = e;
            try {
                const { requester: l, authPayload: p, expiryTimestamp: h } = i.params, u = await this.getVerifyContext({
                    attestationId: r,
                    hash: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["hashMessage"])(JSON.stringify(i)),
                    encryptedId: o,
                    metadata: l.metadata,
                    transportType: a
                }), d = {
                    requester: l,
                    pairingTopic: s,
                    id: i.id,
                    authPayload: p,
                    verifyContext: u,
                    expiryTimestamp: h
                };
                await this.setAuthRequest(i.id, {
                    request: d,
                    pairingTopic: s,
                    transportType: a
                }), a === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TRANSPORT_TYPES"].link_mode && (t = l.metadata.redirect) != null && t.universal && this.client.core.addLinkModeSupportedApp(l.metadata.redirect.universal), this.client.events.emit("session_authenticate", {
                    topic: s,
                    params: i.params,
                    id: i.id,
                    verifyContext: u
                });
            } catch (l) {
                this.client.logger.error(l);
                const p = i.params.requester.publicKey, h = await this.client.core.crypto.generateKeyPair(), u = this.getAppLinkIfEnabled(i.params.requester.metadata, a), d = {
                    type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TYPE_1"],
                    receiverPublicKey: p,
                    senderPublicKey: h
                };
                await this.sendError({
                    id: i.id,
                    topic: s,
                    error: l,
                    encodeOpts: d,
                    rpcOpts: N.wc_sessionAuthenticate.autoReject,
                    appLink: u
                });
            }
        }), c(this, "addSessionRequestToSessionRequestQueue", (e)=>{
            this.sessionRequestQueue.queue.push(e);
        }), c(this, "cleanupAfterResponse", (e)=>{
            this.deletePendingSessionRequest(e.response.id, {
                message: "fulfilled",
                code: 0
            }), setTimeout(()=>{
                this.sessionRequestQueue.state = $.idle, this.processSessionRequestQueue();
            }, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$time$2f$dist$2f$cjs$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toMiliseconds"])(this.requestQueueDelay));
        }), c(this, "cleanupPendingSentRequestsForTopic", ({ topic: e, error: t })=>{
            const s = this.client.core.history.pending;
            s.length > 0 && s.filter((i)=>i.topic === e && i.request.method === "wc_sessionRequest").forEach((i)=>{
                const r = i.request.id, o = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["engineEvent"])("session_request", r);
                if (this.events.listenerCount(o) === 0) throw new Error(`emitting ${o} without any listeners`);
                this.events.emit((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["engineEvent"])("session_request", i.request.id), {
                    error: t
                });
            });
        }), c(this, "processSessionRequestQueue", ()=>{
            if (this.sessionRequestQueue.state === $.active) {
                this.client.logger.info("session request queue is already active.");
                return;
            }
            const e = this.sessionRequestQueue.queue[0];
            if (!e) {
                this.client.logger.info("session request queue is empty.");
                return;
            }
            try {
                this.sessionRequestQueue.state = $.active, this.emitSessionRequest(e);
            } catch (t) {
                this.client.logger.error(t);
            }
        }), c(this, "emitSessionRequest", (e)=>{
            this.client.events.emit("session_request", e);
        }), c(this, "onPairingCreated", (e)=>{
            if (e.methods && this.expectedPairingMethodMap.set(e.topic, e.methods), e.active) return;
            const t = this.client.proposal.getAll().find((s)=>s.pairingTopic === e.topic);
            t && this.onSessionProposeRequest({
                topic: e.topic,
                payload: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatJsonRpcRequest"])("wc_sessionPropose", b(v({}, t), {
                    requiredNamespaces: t.requiredNamespaces,
                    optionalNamespaces: t.optionalNamespaces,
                    relays: t.relays,
                    proposer: t.proposer,
                    sessionProperties: t.sessionProperties,
                    scopedProperties: t.scopedProperties
                }), t.id)
            });
        }), c(this, "isValidConnect", async (e)=>{
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidParams"])(e)) {
                const { message: l } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("MISSING_OR_INVALID", `connect() params: ${JSON.stringify(e)}`);
                throw new Error(l);
            }
            const { pairingTopic: t, requiredNamespaces: s, optionalNamespaces: i, sessionProperties: r, scopedProperties: o, relays: a } = e;
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isUndefined"])(t) || await this.isValidPairingTopic(t), !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidRelays"])(a, !0)) {
                const { message: l } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("MISSING_OR_INVALID", `connect() relays: ${a}`);
                throw new Error(l);
            }
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isUndefined"])(s) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidObject"])(s) !== 0) {
                const l = "requiredNamespaces are deprecated and are automatically assigned to optionalNamespaces";
                [
                    "fatal",
                    "error",
                    "silent"
                ].includes(this.client.logger.level) ? console.warn(l) : this.client.logger.warn(l), this.validateNamespaces(s, "requiredNamespaces");
            }
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isUndefined"])(i) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidObject"])(i) !== 0 && this.validateNamespaces(i, "optionalNamespaces"), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isUndefined"])(r) || this.validateSessionProps(r, "sessionProperties"), !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isUndefined"])(o)) {
                this.validateSessionProps(o, "scopedProperties");
                const l = Object.keys(s || {}).concat(Object.keys(i || {}));
                if (!Object.keys(o).every((p)=>l.includes(p))) throw new Error(`Scoped properties must be a subset of required/optional namespaces, received: ${JSON.stringify(o)}, required/optional namespaces: ${JSON.stringify(l)}`);
            }
        }), c(this, "validateNamespaces", (e, t)=>{
            const s = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidRequiredNamespaces"])(e, "connect()", t);
            if (s) throw new Error(s.message);
        }), c(this, "isValidApprove", async (e)=>{
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidParams"])(e)) throw new Error((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("MISSING_OR_INVALID", `approve() params: ${e}`).message);
            const { id: t, namespaces: s, relayProtocol: i, sessionProperties: r, scopedProperties: o } = e;
            this.checkRecentlyDeleted(t), await this.isValidProposalId(t);
            const a = this.client.proposal.get(t), l = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidNamespaces"])(s, "approve()");
            if (l) throw new Error(l.message);
            const p = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isConformingNamespaces"])(a.requiredNamespaces, s, "approve()");
            if (p) throw new Error(p.message);
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidString"])(i, !0)) {
                const { message: h } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("MISSING_OR_INVALID", `approve() relayProtocol: ${i}`);
                throw new Error(h);
            }
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isUndefined"])(r) || this.validateSessionProps(r, "sessionProperties"), !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isUndefined"])(o)) {
                this.validateSessionProps(o, "scopedProperties");
                const h = new Set(Object.keys(s));
                if (!Object.keys(o).every((u)=>h.has(u))) throw new Error(`Scoped properties must be a subset of approved namespaces, received: ${JSON.stringify(o)}, approved namespaces: ${Array.from(h).join(", ")}`);
            }
        }), c(this, "isValidReject", async (e)=>{
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidParams"])(e)) {
                const { message: i } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("MISSING_OR_INVALID", `reject() params: ${e}`);
                throw new Error(i);
            }
            const { id: t, reason: s } = e;
            if (this.checkRecentlyDeleted(t), await this.isValidProposalId(t), !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidErrorReason"])(s)) {
                const { message: i } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("MISSING_OR_INVALID", `reject() reason: ${JSON.stringify(s)}`);
                throw new Error(i);
            }
        }), c(this, "isValidSessionSettleRequest", (e)=>{
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidParams"])(e)) {
                const { message: l } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("MISSING_OR_INVALID", `onSessionSettleRequest() params: ${e}`);
                throw new Error(l);
            }
            const { relay: t, controller: s, namespaces: i, expiry: r } = e;
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidRelay"])(t)) {
                const { message: l } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("MISSING_OR_INVALID", "onSessionSettleRequest() relay protocol should be a string");
                throw new Error(l);
            }
            const o = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidController"])(s, "onSessionSettleRequest()");
            if (o) throw new Error(o.message);
            const a = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidNamespaces"])(i, "onSessionSettleRequest()");
            if (a) throw new Error(a.message);
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isExpired"])(r)) {
                const { message: l } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("EXPIRED", "onSessionSettleRequest()");
                throw new Error(l);
            }
        }), c(this, "isValidUpdate", async (e)=>{
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidParams"])(e)) {
                const { message: a } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("MISSING_OR_INVALID", `update() params: ${e}`);
                throw new Error(a);
            }
            const { topic: t, namespaces: s } = e;
            this.checkRecentlyDeleted(t), await this.isValidSessionTopic(t);
            const i = this.client.session.get(t), r = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidNamespaces"])(s, "update()");
            if (r) throw new Error(r.message);
            const o = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isConformingNamespaces"])(i.requiredNamespaces, s, "update()");
            if (o) throw new Error(o.message);
        }), c(this, "isValidExtend", async (e)=>{
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidParams"])(e)) {
                const { message: s } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("MISSING_OR_INVALID", `extend() params: ${e}`);
                throw new Error(s);
            }
            const { topic: t } = e;
            this.checkRecentlyDeleted(t), await this.isValidSessionTopic(t);
        }), c(this, "isValidRequest", async (e)=>{
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidParams"])(e)) {
                const { message: a } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("MISSING_OR_INVALID", `request() params: ${e}`);
                throw new Error(a);
            }
            const { topic: t, request: s, chainId: i, expiry: r } = e;
            this.checkRecentlyDeleted(t), await this.isValidSessionTopic(t);
            const { namespaces: o } = this.client.session.get(t);
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidNamespacesChainId"])(o, i)) {
                const { message: a } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("MISSING_OR_INVALID", `request() chainId: ${i}`);
                throw new Error(a);
            }
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidRequest"])(s)) {
                const { message: a } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("MISSING_OR_INVALID", `request() ${JSON.stringify(s)}`);
                throw new Error(a);
            }
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidNamespacesRequest"])(o, i, s.method)) {
                const { message: a } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("MISSING_OR_INVALID", `request() method: ${s.method}`);
                throw new Error(a);
            }
            if (r && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidRequestExpiry"])(r, _e)) {
                const { message: a } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("MISSING_OR_INVALID", `request() expiry: ${r}. Expiry must be a number (in seconds) between ${_e.min} and ${_e.max}`);
                throw new Error(a);
            }
        }), c(this, "isValidRespond", async (e)=>{
            var t;
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidParams"])(e)) {
                const { message: r } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("MISSING_OR_INVALID", `respond() params: ${e}`);
                throw new Error(r);
            }
            const { topic: s, response: i } = e;
            try {
                await this.isValidSessionTopic(s);
            } catch (r) {
                throw (t = e?.response) != null && t.id && this.cleanupAfterResponse(e), r;
            }
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidResponse"])(i)) {
                const { message: r } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("MISSING_OR_INVALID", `respond() response: ${JSON.stringify(i)}`);
                throw new Error(r);
            }
        }), c(this, "isValidPing", async (e)=>{
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidParams"])(e)) {
                const { message: s } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("MISSING_OR_INVALID", `ping() params: ${e}`);
                throw new Error(s);
            }
            const { topic: t } = e;
            await this.isValidSessionOrPairingTopic(t);
        }), c(this, "isValidEmit", async (e)=>{
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidParams"])(e)) {
                const { message: o } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("MISSING_OR_INVALID", `emit() params: ${e}`);
                throw new Error(o);
            }
            const { topic: t, event: s, chainId: i } = e;
            await this.isValidSessionTopic(t);
            const { namespaces: r } = this.client.session.get(t);
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidNamespacesChainId"])(r, i)) {
                const { message: o } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("MISSING_OR_INVALID", `emit() chainId: ${i}`);
                throw new Error(o);
            }
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidEvent"])(s)) {
                const { message: o } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("MISSING_OR_INVALID", `emit() event: ${JSON.stringify(s)}`);
                throw new Error(o);
            }
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidNamespacesEvent"])(r, i, s.name)) {
                const { message: o } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("MISSING_OR_INVALID", `emit() event: ${JSON.stringify(s)}`);
                throw new Error(o);
            }
        }), c(this, "isValidDisconnect", async (e)=>{
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidParams"])(e)) {
                const { message: s } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("MISSING_OR_INVALID", `disconnect() params: ${e}`);
                throw new Error(s);
            }
            const { topic: t } = e;
            await this.isValidSessionOrPairingTopic(t);
        }), c(this, "isValidAuthenticate", (e)=>{
            const { chains: t, uri: s, domain: i, nonce: r } = e;
            if (!Array.isArray(t) || t.length === 0) throw new Error("chains is required and must be a non-empty array");
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidString"])(s, !1)) throw new Error("uri is required parameter");
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidString"])(i, !1)) throw new Error("domain is required parameter");
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidString"])(r, !1)) throw new Error("nonce is required parameter");
            if ([
                ...new Set(t.map((a)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseChainId"])(a).namespace))
            ].length > 1) throw new Error("Multi-namespace requests are not supported. Please request single namespace only.");
            const { namespace: o } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseChainId"])(t[0]);
            if (o !== "eip155") throw new Error("Only eip155 namespace is supported for authenticated sessions. Please use .connect() for non-eip155 chains.");
        }), c(this, "getVerifyContext", async (e)=>{
            const { attestationId: t, hash: s, encryptedId: i, metadata: r, transportType: o } = e, a = {
                verified: {
                    verifyUrl: r.verifyUrl || __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["VERIFY_SERVER"],
                    validation: "UNKNOWN",
                    origin: r.url || ""
                }
            };
            try {
                if (o === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TRANSPORT_TYPES"].link_mode) {
                    const p = this.getAppLinkIfEnabled(r, o);
                    return a.verified.validation = p && new URL(p).origin === new URL(r.url).origin ? "VALID" : "INVALID", a;
                }
                const l = await this.client.core.verify.resolve({
                    attestationId: t,
                    hash: s,
                    encryptedId: i,
                    verifyUrl: r.verifyUrl
                });
                l && (a.verified.origin = l.origin, a.verified.isScam = l.isScam, a.verified.validation = l.origin === new URL(r.url).origin ? "VALID" : "INVALID");
            } catch (l) {
                this.client.logger.warn(l);
            }
            return this.client.logger.debug(`Verify context: ${JSON.stringify(a)}`), a;
        }), c(this, "validateSessionProps", (e, t)=>{
            Object.values(e).forEach((s, i)=>{
                if (s == null) {
                    const { message: r } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("MISSING_OR_INVALID", `${t} must contain an existing value for each key. Received: ${s} for key ${Object.keys(e)[i]}`);
                    throw new Error(r);
                }
            });
        }), c(this, "getPendingAuthRequest", (e)=>{
            const t = this.client.auth.requests.get(e);
            return typeof t == "object" ? t : void 0;
        }), c(this, "addToRecentlyDeleted", (e, t)=>{
            if (this.recentlyDeletedMap.set(e, t), this.recentlyDeletedMap.size >= this.recentlyDeletedLimit) {
                let s = 0;
                const i = this.recentlyDeletedLimit / 2;
                for (const r of this.recentlyDeletedMap.keys()){
                    if (s++ >= i) break;
                    this.recentlyDeletedMap.delete(r);
                }
            }
        }), c(this, "checkRecentlyDeleted", (e)=>{
            const t = this.recentlyDeletedMap.get(e);
            if (t) {
                const { message: s } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("MISSING_OR_INVALID", `Record was recently deleted - ${t}: ${e}`);
                throw new Error(s);
            }
        }), c(this, "isLinkModeEnabled", (e, t)=>{
            var s, i, r, o, a, l, p, h, u;
            return !e || t !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TRANSPORT_TYPES"].link_mode ? !1 : ((i = (s = this.client.metadata) == null ? void 0 : s.redirect) == null ? void 0 : i.linkMode) === !0 && ((o = (r = this.client.metadata) == null ? void 0 : r.redirect) == null ? void 0 : o.universal) !== void 0 && ((l = (a = this.client.metadata) == null ? void 0 : a.redirect) == null ? void 0 : l.universal) !== "" && ((p = e?.redirect) == null ? void 0 : p.universal) !== void 0 && ((h = e?.redirect) == null ? void 0 : h.universal) !== "" && ((u = e?.redirect) == null ? void 0 : u.linkMode) === !0 && this.client.core.linkModeSupportedApps.includes(e.redirect.universal) && typeof (global == null ? void 0 : global.Linking) < "u";
        }), c(this, "getAppLinkIfEnabled", (e, t)=>{
            var s;
            return this.isLinkModeEnabled(e, t) ? (s = e?.redirect) == null ? void 0 : s.universal : void 0;
        }), c(this, "handleLinkModeMessage", ({ url: e })=>{
            if (!e || !e.includes("wc_ev") || !e.includes("topic")) return;
            const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getSearchParamFromURL"])(e, "topic") || "", s = decodeURIComponent((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getSearchParamFromURL"])(e, "wc_ev") || ""), i = this.client.session.keys.includes(t);
            i && this.client.session.update(t, {
                transportType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TRANSPORT_TYPES"].link_mode
            }), this.client.core.dispatchEnvelope({
                topic: t,
                message: s,
                sessionExists: i
            });
        }), c(this, "registerLinkModeListeners", async ()=>{
            var e;
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isTestRun"])() || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isReactNative"])() && (e = this.client.metadata.redirect) != null && e.linkMode) {
                const t = global == null ? void 0 : global.Linking;
                if (typeof t < "u") {
                    t.addEventListener("url", this.handleLinkModeMessage, this.client.name);
                    const s = await t.getInitialURL();
                    s && setTimeout(()=>{
                        this.handleLinkModeMessage({
                            url: s
                        });
                    }, 50);
                }
            }
        }), c(this, "shouldSetTVF", (e, t)=>{
            if (!t || e !== "wc_sessionRequest") return !1;
            const { request: s } = t;
            return Object.keys(Ke).includes(s.method);
        }), c(this, "getTVFParams", (e, t, s)=>{
            var i, r;
            try {
                const o = t.request.method, a = this.extractTxHashesFromResult(o, s);
                return b(v({
                    correlationId: e,
                    rpcMethods: [
                        o
                    ],
                    chainId: t.chainId
                }, this.isValidContractData(t.request.params) && {
                    contractAddresses: [
                        (r = (i = t.request.params) == null ? void 0 : i[0]) == null ? void 0 : r.to
                    ]
                }), {
                    txHashes: a
                });
            } catch (o) {
                this.client.logger.warn("Error getting TVF params", o);
            }
            return {};
        }), c(this, "isValidContractData", (e)=>{
            var t;
            if (!e) return !1;
            try {
                const s = e?.data || ((t = e?.[0]) == null ? void 0 : t.data);
                if (!s.startsWith("0x")) return !1;
                const i = s.slice(2);
                return /^[0-9a-fA-F]*$/.test(i) ? i.length % 2 === 0 : !1;
            } catch  {}
            return !1;
        }), c(this, "extractTxHashesFromResult", (e, t)=>{
            try {
                const s = Ke[e];
                if (typeof t == "string") return [
                    t
                ];
                const i = t[s.key];
                if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidArray"])(i)) return e === "solana_signAllTransactions" ? i.map((r)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extractSolanaTransactionId"])(r)) : i;
                if (typeof i == "string") return [
                    i
                ];
            } catch (s) {
                this.client.logger.warn("Error extracting tx hashes from result", s);
            }
            return [];
        });
    }
    async processPendingMessageEvents() {
        try {
            const n = this.client.session.keys, e = this.client.core.relayer.messages.getWithoutAck(n);
            for (const [t, s] of Object.entries(e))for (const i of s)try {
                await this.onProviderMessageEvent({
                    topic: t,
                    message: i,
                    publishedAt: Date.now()
                });
            } catch  {
                this.client.logger.warn(`Error processing pending message event for topic: ${t}, message: ${i}`);
            }
        } catch (n) {
            this.client.logger.warn("processPendingMessageEvents failed", n);
        }
    }
    isInitialized() {
        if (!this.initialized) {
            const { message: n } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("NOT_INITIALIZED", this.name);
            throw new Error(n);
        }
    }
    async confirmOnlineStateOrThrow() {
        await this.client.core.relayer.confirmOnlineStateOrThrow();
    }
    registerRelayerEvents() {
        this.client.core.relayer.on(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RELAYER_EVENTS"].message, (n)=>{
            this.onProviderMessageEvent(n);
        });
    }
    async onRelayMessage(n) {
        const { topic: e, message: t, attestation: s, transportType: i } = n, { publicKey: r } = this.client.auth.authKeys.keys.includes(ce) ? this.client.auth.authKeys.get(ce) : {
            responseTopic: void 0,
            publicKey: void 0
        };
        try {
            const o = await this.client.core.crypto.decode(e, t, {
                receiverPublicKey: r,
                encoding: i === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TRANSPORT_TYPES"].link_mode ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BASE64URL"] : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BASE64"]
            });
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$validators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isJsonRpcRequest"])(o) ? (this.client.core.history.set(e, o), await this.onRelayEventRequest({
                topic: e,
                payload: o,
                attestation: s,
                transportType: i,
                encryptedId: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["hashMessage"])(t)
            })) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$jsonrpc$2d$utils$2f$dist$2f$esm$2f$validators$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isJsonRpcResponse"])(o) ? (await this.client.core.history.resolve(o), await this.onRelayEventResponse({
                topic: e,
                payload: o,
                transportType: i
            }), this.client.core.history.delete(e, o.id)) : await this.onRelayEventUnknownPayload({
                topic: e,
                payload: o,
                transportType: i
            }), await this.client.core.relayer.messages.ack(e, t);
        } catch (o) {
            this.client.logger.error(o);
        }
    }
    registerExpirerEvents() {
        this.client.core.expirer.on(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EXPIRER_EVENTS"].expired, async (n)=>{
            const { topic: e, id: t } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseExpirerTarget"])(n.target);
            if (t && this.client.pendingRequest.keys.includes(t)) return await this.deletePendingSessionRequest(t, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("EXPIRED"), !0);
            if (t && this.client.auth.requests.keys.includes(t)) return await this.deletePendingAuthRequest(t, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("EXPIRED"), !0);
            e ? this.client.session.keys.includes(e) && (await this.deleteSession({
                topic: e,
                expirerHasDeleted: !0
            }), this.client.events.emit("session_expire", {
                topic: e
            })) : t && (await this.deleteProposal(t, !0), this.client.events.emit("proposal_expire", {
                id: t
            }));
        });
    }
    registerPairingEvents() {
        this.client.core.pairing.events.on(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PAIRING_EVENTS"].create, (n)=>this.onPairingCreated(n)), this.client.core.pairing.events.on(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PAIRING_EVENTS"].delete, (n)=>{
            this.addToRecentlyDeleted(n.topic, "pairing");
        });
    }
    isValidPairingTopic(n) {
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidString"])(n, !1)) {
            const { message: e } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("MISSING_OR_INVALID", `pairing topic should be a string: ${n}`);
            throw new Error(e);
        }
        if (!this.client.core.pairing.pairings.keys.includes(n)) {
            const { message: e } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("NO_MATCHING_KEY", `pairing topic doesn't exist: ${n}`);
            throw new Error(e);
        }
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isExpired"])(this.client.core.pairing.pairings.get(n).expiry)) {
            const { message: e } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("EXPIRED", `pairing topic: ${n}`);
            throw new Error(e);
        }
    }
    async isValidSessionTopic(n) {
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidString"])(n, !1)) {
            const { message: e } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("MISSING_OR_INVALID", `session topic should be a string: ${n}`);
            throw new Error(e);
        }
        if (this.checkRecentlyDeleted(n), !this.client.session.keys.includes(n)) {
            const { message: e } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("NO_MATCHING_KEY", `session topic doesn't exist: ${n}`);
            throw new Error(e);
        }
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isExpired"])(this.client.session.get(n).expiry)) {
            await this.deleteSession({
                topic: n
            });
            const { message: e } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("EXPIRED", `session topic: ${n}`);
            throw new Error(e);
        }
        if (!this.client.core.crypto.keychain.has(n)) {
            const { message: e } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("MISSING_OR_INVALID", `session topic does not exist in keychain: ${n}`);
            throw await this.deleteSession({
                topic: n
            }), new Error(e);
        }
    }
    async isValidSessionOrPairingTopic(n) {
        if (this.checkRecentlyDeleted(n), this.client.session.keys.includes(n)) await this.isValidSessionTopic(n);
        else if (this.client.core.pairing.pairings.keys.includes(n)) this.isValidPairingTopic(n);
        else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidString"])(n, !1)) {
            const { message: e } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("NO_MATCHING_KEY", `session or pairing topic doesn't exist: ${n}`);
            throw new Error(e);
        } else {
            const { message: e } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("MISSING_OR_INVALID", `session or pairing topic should be a string: ${n}`);
            throw new Error(e);
        }
    }
    async isValidProposalId(n) {
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidId"])(n)) {
            const { message: e } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("MISSING_OR_INVALID", `proposal id should be a number: ${n}`);
            throw new Error(e);
        }
        if (!this.client.proposal.keys.includes(n)) {
            const { message: e } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("NO_MATCHING_KEY", `proposal id doesn't exist: ${n}`);
            throw new Error(e);
        }
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isExpired"])(this.client.proposal.get(n).expiryTimestamp)) {
            await this.deleteProposal(n);
            const { message: e } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getInternalError"])("EXPIRED", `proposal id: ${n}`);
            throw new Error(e);
        }
    }
}
class Os extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Store"] {
    constructor(n, e){
        super(n, e, pt, we), this.core = n, this.logger = e;
    }
}
class St extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Store"] {
    constructor(n, e){
        super(n, e, ht, we), this.core = n, this.logger = e;
    }
}
class bs extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Store"] {
    constructor(n, e){
        super(n, e, ut, we, (t)=>t.id), this.core = n, this.logger = e;
    }
}
class As extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Store"] {
    constructor(n, e){
        super(n, e, mt, ae, ()=>ce), this.core = n, this.logger = e;
    }
}
class xs extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Store"] {
    constructor(n, e){
        super(n, e, _t, ae), this.core = n, this.logger = e;
    }
}
class Cs extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Store"] {
    constructor(n, e){
        super(n, e, Et, ae, (t)=>t.id), this.core = n, this.logger = e;
    }
}
var Vs = Object.defineProperty, Ds = (S, n, e)=>n in S ? Vs(S, n, {
        enumerable: !0,
        configurable: !0,
        writable: !0,
        value: e
    }) : S[n] = e, Ge = (S, n, e)=>Ds(S, typeof n != "symbol" ? n + "" : n, e);
class Ls {
    constructor(n, e){
        this.core = n, this.logger = e, Ge(this, "authKeys"), Ge(this, "pairingTopics"), Ge(this, "requests"), this.authKeys = new As(this.core, this.logger), this.pairingTopics = new xs(this.core, this.logger), this.requests = new Cs(this.core, this.logger);
    }
    async init() {
        await this.authKeys.init(), await this.pairingTopics.init(), await this.requests.init();
    }
}
var ks = Object.defineProperty, Ms = (S, n, e)=>n in S ? ks(S, n, {
        enumerable: !0,
        configurable: !0,
        writable: !0,
        value: e
    }) : S[n] = e, E = (S, n, e)=>Ms(S, typeof n != "symbol" ? n + "" : n, e);
class Ee extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$types$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ISignClient"] {
    constructor(n){
        super(n), E(this, "protocol", De), E(this, "version", Le), E(this, "name", me.name), E(this, "metadata"), E(this, "core"), E(this, "logger"), E(this, "events", new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$events$2f$events$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EventEmitter"]), E(this, "engine"), E(this, "session"), E(this, "proposal"), E(this, "pendingRequest"), E(this, "auth"), E(this, "signConfig"), E(this, "on", (t, s)=>this.events.on(t, s)), E(this, "once", (t, s)=>this.events.once(t, s)), E(this, "off", (t, s)=>this.events.off(t, s)), E(this, "removeListener", (t, s)=>this.events.removeListener(t, s)), E(this, "removeAllListeners", (t)=>this.events.removeAllListeners(t)), E(this, "connect", async (t)=>{
            try {
                return await this.engine.connect(t);
            } catch (s) {
                throw this.logger.error(s.message), s;
            }
        }), E(this, "pair", async (t)=>{
            try {
                return await this.engine.pair(t);
            } catch (s) {
                throw this.logger.error(s.message), s;
            }
        }), E(this, "approve", async (t)=>{
            try {
                return await this.engine.approve(t);
            } catch (s) {
                throw this.logger.error(s.message), s;
            }
        }), E(this, "reject", async (t)=>{
            try {
                return await this.engine.reject(t);
            } catch (s) {
                throw this.logger.error(s.message), s;
            }
        }), E(this, "update", async (t)=>{
            try {
                return await this.engine.update(t);
            } catch (s) {
                throw this.logger.error(s.message), s;
            }
        }), E(this, "extend", async (t)=>{
            try {
                return await this.engine.extend(t);
            } catch (s) {
                throw this.logger.error(s.message), s;
            }
        }), E(this, "request", async (t)=>{
            try {
                return await this.engine.request(t);
            } catch (s) {
                throw this.logger.error(s.message), s;
            }
        }), E(this, "respond", async (t)=>{
            try {
                return await this.engine.respond(t);
            } catch (s) {
                throw this.logger.error(s.message), s;
            }
        }), E(this, "ping", async (t)=>{
            try {
                return await this.engine.ping(t);
            } catch (s) {
                throw this.logger.error(s.message), s;
            }
        }), E(this, "emit", async (t)=>{
            try {
                return await this.engine.emit(t);
            } catch (s) {
                throw this.logger.error(s.message), s;
            }
        }), E(this, "disconnect", async (t)=>{
            try {
                return await this.engine.disconnect(t);
            } catch (s) {
                throw this.logger.error(s.message), s;
            }
        }), E(this, "find", (t)=>{
            try {
                return this.engine.find(t);
            } catch (s) {
                throw this.logger.error(s.message), s;
            }
        }), E(this, "getPendingSessionRequests", ()=>{
            try {
                return this.engine.getPendingSessionRequests();
            } catch (t) {
                throw this.logger.error(t.message), t;
            }
        }), E(this, "authenticate", async (t, s)=>{
            try {
                return await this.engine.authenticate(t, s);
            } catch (i) {
                throw this.logger.error(i.message), i;
            }
        }), E(this, "formatAuthMessage", (t)=>{
            try {
                return this.engine.formatAuthMessage(t);
            } catch (s) {
                throw this.logger.error(s.message), s;
            }
        }), E(this, "approveSessionAuthenticate", async (t)=>{
            try {
                return await this.engine.approveSessionAuthenticate(t);
            } catch (s) {
                throw this.logger.error(s.message), s;
            }
        }), E(this, "rejectSessionAuthenticate", async (t)=>{
            try {
                return await this.engine.rejectSessionAuthenticate(t);
            } catch (s) {
                throw this.logger.error(s.message), s;
            }
        }), this.name = n?.name || me.name, this.metadata = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$utils$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["populateAppMetadata"])(n?.metadata), this.signConfig = n?.signConfig;
        const e = typeof n?.logger < "u" && typeof n?.logger != "string" ? n.logger : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$pino$2f$browser$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__pino$3e$__["pino"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$logger$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getDefaultLoggerOptions"])({
            level: n?.logger || me.logger
        }));
        this.core = n?.core || new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reown$2f$appkit$2d$controllers$2f$node_modules$2f40$walletconnect$2f$core$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Core"](n), this.logger = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$logger$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["generateChildLogger"])(e, this.name), this.session = new St(this.core, this.logger), this.proposal = new Os(this.core, this.logger), this.pendingRequest = new bs(this.core, this.logger), this.engine = new Ns(this), this.auth = new Ls(this.core, this.logger);
    }
    static async init(n) {
        const e = new Ee(n);
        return await e.initialize(), e;
    }
    get context() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$logger$2f$dist$2f$index$2e$es$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getLoggerContext"])(this.logger);
    }
    get pairing() {
        return this.core.pairing.pairings;
    }
    async initialize() {
        this.logger.trace("Initialized");
        try {
            await this.core.start(), await this.session.init(), await this.proposal.init(), await this.pendingRequest.init(), await this.auth.init(), await this.engine.init(), this.logger.info("SignClient Initialization Success"), setTimeout(()=>{
                this.engine.processRelayMessageCache();
            }, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$time$2f$dist$2f$cjs$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toMiliseconds"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$walletconnect$2f$time$2f$dist$2f$cjs$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ONE_SECOND"]));
        } catch (n) {
            throw this.logger.info("SignClient Initialization Failure"), this.logger.error(n.message), n;
        }
    }
}
const $s = St, Ks = Ee;
;
 //# sourceMappingURL=index.es.js.map
}}),
}]);

//# sourceMappingURL=node_modules_%40reown_527d1b46._.js.map