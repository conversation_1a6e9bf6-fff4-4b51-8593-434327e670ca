{"version": 3, "file": "opaqueDataToDepositData.js", "sourceRoot": "", "sources": ["../../../op-stack/utils/opaqueDataToDepositData.ts"], "names": [], "mappings": "AAEA,OAAO,EAAsB,IAAI,EAAE,MAAM,0BAA0B,CAAA;AACnE,OAAO,EAAuB,KAAK,EAAE,MAAM,2BAA2B,CAAA;AACtE,OAAO,EAAE,WAAW,EAAE,MAAM,iCAAiC,CAAA;AAiB7D,MAAM,UAAU,uBAAuB,CACrC,UAAe;IAEf,IAAI,MAAM,GAAG,CAAC,CAAA;IACd,MAAM,IAAI,GAAG,KAAK,CAAC,UAAU,EAAE,MAAM,EAAE,MAAM,GAAG,EAAE,CAAC,CAAA;IACnD,MAAM,IAAI,EAAE,CAAA;IACZ,MAAM,KAAK,GAAG,KAAK,CAAC,UAAU,EAAE,MAAM,EAAE,MAAM,GAAG,EAAE,CAAC,CAAA;IACpD,MAAM,IAAI,EAAE,CAAA;IACZ,MAAM,GAAG,GAAG,KAAK,CAAC,UAAU,EAAE,MAAM,EAAE,MAAM,GAAG,CAAC,CAAC,CAAA;IACjD,MAAM,IAAI,CAAC,CAAA;IACX,MAAM,UAAU,GAAG,MAAM,CAAC,KAAK,CAAC,UAAU,EAAE,MAAM,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,EAAE,CAAA;IACvE,MAAM,IAAI,CAAC,CAAA;IACX,MAAM,IAAI,GACR,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC;QAC3B,CAAC,CAAC,IAAI;QACN,CAAC,CAAC,KAAK,CAAC,UAAU,EAAE,MAAM,EAAE,UAAU,CAAC,MAAM,CAAC,CAAA;IAClD,OAAO;QACL,IAAI,EAAE,WAAW,CAAC,IAAI,CAAC;QACvB,KAAK,EAAE,WAAW,CAAC,KAAK,CAAC;QACzB,GAAG,EAAE,WAAW,CAAC,GAAG,CAAC;QACrB,UAAU;QACV,IAAI;KACL,CAAA;AACH,CAAC"}