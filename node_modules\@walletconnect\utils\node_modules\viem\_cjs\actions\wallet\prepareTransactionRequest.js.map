{"version": 3, "file": "prepareTransactionRequest.js", "sourceRoot": "", "sources": ["../../../actions/wallet/prepareTransactionRequest.ts"], "names": [], "mappings": ";;;AAwOA,8DAsMC;AA5aD,0EAG6C;AAC7C,sFAGmD;AACnD,wEAI4C;AAC5C,kEAGyC;AACzC,wFAGoD;AAIpD,gDAG4B;AAsB5B,kFAA2E;AAC3E,wEAAiE;AACjE,sGAA+F;AAC/F,0EAAmE;AAEnE,2DAAoD;AAEpD,+EAIiD;AACjD,yFAGsD;AACtD,2DAAmE;AAEtD,QAAA,iBAAiB,GAAG;IAC/B,qBAAqB;IACrB,SAAS;IACT,MAAM;IACN,KAAK;IACL,OAAO;IACP,MAAM;CACE,CAAA;AAGG,QAAA,mBAAmB,GAAiB,IAAI,GAAG,EAAmB,CAAA;AAyJpE,KAAK,UAAU,yBAAyB,CAO7C,MAAyC,EACzC,IAMC;IAUD,MAAM,EACJ,OAAO,EAAE,QAAQ,GAAG,MAAM,CAAC,OAAO,EAClC,KAAK,EACL,KAAK,EACL,GAAG,EACH,GAAG,EACH,KAAK,EACL,YAAY,EACZ,UAAU,GAAG,yBAAiB,EAC9B,IAAI,GACL,GAAG,IAAI,CAAA;IACR,MAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,IAAA,8BAAY,EAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAA;IAE5D,MAAM,OAAO,GAAG,EAAE,GAAG,IAAI,EAAE,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAA;IAE3E,IAAI,KAAwB,CAAA;IAC5B,KAAK,UAAU,QAAQ;QACrB,IAAI,KAAK;YAAE,OAAO,KAAK,CAAA;QACvB,KAAK,GAAG,MAAM,IAAA,wBAAS,EACrB,MAAM,EACN,sBAAS,EACT,UAAU,CACX,CAAC,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC,CAAA;QACzB,OAAO,KAAK,CAAA;IACd,CAAC;IAED,IAAI,OAA2B,CAAA;IAC/B,KAAK,UAAU,UAAU;QACvB,IAAI,OAAO;YAAE,OAAO,OAAO,CAAA;QAC3B,IAAI,KAAK;YAAE,OAAO,KAAK,CAAC,EAAE,CAAA;QAC1B,IAAI,OAAO,IAAI,CAAC,OAAO,KAAK,WAAW;YAAE,OAAO,IAAI,CAAC,OAAO,CAAA;QAC5D,MAAM,QAAQ,GAAG,MAAM,IAAA,wBAAS,EAAC,MAAM,EAAE,0BAAW,EAAE,YAAY,CAAC,CAAC,EAAE,CAAC,CAAA;QACvE,OAAO,GAAG,QAAQ,CAAA;QAClB,OAAO,OAAO,CAAA;IAChB,CAAC;IAED,IACE,CAAC,UAAU,CAAC,QAAQ,CAAC,qBAAqB,CAAC;QACzC,UAAU,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAClC,KAAK;QACL,GAAG,EACH,CAAC;QACD,MAAM,WAAW,GAAG,IAAA,0CAAkB,EAAC,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAA;QAEtD,IAAI,UAAU,CAAC,QAAQ,CAAC,qBAAqB,CAAC,EAAE,CAAC;YAC/C,MAAM,eAAe,GAAG,IAAA,8DAA4B,EAAC;gBACnD,WAAW;gBACX,EAAE,EAAE,KAAK;aACV,CAAC,CAAA;YACF,OAAO,CAAC,mBAAmB,GAAG,eAAe,CAAA;QAC/C,CAAC;QACD,IAAI,UAAU,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YACpC,MAAM,MAAM,GAAG,IAAA,gCAAa,EAAC,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,EAAE,CAAC,CAAA;YACzD,MAAM,QAAQ,GAAG,IAAA,kCAAc,EAAC;gBAC9B,KAAK;gBACL,WAAW;gBACX,MAAM;gBACN,EAAE,EAAE,KAAK;aACV,CAAC,CAAA;YACF,OAAO,CAAC,QAAQ,GAAG,QAAQ,CAAA;QAC7B,CAAC;IACH,CAAC;IAED,IAAI,UAAU,CAAC,QAAQ,CAAC,SAAS,CAAC;QAAE,OAAO,CAAC,OAAO,GAAG,MAAM,UAAU,EAAE,CAAA;IAExE,IACE,CAAC,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAC5D,OAAO,IAAI,KAAK,WAAW,EAC3B,CAAC;QACD,IAAI,CAAC;YACH,OAAO,CAAC,IAAI,GAAG,IAAA,0CAAkB,EAC/B,OAAkC,CAC5B,CAAA;QACV,CAAC;QAAC,MAAM,CAAC;YACP,IAAI,gBAAgB,GAAG,2BAAmB,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;YAC1D,IAAI,OAAO,gBAAgB,KAAK,WAAW,EAAE,CAAC;gBAC5C,MAAM,KAAK,GAAG,MAAM,QAAQ,EAAE,CAAA;gBAC9B,gBAAgB,GAAG,OAAO,KAAK,EAAE,aAAa,KAAK,QAAQ,CAAA;gBAC3D,2BAAmB,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,EAAE,gBAAgB,CAAC,CAAA;YACvD,CAAC;YACD,OAAO,CAAC,IAAI,GAAG,gBAAgB,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAA;QACxD,CAAC;IACH,CAAC;IAED,IAAI,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;QAGhC,IAAI,OAAO,CAAC,IAAI,KAAK,QAAQ,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;YAE5D,IACE,OAAO,OAAO,CAAC,YAAY,KAAK,WAAW;gBAC3C,OAAO,OAAO,CAAC,oBAAoB,KAAK,WAAW,EACnD,CAAC;gBACD,MAAM,KAAK,GAAG,MAAM,QAAQ,EAAE,CAAA;gBAC9B,MAAM,EAAE,YAAY,EAAE,oBAAoB,EAAE,GAC1C,MAAM,IAAA,mDAA2B,EAAC,MAAM,EAAE;oBACxC,KAAK,EAAE,KAAc;oBACrB,KAAK;oBACL,OAAO,EAAE,OAA8C;iBACxD,CAAC,CAAA;gBAEJ,IACE,OAAO,IAAI,CAAC,oBAAoB,KAAK,WAAW;oBAChD,IAAI,CAAC,YAAY;oBACjB,IAAI,CAAC,YAAY,GAAG,oBAAoB;oBAExC,MAAM,IAAI,gCAAuB,CAAC;wBAChC,oBAAoB;qBACrB,CAAC,CAAA;gBAEJ,OAAO,CAAC,oBAAoB,GAAG,oBAAoB,CAAA;gBACnD,OAAO,CAAC,YAAY,GAAG,YAAY,CAAA;YACrC,CAAC;QACH,CAAC;aAAM,CAAC;YAEN,IACE,OAAO,IAAI,CAAC,YAAY,KAAK,WAAW;gBACxC,OAAO,IAAI,CAAC,oBAAoB,KAAK,WAAW;gBAEhD,MAAM,IAAI,qCAA4B,EAAE,CAAA;YAE1C,IAAI,OAAO,IAAI,CAAC,QAAQ,KAAK,WAAW,EAAE,CAAC;gBACzC,MAAM,KAAK,GAAG,MAAM,QAAQ,EAAE,CAAA;gBAC9B,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,MAAM,IAAA,mDAA2B,EAC/D,MAAM,EACN;oBACE,KAAK,EAAE,KAAc;oBACrB,KAAK;oBACL,OAAO,EAAE,OAA8C;oBACvD,IAAI,EAAE,QAAQ;iBACf,CACF,CAAA;gBACD,OAAO,CAAC,QAAQ,GAAG,SAAS,CAAA;YAC9B,CAAC;QACH,CAAC;IACH,CAAC;IAED,IAAI,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,OAAO,GAAG,KAAK,WAAW;QAC1D,OAAO,CAAC,GAAG,GAAG,MAAM,IAAA,wBAAS,EAC3B,MAAM,EACN,4BAAW,EACX,aAAa,CACd,CAAC;YACA,GAAG,OAAO;YACV,OAAO,EAAE,OAAO;gBACd,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,CAAC,OAAO,EAAE,IAAI,EAAE,UAAU,EAAE;gBAChD,CAAC,CAAC,OAAO;SACa,CAAC,CAAA;IAE7B,IAAI,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,OAAO,KAAK,KAAK,WAAW,IAAI,OAAO,EAAE,CAAC;QAC5E,IAAI,YAAY,EAAE,CAAC;YACjB,MAAM,OAAO,GAAG,MAAM,UAAU,EAAE,CAAA;YAClC,OAAO,CAAC,KAAK,GAAG,MAAM,YAAY,CAAC,OAAO,CAAC;gBACzC,OAAO,EAAE,OAAO,CAAC,OAAO;gBACxB,OAAO;gBACP,MAAM;aACP,CAAC,CAAA;QACJ,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,KAAK,GAAG,MAAM,IAAA,wBAAS,EAC7B,MAAM,EACN,4CAAmB,EACnB,qBAAqB,CACtB,CAAC;gBACA,OAAO,EAAE,OAAO,CAAC,OAAO;gBACxB,QAAQ,EAAE,SAAS;aACpB,CAAC,CAAA;QACJ,CAAC;IACH,CAAC;IAED,IAAA,gCAAa,EAAC,OAAkC,CAAC,CAAA;IAEjD,OAAO,OAAO,CAAC,UAAU,CAAA;IAEzB,OAAO,OAAc,CAAA;AACvB,CAAC"}