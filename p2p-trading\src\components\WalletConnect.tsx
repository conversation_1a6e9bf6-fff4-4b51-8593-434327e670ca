'use client'

import { useAccount, useConnect, useDisconnect } from 'wagmi'
import { useEffect, useState } from 'react'
import { dbHelpers } from '@/lib/supabase'
import UserDetailsForm from './UserDetailsForm'

export default function WalletConnect() {
  const { address, isConnected } = useAccount()
  const { connect, connectors, error, isPending } = useConnect()
  const { disconnect } = useDisconnect()
  const [user, setUser] = useState<any>(null)
  const [showUserForm, setShowUserForm] = useState(false)
  const [loading, setLoading] = useState(false)
  const [showWalletOptions, setShowWalletOptions] = useState(false)
  const [mounted, setMounted] = useState(false)
  const [connectionError, setConnectionError] = useState<string | null>(null)

  // Fix hydration mismatch
  useEffect(() => {
    setMounted(true)
    console.log('WalletConnect mounted')
    console.log('Available connectors:', connectors)
  }, [])

  // Debug connection state changes
  useEffect(() => {
    console.log('Connection state changed:', { isConnected, address })
  }, [isConnected, address])

  // Debug errors
  useEffect(() => {
    if (error) {
      console.error('Wagmi connection error:', error)
    }
  }, [error])

  // Check if user exists when wallet connects
  useEffect(() => {
    if (isConnected && address) {
      checkUserExists()
    }
  }, [isConnected, address])

  const checkUserExists = async () => {
    if (!address) return
    
    setLoading(true)
    try {
      const { data, error } = await dbHelpers.getUser(address)
      
      if (error && error.code !== 'PGRST116') {
        console.error('Error checking user:', error)
        return
      }
      
      if (data) {
        // User exists
        setUser(data)
        setShowUserForm(false)
      } else {
        // User doesn't exist, show form
        setShowUserForm(true)
      }
    } catch (error) {
      console.error('Error:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleUserCreated = (userData: any) => {
    setUser(userData)
    setShowUserForm(false)
  }

  const handleDisconnect = () => {
    disconnect()
    setUser(null)
    setShowUserForm(false)
    setShowWalletOptions(false)
  }

  const handleConnectClick = () => {
    setShowWalletOptions(true)
  }



  const handleWalletSelect = async (walletType: string) => {
    try {
      setConnectionError(null)
      console.log('Attempting to connect with:', walletType)

      if (walletType === 'MetaMask') {
        await connectMetaMask()
      } else if (walletType === 'TrustWallet') {
        await connectTrustWallet()
      } else {
        // Fallback to wagmi connector
        const connector = connectors.find(c => c.name.toLowerCase().includes(walletType.toLowerCase()))
        if (connector) {
          await connect({ connector })
        }
      }

      setShowWalletOptions(false)
    } catch (err) {
      console.error('Connection error:', err)
      setConnectionError(`Failed to connect: ${err instanceof Error ? err.message : 'Unknown error'}`)
    }
  }

  const connectMetaMask = async () => {
    if (typeof window !== 'undefined' && window.ethereum) {
      try {
        // Request account access
        const accounts = await window.ethereum.request({
          method: 'eth_requestAccounts'
        })

        // Switch to BSC network
        try {
          await window.ethereum.request({
            method: 'wallet_switchEthereumChain',
            params: [{ chainId: '0x38' }], // BSC Mainnet
          })
        } catch (switchError: any) {
          // If BSC is not added, add it
          if (switchError.code === 4902) {
            await window.ethereum.request({
              method: 'wallet_addEthereumChain',
              params: [{
                chainId: '0x38',
                chainName: 'BSC Mainnet',
                nativeCurrency: {
                  name: 'BNB',
                  symbol: 'BNB',
                  decimals: 18,
                },
                rpcUrls: ['https://bsc-dataseed1.binance.org/'],
                blockExplorerUrls: ['https://bscscan.com/'],
              }],
            })
          }
        }

        console.log('MetaMask connected:', accounts[0])
        // Force wagmi to recognize the connection
        window.location.reload()
      } catch (error) {
        throw new Error('Failed to connect to MetaMask')
      }
    } else {
      throw new Error('MetaMask is not installed. Please install MetaMask extension.')
    }
  }

  const connectTrustWallet = async () => {
    if (typeof window !== 'undefined' && window.ethereum) {
      // Trust Wallet also uses window.ethereum
      await connectMetaMask()
    } else {
      throw new Error('Trust Wallet is not detected. Please open this page in Trust Wallet browser.')
    }
  }

  // Prevent hydration mismatch
  if (!mounted) {
    return (
      <div className="flex items-center justify-center">
        <div className="text-lg">Loading...</div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center">
        <div className="text-lg">Loading...</div>
      </div>
    )
  }

  if (!isConnected) {
    return (
      <div className="text-center">
        <h2 className="text-2xl font-bold mb-6">Connect Your Wallet</h2>
        <p className="text-gray-400 mb-8">
          Connect your wallet to start trading USDT
        </p>

        {!showWalletOptions ? (
          <button
            onClick={handleConnectClick}
            className="btn-primary text-lg px-8 py-4"
          >
            Connect Wallet
          </button>
        ) : (
          <div className="space-y-4 max-w-sm mx-auto">
            <h3 className="text-lg font-semibold mb-4">Choose Your Wallet</h3>

            {/* MetaMask */}
            <button
              onClick={() => handleWalletSelect('MetaMask')}
              className="btn-primary w-full py-3 px-6 flex items-center justify-center gap-3 disabled:opacity-50"
              disabled={isPending}
            >
              <span>🦊</span>
              {isPending ? 'Connecting...' : 'Connect MetaMask'}
            </button>

            {/* Trust Wallet */}
            <button
              onClick={() => handleWalletSelect('TrustWallet')}
              className="btn-primary w-full py-3 px-6 flex items-center justify-center gap-3 disabled:opacity-50"
              disabled={isPending}
            >
              <span>🛡️</span>
              {isPending ? 'Connecting...' : 'Connect Trust Wallet'}
            </button>

            {/* Other Injected Wallets */}
            <button
              onClick={() => handleWalletSelect('Injected')}
              className="btn-primary w-full py-3 px-6 flex items-center justify-center gap-3 disabled:opacity-50"
              disabled={isPending}
            >
              <span>💼</span>
              {isPending ? 'Connecting...' : 'Connect Other Wallet'}
            </button>

            {/* Error display */}
            {(error || connectionError) && (
              <div className="text-red-500 text-sm mt-2 p-2 bg-red-900/20 rounded">
                {connectionError || error?.message}
              </div>
            )}

            <button
              onClick={() => setShowWalletOptions(false)}
              className="text-gray-400 hover:text-white mt-4 w-full text-center"
            >
              ← Back
            </button>
          </div>
        )}
      </div>
    )
  }

  if (showUserForm) {
    return (
      <UserDetailsForm
        walletAddress={address!}
        onUserCreated={handleUserCreated}
      />
    )
  }

  return (
    <div className="text-center">
      <div className="mb-6 p-4 bg-green-900/20 border border-green-500/30 rounded-lg">
        <h2 className="text-2xl font-bold mb-2 text-green-400">✅ Wallet Connected</h2>
        <p className="text-gray-300 text-sm font-mono">
          {address?.slice(0, 8)}...{address?.slice(-6)}
        </p>
        <p className="text-xs text-gray-500 mt-1">BSC Mainnet</p>
      </div>

      {user && (
        <div className="mb-6 p-4 bg-blue-900/20 border border-blue-500/30 rounded-lg">
          <h3 className="text-lg font-semibold mb-2 text-blue-400">Your Payment Details</h3>
          {user.upi_id && (
            <p className="text-sm text-gray-300">UPI: {user.upi_id}</p>
          )}
          {user.bank_details && (
            <p className="text-sm text-gray-300">Bank: {user.bank_details}</p>
          )}
        </div>
      )}

      <div className="space-y-3">
        <button
          onClick={handleDisconnect}
          className="btn-primary bg-red-600 hover:bg-red-700"
        >
          Disconnect Wallet
        </button>

        {user && (
          <p className="text-xs text-gray-500">
            Ready to trade! Use the Buy/Sell buttons below.
          </p>
        )}
      </div>
    </div>
  )
}
