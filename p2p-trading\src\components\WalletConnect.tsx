'use client'

import { useAccount, useConnect, useDisconnect } from 'wagmi'
import { useEffect, useState } from 'react'
import { dbHelpers } from '@/lib/supabase'
import UserDetailsForm from './UserDetailsForm'

export default function WalletConnect() {
  const { address, isConnected } = useAccount()
  const { connect, connectors } = useConnect()
  const { disconnect } = useDisconnect()
  const [user, setUser] = useState<any>(null)
  const [showUserForm, setShowUserForm] = useState(false)
  const [loading, setLoading] = useState(false)
  const [showWalletOptions, setShowWalletOptions] = useState(false)
  const [mounted, setMounted] = useState(false)

  // Fix hydration mismatch
  useEffect(() => {
    setMounted(true)
  }, [])

  // Check if user exists when wallet connects
  useEffect(() => {
    if (isConnected && address) {
      checkUserExists()
    }
  }, [isConnected, address])

  const checkUserExists = async () => {
    if (!address) return
    
    setLoading(true)
    try {
      const { data, error } = await dbHelpers.getUser(address)
      
      if (error && error.code !== 'PGRST116') {
        console.error('Error checking user:', error)
        return
      }
      
      if (data) {
        // User exists
        setUser(data)
        setShowUserForm(false)
      } else {
        // User doesn't exist, show form
        setShowUserForm(true)
      }
    } catch (error) {
      console.error('Error:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleUserCreated = (userData: any) => {
    setUser(userData)
    setShowUserForm(false)
  }

  const handleDisconnect = () => {
    disconnect()
    setUser(null)
    setShowUserForm(false)
    setShowWalletOptions(false)
  }

  const handleConnectClick = () => {
    setShowWalletOptions(true)
  }

  const handleWalletSelect = (connector: any) => {
    connect({ connector })
    setShowWalletOptions(false)
  }

  // Prevent hydration mismatch
  if (!mounted) {
    return (
      <div className="flex items-center justify-center">
        <div className="text-lg">Loading...</div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center">
        <div className="text-lg">Loading...</div>
      </div>
    )
  }

  if (!isConnected) {
    return (
      <div className="text-center">
        <h2 className="text-2xl font-bold mb-6">Connect Your Wallet</h2>
        <p className="text-gray-400 mb-8">
          Connect your wallet to start trading USDT
        </p>

        {!showWalletOptions ? (
          <button
            onClick={handleConnectClick}
            className="btn-primary text-lg px-8 py-4"
          >
            Connect Wallet
          </button>
        ) : (
          <div className="space-y-4 max-w-sm mx-auto">
            <h3 className="text-lg font-semibold mb-4">Choose Your Wallet</h3>
            {connectors.map((connector) => (
              <button
                key={connector.uid}
                onClick={() => handleWalletSelect(connector)}
                className="btn-primary w-full py-3 px-6 flex items-center justify-center gap-3"
                disabled={!connector.ready}
              >
                <span>🦊</span>
                Connect {connector.name}
              </button>
            ))}
            <button
              onClick={() => setShowWalletOptions(false)}
              className="text-gray-400 hover:text-white mt-4 w-full text-center"
            >
              ← Back
            </button>
          </div>
        )}
      </div>
    )
  }

  if (showUserForm) {
    return (
      <UserDetailsForm 
        walletAddress={address!}
        onUserCreated={handleUserCreated}
      />
    )
  }

  return (
    <div className="text-center">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-2">Wallet Connected</h2>
        <p className="text-gray-400 text-sm">
          {address?.slice(0, 6)}...{address?.slice(-4)}
        </p>
      </div>
      
      {user && (
        <div className="mb-6 p-4 bg-gray-800 rounded-lg">
          <h3 className="text-lg font-semibold mb-2">Your Details</h3>
          {user.upi_id && (
            <p className="text-sm text-gray-300">UPI: {user.upi_id}</p>
          )}
          {user.bank_details && (
            <p className="text-sm text-gray-300">Bank: {user.bank_details}</p>
          )}
        </div>
      )}
      
      <button
        onClick={handleDisconnect}
        className="btn-primary bg-red-600 hover:bg-red-700"
      >
        Disconnect Wallet
      </button>
    </div>
  )
}
