{"version": 3, "file": "prepareUserOperation.d.ts", "sourceRoot": "", "sources": ["../../../../account-abstraction/actions/bundler/prepareUserOperation.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,SAAS,CAAA;AAC9C,OAAO,EACL,KAAK,qBAAqB,EAE3B,MAAM,yCAAyC,CAAA;AAChD,OAAO,EACL,KAAK,2BAA2B,EAEjC,MAAM,+CAA+C,CAAA;AAEtD,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,kCAAkC,CAAA;AAC9D,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,gDAAgD,CAAA;AAE/E,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,0BAA0B,CAAA;AACzD,OAAO,KAAK,EAAQ,KAAK,EAAE,MAAM,yBAAyB,CAAA;AAC1D,OAAO,KAAK,EAAE,KAAK,EAAE,MAAM,yBAAyB,CAAA;AACpD,OAAO,KAAK,EAAE,GAAG,EAAE,MAAM,wBAAwB,CAAA;AACjD,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,iCAAiC,CAAA;AACpE,OAAO,KAAK,EACV,MAAM,EACN,KAAK,EACL,QAAQ,EACR,SAAS,EACV,MAAM,yBAAyB,CAAA;AAChC,OAAO,EACL,KAAK,2BAA2B,EAEjC,MAAM,0CAA0C,CAAA;AACjD,OAAO,EAAE,KAAK,eAAe,EAAU,MAAM,+BAA+B,CAAA;AAG5E,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,yBAAyB,CAAA;AAE3D,OAAO,KAAK,EAAE,gBAAgB,EAAE,MAAM,uCAAuC,CAAA;AAC7E,OAAO,KAAK,EACV,kBAAkB,EAClB,wBAAwB,EACzB,MAAM,wBAAwB,CAAA;AAC/B,OAAO,KAAK,EACV,uBAAuB,EACvB,iBAAiB,EAClB,MAAM,kCAAkC,CAAA;AACzC,OAAO,KAAK,EACV,aAAa,EACb,oBAAoB,EACrB,MAAM,8BAA8B,CAAA;AACrC,OAAO,EACL,KAAK,yBAAyB,EAE/B,MAAM,kCAAkC,CAAA;AACzC,OAAO,EACL,KAAK,6BAA6B,EAEnC,MAAM,sCAAsC,CAAA;AAM7C,QAAA,MAAM,iBAAiB,wEAOb,CAAA;AAEV,MAAM,MAAM,iCAAiC,GACzC,SAAS,GACT,MAAM,GACN,KAAK,GACL,WAAW,GACX,OAAO,GACP,WAAW,CAAA;AAEf,KAAK,iBAAiB,CACpB,iBAAiB,SAAS,iBAAiB,GAAG,iBAAiB,IAE7D,CAAC,iBAAiB,SAAS,KAAK,GAC5B;IACE,OAAO,EAAE,aAAa,CAAC,SAAS,CAAC,CAAA;IACjC,WAAW,EAAE,aAAa,CAAC,aAAa,CAAC,CAAA;CAC1C,GACD,KAAK,CAAC,GACV,CAAC,iBAAiB,SAAS,KAAK,GAC5B;IACE,QAAQ,EAAE,aAAa,CAAC,UAAU,CAAC,CAAA;CACpC,GACD,KAAK,CAAC,CAAA;AAEd,KAAK,aAAa,CAChB,iBAAiB,SAAS,iBAAiB,GAAG,iBAAiB,IAE7D,CAAC,iBAAiB,SAAS,KAAK,GAC5B;IACE,YAAY,EAAE,aAAa,CAAC,cAAc,CAAC,CAAA;IAC3C,kBAAkB,EAAE,aAAa,CAAC,oBAAoB,CAAC,CAAA;IACvD,oBAAoB,EAAE,aAAa,CAAC,sBAAsB,CAAC,CAAA;IAC3D,uBAAuB,EAAE,aAAa,CAAC,yBAAyB,CAAC,CAAA;IACjE,6BAA6B,EAAE,aAAa,CAAC,+BAA+B,CAAC,CAAA;CAC9E,GACD,KAAK,CAAC,GACV,CAAC,iBAAiB,SAAS,KAAK,GAC5B;IACE,YAAY,EAAE,aAAa,CAAC,cAAc,CAAC,CAAA;IAC3C,kBAAkB,EAAE,aAAa,CAAC,oBAAoB,CAAC,CAAA;IACvD,oBAAoB,EAAE,aAAa,CAAC,sBAAsB,CAAC,CAAA;CAC5D,GACD,KAAK,CAAC,CAAA;AAEd,KAAK,aAAa,GAAG;IACnB,YAAY,EAAE,aAAa,CAAC,cAAc,CAAC,CAAA;IAC3C,oBAAoB,EAAE,aAAa,CAAC,sBAAsB,CAAC,CAAA;CAC5D,CAAA;AAED,KAAK,eAAe,GAAG;IACrB,KAAK,EAAE,aAAa,CAAC,OAAO,CAAC,CAAA;CAC9B,CAAA;AAED,KAAK,mBAAmB,CACtB,iBAAiB,SAAS,iBAAiB,GAAG,iBAAiB,IAE7D,CAAC,iBAAiB,SAAS,KAAK,GAC5B;IACE,SAAS,EAAE,aAAa,CAAC,WAAW,CAAC,CAAA;IACrC,aAAa,EAAE,aAAa,CAAC,eAAe,CAAC,CAAA;IAC7C,uBAAuB,EAAE,aAAa,CAAC,yBAAyB,CAAC,CAAA;IACjE,6BAA6B,EAAE,aAAa,CAAC,+BAA+B,CAAC,CAAA;CAC9E,GACD,KAAK,CAAC,GACV,CAAC,iBAAiB,SAAS,KAAK,GAC5B;IACE,gBAAgB,EAAE,aAAa,CAAC,kBAAkB,CAAC,CAAA;CACpD,GACD,KAAK,CAAC,CAAA;AAEd,KAAK,mBAAmB,GAAG;IACzB,SAAS,EAAE,aAAa,CAAC,WAAW,CAAC,CAAA;CACtC,CAAA;AAED,MAAM,MAAM,2BAA2B,CACrC,OAAO,SAAS,YAAY,GAAG,SAAS,GAAG,YAAY,GAAG,SAAS,EACnE,eAAe,SAAS,YAAY,GAAG,SAAS,GAAG,YAAY,GAAG,SAAS,EAC3E,KAAK,SAAS,SAAS,OAAO,EAAE,GAAG,SAAS,OAAO,EAAE,EAErD,eAAe,SAAS,YAAY,GAAG,SAAS,GAAG,kBAAkB,CACnE,OAAO,EACP,eAAe,CAChB,EACD,eAAe,SACb,iBAAiB,GAAG,uBAAuB,CAAC,eAAe,CAAC,IAC5D,MAAM,CACR,oBAAoB,CAAC,eAAe,CAAC,EACrC,KAAK,CAAC;IAAE,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAA;CAAE,GAAG;IAAE,QAAQ,EAAE,GAAG,CAAA;CAAE,CAAC,GAAG;IAC3D,UAAU,CAAC,EAAE,SAAS,iCAAiC,EAAE,GAAG,SAAS,CAAA;IACrE,SAAS,CAAC,EACN,OAAO,GACP,IAAI,GACJ;QACE,uGAAuG;QACvG,gBAAgB,CAAC,EAAE,gBAAgB,CAAC,kBAAkB,CAAC,GAAG,SAAS,CAAA;QACnE,2FAA2F;QAC3F,oBAAoB,CAAC,EACjB,gBAAgB,CAAC,sBAAsB,CAAC,GACxC,SAAS,CAAA;KACd,GACD,SAAS,CAAA;IACb,wFAAwF;IACxF,gBAAgB,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;IACtC,mDAAmD;IACnD,aAAa,CAAC,EAAE,aAAa,GAAG,SAAS,CAAA;CAC1C,CACF,CAAA;AAED,MAAM,MAAM,8BAA8B,CACxC,OAAO,SAAS,YAAY,GAAG,SAAS,GAAG,YAAY,GAAG,SAAS,EACnE,eAAe,SAAS,YAAY,GAAG,SAAS,GAAG,YAAY,GAAG,SAAS,EAC3E,KAAK,SAAS,SAAS,OAAO,EAAE,GAAG,SAAS,OAAO,EAAE,EACrD,OAAO,SAAS,2BAA2B,CACzC,OAAO,EACP,eAAe,EACf,KAAK,CACN,GAAG,2BAA2B,CAAC,OAAO,EAAE,eAAe,EAAE,KAAK,CAAC,IAC9D,OAAO,GAAG,wBAAwB,CAAC,OAAO,EAAE,eAAe,CAAC,CAAA;AAEhE,MAAM,MAAM,8BAA8B,CACxC,OAAO,SAAS,YAAY,GAAG,SAAS,GAAG,YAAY,GAAG,SAAS,EACnE,eAAe,SAAS,YAAY,GAAG,SAAS,GAAG,YAAY,GAAG,SAAS,EAC3E,KAAK,SAAS,SAAS,OAAO,EAAE,GAAG,SAAS,OAAO,EAAE,EACrD,OAAO,SAAS,2BAA2B,CACzC,OAAO,EACP,eAAe,EACf,KAAK,CACN,GAAG,2BAA2B,CAAC,OAAO,EAAE,eAAe,EAAE,KAAK,CAAC,EAEhE,WAAW,SACT,iCAAiC,GAAG,OAAO,CAAC,YAAY,CAAC,SAAS,SAAS,iCAAiC,EAAE,GAC5G,OAAO,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC,GAC7B,CAAC,OAAO,iBAAiB,CAAC,CAAC,MAAM,CAAC,EACtC,eAAe,SAAS,YAAY,GAAG,SAAS,GAAG,kBAAkB,CACnE,OAAO,EACP,eAAe,CAChB,EACD,eAAe,SACb,iBAAiB,GAAG,uBAAuB,CAAC,eAAe,CAAC,IAC5D,QAAQ,CACV,SAAS,CAAC,OAAO,EAAE,OAAO,GAAG,YAAY,CAAC,GAAG;IAC3C,QAAQ,EAAE,GAAG,CAAA;IACb,gBAAgB,EAAE,eAAe,SAAS,KAAK,GAAG,GAAG,GAAG,SAAS,CAAA;IACjE,MAAM,EAAE,aAAa,CAAC,QAAQ,CAAC,CAAA;CAChC,GAAG,CAAC,OAAO,CAAC,WAAW,EAAE,SAAS,CAAC,SAAS,KAAK,GAC5C,EAAE,GACF,iBAAiB,CAAC,eAAe,CAAC,CAAC,GACvC,CAAC,OAAO,CAAC,WAAW,EAAE,OAAO,CAAC,SAAS,KAAK,GAAG,EAAE,GAAG,eAAe,CAAC,GACpE,CAAC,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC,SAAS,KAAK,GAAG,EAAE,GAAG,aAAa,CAAC,GACjE,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,CAAC,SAAS,KAAK,GACtC,EAAE,GACF,aAAa,CAAC,eAAe,CAAC,CAAC,GACnC,CAAC,OAAO,CAAC,WAAW,EAAE,WAAW,CAAC,SAAS,KAAK,GAC5C,EAAE,GACF,mBAAmB,CAAC,eAAe,CAAC,CAAC,GACzC,CAAC,OAAO,CAAC,WAAW,EAAE,WAAW,CAAC,SAAS,KAAK,GAAG,EAAE,GAAG,mBAAmB,CAAC,CAC/E,CAAA;AAED,MAAM,MAAM,6BAA6B,GACrC,qBAAqB,GACrB,6BAA6B,GAC7B,yBAAyB,GACzB,2BAA2B,GAC3B,eAAe,GACf,2BAA2B,GAC3B,SAAS,CAAA;AAEb;;;;;;;;;;;;;;;;;;;;;;;;;GAyBG;AACH,wBAAsB,oBAAoB,CACxC,OAAO,SAAS,YAAY,GAAG,SAAS,EACxC,KAAK,CAAC,KAAK,SAAS,SAAS,OAAO,EAAE,EACtC,KAAK,CAAC,OAAO,SAAS,2BAA2B,CAC/C,OAAO,EACP,eAAe,EACf,KAAK,CACN,EACD,eAAe,SAAS,YAAY,GAAG,SAAS,GAAG,SAAS,EAE5D,MAAM,EAAE,MAAM,CAAC,SAAS,EAAE,KAAK,GAAG,SAAS,EAAE,OAAO,CAAC,EACrD,WAAW,EAAE,8BAA8B,CACzC,OAAO,EACP,eAAe,EACf,KAAK,EACL,OAAO,CACR,GACA,OAAO,CACR,8BAA8B,CAAC,OAAO,EAAE,eAAe,EAAE,KAAK,EAAE,OAAO,CAAC,CACzE,CAsXA"}