'use client'

import { useAccount } from 'wagmi'
import WalletConnect from '@/components/WalletConnect'
import { useEffect, useState } from 'react'

export default function Home() {
  const { isConnected } = useAccount()
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center p-8">
        <div className="text-center max-w-4xl mx-auto">
          <h1 className="text-6xl font-bold mb-4">
            <span className="accent-primary">Srd</span>
            <span className="accent-secondary">.Exchange</span>
          </h1>
          <p className="text-xl mb-8 text-gray-300">
            Secure P2P USDT Trading Platform
          </p>
          <p className="text-lg mb-12 text-gray-400">
            Buy and sell USDT with INR through our secure, admin-verified platform
          </p>
          <div className="text-lg">Loading...</div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex flex-col items-center justify-center p-8">
      {/* Hero Section */}
      <div className="text-center max-w-4xl mx-auto">
        <h1 className="text-6xl font-bold mb-4">
          <span className="accent-primary">Srd</span>
          <span className="accent-secondary">.Exchange</span>
        </h1>
        <p className="text-xl mb-8 text-gray-300">
          Secure P2P USDT Trading Platform
        </p>
        <p className="text-lg mb-12 text-gray-400">
          Buy and sell USDT with INR through our secure, admin-verified platform
        </p>

        {/* Wallet Connection Section */}
        <div className="mb-12">
          <WalletConnect />
        </div>

        {/* Trading Section - Only show when wallet is connected */}
        {isConnected && (
          <div className="flex justify-center gap-4 mb-8">
            <button className="btn-primary px-6 py-3">
              Buy USDT
            </button>
            <button className="btn-primary px-6 py-3">
              Sell USDT
            </button>
          </div>
        )}

        {/* Features */}
        <div className="grid md:grid-cols-3 gap-8 mt-16">
          <div className="p-6 border border-gray-700 rounded-lg">
            <h3 className="text-xl font-semibold mb-3 accent-primary">Secure Trading</h3>
            <p className="text-gray-400">All transactions are verified by our admin team</p>
          </div>
          <div className="p-6 border border-gray-700 rounded-lg">
            <h3 className="text-xl font-semibold mb-3 accent-secondary">Fast Processing</h3>
            <p className="text-gray-400">Quick verification and USDT release</p>
          </div>
          <div className="p-6 border border-gray-700 rounded-lg">
            <h3 className="text-xl font-semibold mb-3 accent-primary">Wallet Integration</h3>
            <p className="text-gray-400">Connect with Metamask or Trust Wallet</p>
          </div>
        </div>
      </div>
    </div>
  );
}
