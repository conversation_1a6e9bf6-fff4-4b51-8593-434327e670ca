-- P2P Trading Platform Database Schema
-- Run this script in your Supabase SQL Editor

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 1. Create users table
CREATE TABLE IF NOT EXISTS users (
    wallet VARCHAR(42) PRIMARY KEY, -- Ethereum wallet address
    upi_id VARCHAR(255),
    bank_details TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Create orders table
CREATE TABLE IF NOT EXISTS orders (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    type VARCHAR(10) NOT NULL CHECK (type IN ('buy', 'sell')),
    amount DECIMAL(18, 6) NOT NULL CHECK (amount > 0),
    rate DECIMAL(10, 2) NOT NULL CHECK (rate > 0),
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'cancelled')),
    user_wallet VARCHAR(42) NOT NULL REFERENCES users(wallet),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Create proofs table
CREATE TABLE IF NOT EXISTS proofs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    order_id UUID NOT NULL REFERENCES orders(id),
    buyer_wallet VARCHAR(42) NOT NULL REFERENCES users(wallet),
    proof_url TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_orders_user_wallet ON orders(user_wallet);
CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status);
CREATE INDEX IF NOT EXISTS idx_orders_type ON orders(type);
CREATE INDEX IF NOT EXISTS idx_orders_created_at ON orders(created_at);
CREATE INDEX IF NOT EXISTS idx_proofs_order_id ON proofs(order_id);
CREATE INDEX IF NOT EXISTS idx_proofs_buyer_wallet ON proofs(buyer_wallet);

-- Enable Row Level Security (RLS)
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE proofs ENABLE ROW LEVEL SECURITY;

-- RLS Policies for users table (simplified for wallet-based auth)
CREATE POLICY "Anyone can view users" ON users
    FOR SELECT USING (true);

CREATE POLICY "Anyone can insert users" ON users
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Anyone can update users" ON users
    FOR UPDATE USING (true);

-- RLS Policies for orders table
CREATE POLICY "Anyone can view orders" ON orders
    FOR SELECT USING (true);

CREATE POLICY "Anyone can insert orders" ON orders
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Anyone can update orders" ON orders
    FOR UPDATE USING (true);

-- RLS Policies for proofs table
CREATE POLICY "Anyone can view proofs" ON proofs
    FOR SELECT USING (true);

CREATE POLICY "Anyone can insert proofs" ON proofs
    FOR INSERT WITH CHECK (true);

-- Create storage bucket for payment proofs
INSERT INTO storage.buckets (id, name, public) 
VALUES ('payment_proofs', 'payment_proofs', false)
ON CONFLICT (id) DO NOTHING;

-- Storage policy for payment proofs
CREATE POLICY "Users can upload payment proofs" ON storage.objects
    FOR INSERT WITH CHECK (bucket_id = 'payment_proofs');

CREATE POLICY "Users can view payment proofs" ON storage.objects
    FOR SELECT USING (bucket_id = 'payment_proofs');
