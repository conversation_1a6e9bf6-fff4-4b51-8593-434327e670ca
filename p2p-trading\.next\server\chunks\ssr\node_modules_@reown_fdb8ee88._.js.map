{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "file": "index.es.js", "sources": ["file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/node_modules/%40reown/appkit/node_modules/%40walletconnect/universal-provider/src/constants/values.ts", "file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/node_modules/%40reown/appkit/node_modules/%40walletconnect/universal-provider/src/constants/events.ts", "file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/node_modules/%40reown/appkit/node_modules/node_modules/es-toolkit/dist/function/noop.mjs", "file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/node_modules/%40reown/appkit/node_modules/node_modules/es-toolkit/dist/predicate/isPrimitive.mjs", "file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/node_modules/%40reown/appkit/node_modules/node_modules/es-toolkit/dist/predicate/isTypedArray.mjs", "file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/node_modules/%40reown/appkit/node_modules/node_modules/es-toolkit/dist/object/clone.mjs", "file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/node_modules/%40reown/appkit/node_modules/node_modules/es-toolkit/dist/compat/predicate/isObjectLike.mjs", "file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/node_modules/%40reown/appkit/node_modules/node_modules/es-toolkit/dist/compat/_internal/getSymbols.mjs", "file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/node_modules/%40reown/appkit/node_modules/node_modules/es-toolkit/dist/compat/_internal/getTag.mjs", "file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/node_modules/%40reown/appkit/node_modules/node_modules/es-toolkit/dist/compat/_internal/tags.mjs", "file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/node_modules/%40reown/appkit/node_modules/node_modules/es-toolkit/dist/object/cloneDeepWith.mjs", "file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/node_modules/%40reown/appkit/node_modules/node_modules/es-toolkit/dist/compat/object/cloneDeepWith.mjs", "file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/node_modules/%40reown/appkit/node_modules/node_modules/es-toolkit/dist/compat/object/cloneDeep.mjs", "file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/node_modules/%40reown/appkit/node_modules/node_modules/es-toolkit/dist/compat/predicate/isArguments.mjs", "file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/node_modules/%40reown/appkit/node_modules/node_modules/es-toolkit/dist/compat/predicate/isTypedArray.mjs", "file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/node_modules/%40reown/appkit/node_modules/node_modules/es-toolkit/dist/compat/predicate/isPlainObject.mjs", "file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/node_modules/%40reown/appkit/node_modules/node_modules/es-toolkit/dist/compat/object/mergeWith.mjs", "file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/node_modules/%40reown/appkit/node_modules/node_modules/es-toolkit/dist/compat/object/merge.mjs", "file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/node_modules/%40reown/appkit/node_modules/%40walletconnect/universal-provider/src/utils/misc.ts", "file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/node_modules/%40reown/appkit/node_modules/%40walletconnect/universal-provider/src/utils/globals.ts", "file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/node_modules/%40reown/appkit/node_modules/%40walletconnect/universal-provider/src/providers/polkadot.ts", "file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/node_modules/%40reown/appkit/node_modules/%40walletconnect/universal-provider/src/providers/eip155.ts", "file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/node_modules/%40reown/appkit/node_modules/%40walletconnect/universal-provider/src/providers/solana.ts", "file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/node_modules/%40reown/appkit/node_modules/%40walletconnect/universal-provider/src/providers/cosmos.ts", "file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/node_modules/%40reown/appkit/node_modules/%40walletconnect/universal-provider/src/providers/algorand.ts", "file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/node_modules/%40reown/appkit/node_modules/%40walletconnect/universal-provider/src/providers/cardano.ts", "file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/node_modules/%40reown/appkit/node_modules/%40walletconnect/universal-provider/src/providers/elrond.ts", "file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/node_modules/%40reown/appkit/node_modules/%40walletconnect/universal-provider/src/providers/multiversx.ts", "file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/node_modules/%40reown/appkit/node_modules/%40walletconnect/universal-provider/src/providers/near.ts", "file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/node_modules/%40reown/appkit/node_modules/%40walletconnect/universal-provider/src/providers/tezos.ts", "file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/node_modules/%40reown/appkit/node_modules/%40walletconnect/universal-provider/src/providers/generic.ts", "file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/node_modules/%40reown/appkit/node_modules/%40walletconnect/universal-provider/src/UniversalProvider.ts", "file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/node_modules/%40reown/appkit/node_modules/%40walletconnect/universal-provider/src/index.ts"], "sourcesContent": ["export const LOGGER = \"error\";\n\nexport const RELAY_URL = \"wss://relay.walletconnect.org\";\n\nexport const PROTOCOL = \"wc\";\nexport const WC_VERSION = 2;\nexport const CONTEXT = \"universal_provider\";\n\nexport const STORAGE = `${PROTOCOL}@${WC_VERSION}:${CONTEXT}:`;\n\nexport const RPC_URL = \"https://rpc.walletconnect.org/v1/\";\n\nexport const GENERIC_SUBPROVIDER_NAME = \"generic\";\n\nexport const BUNDLER_URL = `${RPC_URL}bundler`;\n", "export const PROVIDER_EVENTS = {\n  DEFAULT_CHAIN_CHANGED: \"default_chain_changed\",\n};\n", "function noop() { }\n\nexport { noop };\n", "function isPrimitive(value) {\n    return value == null || (typeof value !== 'object' && typeof value !== 'function');\n}\n\nexport { isPrimitive };\n", "function isTypedArray(x) {\n    return ArrayBuffer.isView(x) && !(x instanceof DataView);\n}\n\nexport { isTypedArray };\n", "import { isPrimitive } from '../predicate/isPrimitive.mjs';\nimport { isTypedArray } from '../predicate/isTypedArray.mjs';\n\nfunction clone(obj) {\n    if (isPrimitive(obj)) {\n        return obj;\n    }\n    if (Array.isArray(obj) ||\n        isTypedArray(obj) ||\n        obj instanceof ArrayBuffer ||\n        (typeof SharedArrayBuffer !== 'undefined' && obj instanceof SharedArrayBuffer)) {\n        return obj.slice(0);\n    }\n    const prototype = Object.getPrototypeOf(obj);\n    const Constructor = prototype.constructor;\n    if (obj instanceof Date || obj instanceof Map || obj instanceof Set) {\n        return new Constructor(obj);\n    }\n    if (obj instanceof RegExp) {\n        const newRegExp = new Constructor(obj);\n        newRegExp.lastIndex = obj.lastIndex;\n        return newRegExp;\n    }\n    if (obj instanceof DataView) {\n        return new Constructor(obj.buffer.slice(0));\n    }\n    if (obj instanceof Error) {\n        const newError = new Constructor(obj.message);\n        newError.stack = obj.stack;\n        newError.name = obj.name;\n        newError.cause = obj.cause;\n        return newError;\n    }\n    if (typeof File !== 'undefined' && obj instanceof File) {\n        const newFile = new Constructor([obj], obj.name, { type: obj.type, lastModified: obj.lastModified });\n        return newFile;\n    }\n    if (typeof obj === 'object') {\n        const newObject = Object.create(prototype);\n        return Object.assign(newObject, obj);\n    }\n    return obj;\n}\n\nexport { clone };\n", "function isObjectLike(value) {\n    return typeof value === 'object' && value !== null;\n}\n\nexport { isObjectLike };\n", "function getSymbols(object) {\n    return Object.getOwnPropertySymbols(object).filter(symbol => Object.prototype.propertyIsEnumerable.call(object, symbol));\n}\n\nexport { getSymbols };\n", "function getTag(value) {\n    if (value == null) {\n        return value === undefined ? '[object Undefined]' : '[object Null]';\n    }\n    return Object.prototype.toString.call(value);\n}\n\nexport { getTag };\n", "const regexpTag = '[object RegExp]';\nconst stringTag = '[object String]';\nconst numberTag = '[object Number]';\nconst booleanTag = '[object Boolean]';\nconst argumentsTag = '[object Arguments]';\nconst symbolTag = '[object Symbol]';\nconst dateTag = '[object Date]';\nconst mapTag = '[object Map]';\nconst setTag = '[object Set]';\nconst arrayTag = '[object Array]';\nconst functionTag = '[object Function]';\nconst arrayBufferTag = '[object ArrayBuffer]';\nconst objectTag = '[object Object]';\nconst errorTag = '[object Error]';\nconst dataViewTag = '[object DataView]';\nconst uint8ArrayTag = '[object Uint8Array]';\nconst uint8ClampedArrayTag = '[object Uint8ClampedArray]';\nconst uint16ArrayTag = '[object Uint16Array]';\nconst uint32ArrayTag = '[object Uint32Array]';\nconst bigUint64ArrayTag = '[object BigUint64Array]';\nconst int8ArrayTag = '[object Int8Array]';\nconst int16ArrayTag = '[object Int16Array]';\nconst int32ArrayTag = '[object Int32Array]';\nconst bigInt64ArrayTag = '[object BigInt64Array]';\nconst float32ArrayTag = '[object Float32Array]';\nconst float64ArrayTag = '[object Float64Array]';\n\nexport { argumentsTag, arrayBufferTag, arrayTag, bigInt64ArrayTag, bigUint64ArrayTag, booleanTag, dataViewTag, dateTag, errorTag, float32ArrayTag, float64ArrayTag, functionTag, int16ArrayTag, int32ArrayTag, int8ArrayTag, mapTag, numberTag, objectTag, regexpTag, setTag, stringTag, symbolTag, uint16ArrayTag, uint32ArrayTag, uint8ArrayTag, uint8ClampedArrayTag };\n", "import { getSymbols } from '../compat/_internal/getSymbols.mjs';\nimport { getTag } from '../compat/_internal/getTag.mjs';\nimport { uint32ArrayTag, uint16ArrayTag, uint8ClampedArrayTag, uint8ArrayTag, symbolTag, stringTag, setTag, regexpTag, objectTag, numberTag, mapTag, int32ArrayTag, int16ArrayTag, int8ArrayTag, float64ArrayTag, float32ArrayTag, dateTag, booleanTag, dataViewTag, arrayBufferTag, arrayTag, argumentsTag } from '../compat/_internal/tags.mjs';\nimport { isPrimitive } from '../predicate/isPrimitive.mjs';\nimport { isTypedArray } from '../predicate/isTypedArray.mjs';\n\nfunction cloneDeepWith(obj, cloneValue) {\n    return cloneDeepWithImpl(obj, undefined, obj, new Map(), cloneValue);\n}\nfunction cloneDeepWithImpl(valueToClone, keyToClone, objectToClone, stack = new Map(), cloneValue = undefined) {\n    const cloned = cloneValue?.(valueToClone, keyToClone, objectToClone, stack);\n    if (cloned != null) {\n        return cloned;\n    }\n    if (isPrimitive(valueToClone)) {\n        return valueToClone;\n    }\n    if (stack.has(valueToClone)) {\n        return stack.get(valueToClone);\n    }\n    if (Array.isArray(valueToClone)) {\n        const result = new Array(valueToClone.length);\n        stack.set(valueToClone, result);\n        for (let i = 0; i < valueToClone.length; i++) {\n            result[i] = cloneDeepWithImpl(valueToClone[i], i, objectToClone, stack, cloneValue);\n        }\n        if (Object.hasOwn(valueToClone, 'index')) {\n            result.index = valueToClone.index;\n        }\n        if (Object.hasOwn(valueToClone, 'input')) {\n            result.input = valueToClone.input;\n        }\n        return result;\n    }\n    if (valueToClone instanceof Date) {\n        return new Date(valueToClone.getTime());\n    }\n    if (valueToClone instanceof RegExp) {\n        const result = new RegExp(valueToClone.source, valueToClone.flags);\n        result.lastIndex = valueToClone.lastIndex;\n        return result;\n    }\n    if (valueToClone instanceof Map) {\n        const result = new Map();\n        stack.set(valueToClone, result);\n        for (const [key, value] of valueToClone) {\n            result.set(key, cloneDeepWithImpl(value, key, objectToClone, stack, cloneValue));\n        }\n        return result;\n    }\n    if (valueToClone instanceof Set) {\n        const result = new Set();\n        stack.set(valueToClone, result);\n        for (const value of valueToClone) {\n            result.add(cloneDeepWithImpl(value, undefined, objectToClone, stack, cloneValue));\n        }\n        return result;\n    }\n    if (typeof Buffer !== 'undefined' && Buffer.isBuffer(valueToClone)) {\n        return valueToClone.subarray();\n    }\n    if (isTypedArray(valueToClone)) {\n        const result = new (Object.getPrototypeOf(valueToClone).constructor)(valueToClone.length);\n        stack.set(valueToClone, result);\n        for (let i = 0; i < valueToClone.length; i++) {\n            result[i] = cloneDeepWithImpl(valueToClone[i], i, objectToClone, stack, cloneValue);\n        }\n        return result;\n    }\n    if (valueToClone instanceof ArrayBuffer ||\n        (typeof SharedArrayBuffer !== 'undefined' && valueToClone instanceof SharedArrayBuffer)) {\n        return valueToClone.slice(0);\n    }\n    if (valueToClone instanceof DataView) {\n        const result = new DataView(valueToClone.buffer.slice(0), valueToClone.byteOffset, valueToClone.byteLength);\n        stack.set(valueToClone, result);\n        copyProperties(result, valueToClone, objectToClone, stack, cloneValue);\n        return result;\n    }\n    if (typeof File !== 'undefined' && valueToClone instanceof File) {\n        const result = new File([valueToClone], valueToClone.name, {\n            type: valueToClone.type,\n        });\n        stack.set(valueToClone, result);\n        copyProperties(result, valueToClone, objectToClone, stack, cloneValue);\n        return result;\n    }\n    if (valueToClone instanceof Blob) {\n        const result = new Blob([valueToClone], { type: valueToClone.type });\n        stack.set(valueToClone, result);\n        copyProperties(result, valueToClone, objectToClone, stack, cloneValue);\n        return result;\n    }\n    if (valueToClone instanceof Error) {\n        const result = new valueToClone.constructor();\n        stack.set(valueToClone, result);\n        result.message = valueToClone.message;\n        result.name = valueToClone.name;\n        result.stack = valueToClone.stack;\n        result.cause = valueToClone.cause;\n        copyProperties(result, valueToClone, objectToClone, stack, cloneValue);\n        return result;\n    }\n    if (typeof valueToClone === 'object' && isCloneableObject(valueToClone)) {\n        const result = Object.create(Object.getPrototypeOf(valueToClone));\n        stack.set(valueToClone, result);\n        copyProperties(result, valueToClone, objectToClone, stack, cloneValue);\n        return result;\n    }\n    return valueToClone;\n}\nfunction copyProperties(target, source, objectToClone = target, stack, cloneValue) {\n    const keys = [...Object.keys(source), ...getSymbols(source)];\n    for (let i = 0; i < keys.length; i++) {\n        const key = keys[i];\n        const descriptor = Object.getOwnPropertyDescriptor(target, key);\n        if (descriptor == null || descriptor.writable) {\n            target[key] = cloneDeepWithImpl(source[key], key, objectToClone, stack, cloneValue);\n        }\n    }\n}\nfunction isCloneableObject(object) {\n    switch (getTag(object)) {\n        case argumentsTag:\n        case arrayTag:\n        case arrayBufferTag:\n        case dataViewTag:\n        case booleanTag:\n        case dateTag:\n        case float32ArrayTag:\n        case float64ArrayTag:\n        case int8ArrayTag:\n        case int16ArrayTag:\n        case int32ArrayTag:\n        case mapTag:\n        case numberTag:\n        case objectTag:\n        case regexpTag:\n        case setTag:\n        case stringTag:\n        case symbolTag:\n        case uint8ArrayTag:\n        case uint8ClampedArrayTag:\n        case uint16ArrayTag:\n        case uint32ArrayTag: {\n            return true;\n        }\n        default: {\n            return false;\n        }\n    }\n}\n\nexport { cloneDeepWith, cloneDeepWithImpl, copyProperties };\n", "import { cloneDeep<PERSON>ith as cloneDeep<PERSON>ith$1, copyProperties } from '../../object/cloneDeepWith.mjs';\nimport { argumentsTag, booleanTag, stringTag, numberTag } from '../_internal/tags.mjs';\n\nfunction cloneDeepWith(obj, cloneValue) {\n    return cloneDeepWith$1(obj, (value, key, object, stack) => {\n        const cloned = cloneValue?.(value, key, object, stack);\n        if (cloned != null) {\n            return cloned;\n        }\n        if (typeof obj !== 'object') {\n            return undefined;\n        }\n        switch (Object.prototype.toString.call(obj)) {\n            case numberTag:\n            case stringTag:\n            case booleanTag: {\n                const result = new obj.constructor(obj?.valueOf());\n                copyProperties(result, obj);\n                return result;\n            }\n            case argumentsTag: {\n                const result = {};\n                copyProperties(result, obj);\n                result.length = obj.length;\n                result[Symbol.iterator] = obj[Symbol.iterator];\n                return result;\n            }\n            default: {\n                return undefined;\n            }\n        }\n    });\n}\n\nexport { cloneDeepWith };\n", "import { cloneDeepWith } from './cloneDeepWith.mjs';\n\nfunction cloneDeep(obj) {\n    return cloneDeepWith(obj);\n}\n\nexport { cloneDeep };\n", "import { getTag } from '../_internal/getTag.mjs';\n\nfunction isArguments(value) {\n    return value !== null && typeof value === 'object' && getTag(value) === '[object Arguments]';\n}\n\nexport { isArguments };\n", "import { isTypedArray as isTypedArray$1 } from '../../predicate/isTypedArray.mjs';\n\nfunction isTypedArray(x) {\n    return isTypedArray$1(x);\n}\n\nexport { isTypedArray };\n", "function isPlainObject(object) {\n    if (typeof object !== 'object') {\n        return false;\n    }\n    if (object == null) {\n        return false;\n    }\n    if (Object.getPrototypeOf(object) === null) {\n        return true;\n    }\n    if (Object.prototype.toString.call(object) !== '[object Object]') {\n        const tag = object[Symbol.toStringTag];\n        if (tag == null) {\n            return false;\n        }\n        const isTagReadonly = !Object.getOwnPropertyDescriptor(object, Symbol.toStringTag)?.writable;\n        if (isTagReadonly) {\n            return false;\n        }\n        return object.toString() === `[object ${tag}]`;\n    }\n    let proto = object;\n    while (Object.getPrototypeOf(proto) !== null) {\n        proto = Object.getPrototypeOf(proto);\n    }\n    return Object.getPrototypeOf(object) === proto;\n}\n\nexport { isPlainObject };\n", "import { cloneDeep } from './cloneDeep.mjs';\nimport { clone } from '../../object/clone.mjs';\nimport { isPrimitive } from '../../predicate/isPrimitive.mjs';\nimport { getSymbols } from '../_internal/getSymbols.mjs';\nimport { isArguments } from '../predicate/isArguments.mjs';\nimport { isObjectLike } from '../predicate/isObjectLike.mjs';\nimport { isPlainObject } from '../predicate/isPlainObject.mjs';\nimport { isTypedArray } from '../predicate/isTypedArray.mjs';\n\nfunction mergeWith(object, ...otherArgs) {\n    const sources = otherArgs.slice(0, -1);\n    const merge = otherArgs[otherArgs.length - 1];\n    let result = object;\n    for (let i = 0; i < sources.length; i++) {\n        const source = sources[i];\n        result = mergeWithDeep(result, source, merge, new Map());\n    }\n    return result;\n}\nfunction mergeWithDeep(target, source, merge, stack) {\n    if (isPrimitive(target)) {\n        target = Object(target);\n    }\n    if (source == null || typeof source !== 'object') {\n        return target;\n    }\n    if (stack.has(source)) {\n        return clone(stack.get(source));\n    }\n    stack.set(source, target);\n    if (Array.isArray(source)) {\n        source = source.slice();\n        for (let i = 0; i < source.length; i++) {\n            source[i] = source[i] ?? undefined;\n        }\n    }\n    const sourceKeys = [...Object.keys(source), ...getSymbols(source)];\n    for (let i = 0; i < sourceKeys.length; i++) {\n        const key = sourceKeys[i];\n        let sourceValue = source[key];\n        let targetValue = target[key];\n        if (isArguments(sourceValue)) {\n            sourceValue = { ...sourceValue };\n        }\n        if (isArguments(targetValue)) {\n            targetValue = { ...targetValue };\n        }\n        if (typeof Buffer !== 'undefined' && Buffer.isBuffer(sourceValue)) {\n            sourceValue = cloneDeep(sourceValue);\n        }\n        if (Array.isArray(sourceValue)) {\n            if (typeof targetValue === 'object' && targetValue != null) {\n                const cloned = [];\n                const targetKeys = Reflect.ownKeys(targetValue);\n                for (let i = 0; i < targetKeys.length; i++) {\n                    const targetKey = targetKeys[i];\n                    cloned[targetKey] = targetValue[targetKey];\n                }\n                targetValue = cloned;\n            }\n            else {\n                targetValue = [];\n            }\n        }\n        const merged = merge(targetValue, sourceValue, key, target, source, stack);\n        if (merged != null) {\n            target[key] = merged;\n        }\n        else if (Array.isArray(sourceValue)) {\n            target[key] = mergeWithDeep(targetValue, sourceValue, merge, stack);\n        }\n        else if (isObjectLike(targetValue) && isObjectLike(sourceValue)) {\n            target[key] = mergeWithDeep(targetValue, sourceValue, merge, stack);\n        }\n        else if (targetValue == null && isPlainObject(sourceValue)) {\n            target[key] = mergeWithDeep({}, sourceValue, merge, stack);\n        }\n        else if (targetValue == null && isTypedArray(sourceValue)) {\n            target[key] = cloneDeep(sourceValue);\n        }\n        else if (targetValue === undefined || sourceValue !== undefined) {\n            target[key] = sourceValue;\n        }\n    }\n    return target;\n}\n\nexport { mergeWith };\n", "import { mergeWith } from './mergeWith.mjs';\nimport { noop } from '../../function/noop.mjs';\n\nfunction merge(object, ...sources) {\n    return mergeWith(object, ...sources, noop);\n}\n\nexport { merge };\n", "import { SessionTypes } from \"@walletconnect/types\";\nimport {\n  isCaipNamespace,\n  isValidObject,\n  mergeArrays,\n  parseChainId,\n  parseNamespaceKey,\n} from \"@walletconnect/utils\";\nimport { RPC_URL } from \"../constants\";\nimport { Namespace, NamespaceConfig } from \"../types\";\nimport { merge } from \"es-toolkit/compat\";\n\nexport function getRpcUrl(chainId: string, rpc: Namespace, projectId?: string): string | undefined {\n  const chain = parseChainId(chainId);\n  return (\n    rpc.rpcMap?.[chain.reference] ||\n    `${RPC_URL}?chainId=${chain.namespace}:${chain.reference}&projectId=${projectId}`\n  );\n}\n\nexport function getChainId(chain: string): string {\n  return chain.includes(\":\") ? chain.split(\":\")[1] : chain;\n}\n\nexport function validateChainApproval(chain: string, chains: string[]): void {\n  if (!chains.includes(chain)) {\n    throw new Error(\n      `Chain '${chain}' not approved. Please use one of the following: ${chains.toString()}`,\n    );\n  }\n}\n\nexport function getChainsFromApprovedSession(accounts: string[]): string[] {\n  return accounts.map((address) => `${address.split(\":\")[0]}:${address.split(\":\")[1]}`);\n}\n\nexport function getAccountsFromSession(namespace: string, session: SessionTypes.Struct): string[] {\n  // match namespaces e.g. eip155 with eip155:1\n  const matchedNamespaceKeys = Object.keys(session.namespaces).filter((key) =>\n    key.includes(namespace),\n  );\n  if (!matchedNamespaceKeys.length) return [];\n  const accounts: string[] = [];\n  matchedNamespaceKeys.forEach((key) => {\n    const accountsForNamespace = session.namespaces[key].accounts;\n    accounts.push(...accountsForNamespace);\n  });\n  return accounts;\n}\n\nexport function mergeRequiredOptionalNamespaces(\n  required: NamespaceConfig = {},\n  optional: NamespaceConfig = {},\n) {\n  const requiredNamespaces = normalizeNamespaces(required);\n  const optionalNamespaces = normalizeNamespaces(optional);\n  return merge(requiredNamespaces, optionalNamespaces);\n}\n\n/**\n * Converts\n * {\n *  \"eip155:1\": {...},\n *  \"eip155:2\": {...},\n * }\n * into\n * {\n *  \"eip155\": {\n *      chains: [\"eip155:1\", \"eip155:2\"],\n *      ...\n *    }\n * }\n *\n */\nexport function normalizeNamespaces(namespaces: NamespaceConfig): NamespaceConfig {\n  const normalizedNamespaces: NamespaceConfig = {};\n  if (!isValidObject(namespaces)) return normalizedNamespaces;\n\n  for (const [key, values] of Object.entries(namespaces)) {\n    const chains = isCaipNamespace(key) ? [key] : values.chains;\n    const methods = values.methods || [];\n    const events = values.events || [];\n    const rpcMap = values.rpcMap || {};\n    const normalizedKey = parseNamespaceKey(key);\n    normalizedNamespaces[normalizedKey] = {\n      ...normalizedNamespaces[normalizedKey],\n      ...values,\n      chains: mergeArrays(chains, normalizedNamespaces[normalizedKey]?.chains),\n      methods: mergeArrays(methods, normalizedNamespaces[normalizedKey]?.methods),\n      events: mergeArrays(events, normalizedNamespaces[normalizedKey]?.events),\n    };\n    // avoid adding empty `rpcMap: {}` if there are no values for it\n    if (isValidObject(rpcMap) || isValidObject(normalizedNamespaces[normalizedKey]?.rpcMap || {})) {\n      normalizedNamespaces[normalizedKey].rpcMap = {\n        ...rpcMap,\n        ...normalizedNamespaces[normalizedKey]?.rpcMap,\n      };\n    }\n  }\n  return normalizedNamespaces;\n}\n\nexport function parseCaip10Account(caip10Account: string): string {\n  return caip10Account.includes(\":\") ? caip10Account.split(\":\")[2] : caip10Account;\n}\n\n/**\n * Populates the chains array for each namespace with the chains extracted from the accounts if are otherwise missing\n */\nexport function populateNamespacesChains(\n  namespaces: SessionTypes.Namespaces,\n): Record<string, SessionTypes.Namespace> {\n  const parsedNamespaces: Record<string, SessionTypes.Namespace> = {};\n  for (const [key, values] of Object.entries(namespaces)) {\n    const methods = values.methods || [];\n    const events = values.events || [];\n    const accounts = values.accounts || [];\n    // If the key includes a CAIP separator `:` we know it's a namespace + chainId (e.g. `eip155:1`)\n    const chains = isCaipNamespace(key)\n      ? [key]\n      : values.chains\n        ? values.chains\n        : getChainsFromApprovedSession(values.accounts);\n    parsedNamespaces[key] = {\n      chains,\n      methods,\n      events,\n      accounts,\n    };\n  }\n  return parsedNamespaces;\n}\n\nexport function convertChainIdToNumber(chainId: string | number): number | string {\n  if (typeof chainId === \"number\") return chainId;\n  if (chainId.includes(\"0x\")) {\n    return parseInt(chainId, 16);\n  }\n\n  chainId = chainId.includes(\":\") ? chainId.split(\":\")[1] : chainId;\n  return isNaN(Number(chainId)) ? chainId : Number(chainId);\n}\n", "const globals = {};\nexport const getGlobal = (key: string) => {\n  return globals[key];\n};\n\nexport const setGlobal = (key: string, value: unknown) => {\n  globals[key] = value;\n};\n", "import HttpConnection from \"@walletconnect/jsonrpc-http-connection\";\nimport { JsonRpcProvider } from \"@walletconnect/jsonrpc-provider\";\nimport Client from \"@walletconnect/sign-client\";\nimport { EngineTypes, SessionTypes } from \"@walletconnect/types\";\nimport EventEmitter from \"events\";\nimport { PROVIDER_EVENTS } from \"../constants\";\nimport {\n  IProvider,\n  RequestParams,\n  RpcProvidersMap,\n  SessionNamespace,\n  SubProviderOpts,\n} from \"../types\";\n\nimport { getChainId, getGlobal, getRpcUrl } from \"../utils\";\n\nclass PolkadotProvider implements IProvider {\n  public name = \"polkadot\";\n  public client: Client;\n  public httpProviders: RpcProvidersMap;\n  public events: EventEmitter;\n  public namespace: SessionNamespace;\n  public chainId: string;\n\n  constructor(opts: SubProviderOpts) {\n    this.namespace = opts.namespace;\n    this.events = getGlobal(\"events\");\n    this.client = getGlobal(\"client\");\n    this.chainId = this.getDefaultChain();\n    this.httpProviders = this.createHttpProviders();\n  }\n\n  public updateNamespace(namespace: SessionTypes.Namespace) {\n    this.namespace = Object.assign(this.namespace, namespace);\n  }\n\n  public requestAccounts(): string[] {\n    return this.getAccounts();\n  }\n\n  public getDefaultChain(): string {\n    if (this.chainId) return this.chainId;\n    if (this.namespace.defaultChain) return this.namespace.defaultChain;\n\n    const chainId = this.namespace.chains[0];\n\n    if (!chainId) throw new Error(`ChainId not found`);\n\n    return chainId.split(\":\")[1];\n  }\n\n  public request<T = unknown>(args: RequestParams): Promise<T> {\n    if (this.namespace.methods.includes(args.request.method)) {\n      return this.client.request(args as EngineTypes.RequestParams);\n    }\n    return this.getHttpProvider().request(args.request);\n  }\n\n  public setDefaultChain(chainId: string, rpcUrl?: string | undefined) {\n    // http provider exists so just set the chainId\n    if (!this.httpProviders[chainId]) {\n      this.setHttpProvider(chainId, rpcUrl);\n    }\n    this.chainId = chainId;\n    this.events.emit(PROVIDER_EVENTS.DEFAULT_CHAIN_CHANGED, `${this.name}:${chainId}`);\n  }\n\n  // ---------------- PRIVATE ---------------- //\n\n  private getAccounts(): string[] {\n    const accounts = this.namespace.accounts;\n    if (!accounts) {\n      return [];\n    }\n\n    return (\n      accounts\n        // get the accounts from the active chain\n        .filter((account) => account.split(\":\")[1] === this.chainId.toString())\n        // remove namespace & chainId from the string\n        .map((account) => account.split(\":\")[2]) || []\n    );\n  }\n\n  private createHttpProviders(): RpcProvidersMap {\n    const http = {};\n    this.namespace.chains.forEach((chain) => {\n      const parsedChainId = getChainId(chain);\n      http[parsedChainId] = this.createHttpProvider(parsedChainId, this.namespace.rpcMap?.[chain]);\n    });\n    return http;\n  }\n\n  private getHttpProvider(): JsonRpcProvider {\n    const chain = `${this.name}:${this.chainId}`;\n    const http = this.httpProviders[chain];\n    if (typeof http === \"undefined\") {\n      throw new Error(`JSON-RPC provider for ${chain} not found`);\n    }\n    return http;\n  }\n\n  private setHttpProvider(chainId: string, rpcUrl?: string): void {\n    const http = this.createHttpProvider(chainId, rpcUrl);\n    if (http) {\n      this.httpProviders[chainId] = http;\n    }\n  }\n\n  private createHttpProvider(\n    chainId: string,\n    rpcUrl?: string | undefined,\n  ): JsonRpcProvider | undefined {\n    const rpc = rpcUrl || getRpcUrl(chainId, this.namespace, this.client.core.projectId);\n    if (!rpc) {\n      throw new Error(`No RPC url provided for chainId: ${chainId}`);\n    }\n    const http = new JsonRpcProvider(new HttpConnection(rpc, getGlobal(\"disableProviderPing\")));\n    return http;\n  }\n}\n\nexport default PolkadotProvider;\n", "import Client from \"@walletconnect/sign-client\";\nimport { JsonRpcProvider } from \"@walletconnect/jsonrpc-provider\";\nimport { HttpConnection } from \"@walletconnect/jsonrpc-http-connection\";\nimport { EngineTypes, SessionTypes } from \"@walletconnect/types\";\n\nimport {\n  IProvider,\n  RpcProvidersMap,\n  SubProviderOpts,\n  RequestParams,\n  SessionNamespace,\n} from \"../types\";\n\nimport { getChainId, getGlobal, getRpcUrl } from \"../utils\";\nimport EventEmitter from \"events\";\nimport { BUNDLER_URL, PROVIDER_EVENTS } from \"../constants\";\nimport { formatJsonRpcRequest } from \"@walletconnect/jsonrpc-utils\";\n\nclass Eip155Provider implements IProvider {\n  public name = \"eip155\";\n  public client: Client;\n  // the active chainId on the dapp\n  public chainId: number;\n  public namespace: SessionNamespace;\n  public httpProviders: RpcProvidersMap;\n  public events: EventEmitter;\n\n  constructor(opts: SubProviderOpts) {\n    this.namespace = opts.namespace;\n    this.events = getGlobal(\"events\");\n    this.client = getGlobal(\"client\");\n    this.httpProviders = this.createHttpProviders();\n    this.chainId = parseInt(this.getDefaultChain());\n  }\n\n  public async request<T = unknown>(args: RequestParams): Promise<T> {\n    switch (args.request.method) {\n      case \"eth_requestAccounts\":\n        return this.getAccounts() as unknown as T;\n      case \"eth_accounts\":\n        return this.getAccounts() as unknown as T;\n      case \"wallet_switchEthereumChain\": {\n        return (await this.handleSwitchChain(args)) as unknown as T;\n      }\n      case \"eth_chainId\":\n        return parseInt(this.getDefaultChain()) as unknown as T;\n      case \"wallet_getCapabilities\":\n        return (await this.getCapabilities(args)) as unknown as T;\n      case \"wallet_getCallsStatus\":\n        return (await this.getCallStatus(args)) as unknown as T;\n      default:\n        break;\n    }\n    if (this.namespace.methods.includes(args.request.method)) {\n      return await this.client.request(args as EngineTypes.RequestParams);\n    }\n    return this.getHttpProvider().request(args.request);\n  }\n\n  public updateNamespace(namespace: SessionTypes.Namespace) {\n    this.namespace = Object.assign(this.namespace, namespace);\n  }\n\n  public setDefaultChain(chainId: string, rpcUrl?: string | undefined) {\n    // http provider exists so just set the chainId\n    if (!this.httpProviders[chainId]) {\n      this.setHttpProvider(parseInt(chainId), rpcUrl);\n    }\n    this.chainId = parseInt(chainId);\n    this.events.emit(PROVIDER_EVENTS.DEFAULT_CHAIN_CHANGED, `${this.name}:${chainId}`);\n  }\n\n  public requestAccounts(): string[] {\n    return this.getAccounts();\n  }\n\n  public getDefaultChain(): string {\n    if (this.chainId) return this.chainId.toString();\n    if (this.namespace.defaultChain) return this.namespace.defaultChain;\n\n    const chainId = this.namespace.chains[0];\n    if (!chainId) throw new Error(`ChainId not found`);\n\n    return chainId.split(\":\")[1];\n  }\n\n  // ---------- Private ----------------------------------------------- //\n\n  private createHttpProvider(\n    chainId: number,\n    rpcUrl?: string | undefined,\n  ): JsonRpcProvider | undefined {\n    const rpc =\n      rpcUrl || getRpcUrl(`${this.name}:${chainId}`, this.namespace, this.client.core.projectId);\n    if (!rpc) {\n      throw new Error(`No RPC url provided for chainId: ${chainId}`);\n    }\n    const http = new JsonRpcProvider(new HttpConnection(rpc, getGlobal(\"disableProviderPing\")));\n    return http;\n  }\n\n  private setHttpProvider(chainId: number, rpcUrl?: string): void {\n    const http = this.createHttpProvider(chainId, rpcUrl);\n    if (http) {\n      this.httpProviders[chainId] = http;\n    }\n  }\n\n  private createHttpProviders(): RpcProvidersMap {\n    const http = {};\n    this.namespace.chains.forEach((chain) => {\n      const parsedChain = parseInt(getChainId(chain));\n      http[parsedChain] = this.createHttpProvider(parsedChain, this.namespace.rpcMap?.[chain]);\n    });\n    return http;\n  }\n\n  private getAccounts(): string[] {\n    const accounts = this.namespace.accounts;\n    if (!accounts) {\n      return [];\n    }\n    return [\n      ...new Set(\n        accounts\n          // get the accounts from the active chain\n          .filter((account) => account.split(\":\")[1] === this.chainId.toString())\n          // remove namespace & chainId from the string\n          .map((account) => account.split(\":\")[2]),\n      ),\n    ];\n  }\n\n  private getHttpProvider(): JsonRpcProvider {\n    const chain = this.chainId;\n    const http = this.httpProviders[chain];\n    if (typeof http === \"undefined\") {\n      throw new Error(`JSON-RPC provider for ${chain} not found`);\n    }\n    return http;\n  }\n\n  private async handleSwitchChain(args: RequestParams): Promise<any> {\n    let hexChainId = args.request.params ? args.request.params[0]?.chainId : \"0x0\";\n    hexChainId = hexChainId.startsWith(\"0x\") ? hexChainId : `0x${hexChainId}`;\n    const parsedChainId = parseInt(hexChainId, 16);\n    // if chainId is already approved, switch locally\n    if (this.isChainApproved(parsedChainId)) {\n      this.setDefaultChain(`${parsedChainId}`);\n    } else if (this.namespace.methods.includes(\"wallet_switchEthereumChain\")) {\n      // try to switch chain within the wallet\n      await this.client.request({\n        topic: args.topic,\n        request: {\n          method: args.request.method,\n          params: [\n            {\n              chainId: hexChainId,\n            },\n          ],\n        },\n        chainId: this.namespace.chains?.[0], // Sending a previously unapproved chainId will cause namespace validation failure so we must set request chainId to the first chainId in the namespace to avoid it\n      } as EngineTypes.RequestParams);\n      this.setDefaultChain(`${parsedChainId}`);\n    } else {\n      throw new Error(\n        `Failed to switch to chain 'eip155:${parsedChainId}'. The chain is not approved or the wallet does not support 'wallet_switchEthereumChain' method.`,\n      );\n    }\n    return null;\n  }\n\n  private isChainApproved(chainId: number): boolean {\n    return this.namespace.chains.includes(`${this.name}:${chainId}`);\n  }\n\n  private async getCapabilities(args: RequestParams) {\n    // if capabilities are stored in the session, return them, else send the request to the wallet\n    const address = args.request?.params?.[0];\n    const chainIds: string[] = args.request?.params?.[1] || [];\n\n    // cache key is address + chainIds to allow requests to be made to different chains\n    const capabilitiesKey = `${address}${chainIds.join(\",\")}`;\n    if (!address) throw new Error(\"Missing address parameter in `wallet_getCapabilities` request\");\n    const session = this.client.session.get(args.topic);\n    const sessionCapabilities = session?.sessionProperties?.capabilities || {};\n\n    if (sessionCapabilities?.[capabilitiesKey]) {\n      return sessionCapabilities?.[capabilitiesKey];\n    }\n\n    // intentionally omit catching errors/rejection during `request` to allow the error to bubble up\n    const capabilities = await this.client.request(args as EngineTypes.RequestParams);\n    try {\n      // update the session with the capabilities so they can be retrieved later\n      await this.client.session.update(args.topic, {\n        sessionProperties: {\n          ...(session.sessionProperties || {}),\n          capabilities: {\n            ...(sessionCapabilities || {}),\n            [capabilitiesKey]: capabilities,\n          } as any, // by spec sessionProperties should be <string, string> but here are used as objects?\n        },\n      });\n    } catch (error) {\n      console.warn(\"Failed to update session with capabilities\", error);\n    }\n    return capabilities;\n  }\n\n  private async getCallStatus(args: RequestParams) {\n    const session = this.client.session.get(args.topic);\n    const bundlerName = session.sessionProperties?.bundler_name;\n    if (bundlerName) {\n      const bundlerUrl = this.getBundlerUrl(args.chainId, bundlerName);\n      try {\n        return await this.getUserOperationReceipt(bundlerUrl, args);\n      } catch (error) {\n        console.warn(\"Failed to fetch call status from bundler\", error, bundlerUrl);\n      }\n    }\n    const customUrl = session.sessionProperties?.bundler_url;\n    if (customUrl) {\n      try {\n        return await this.getUserOperationReceipt(customUrl, args);\n      } catch (error) {\n        console.warn(\"Failed to fetch call status from custom bundler\", error, customUrl);\n      }\n    }\n\n    if (this.namespace.methods.includes(args.request.method)) {\n      return await this.client.request(args as EngineTypes.RequestParams);\n    }\n\n    throw new Error(\"Fetching call status not approved by the wallet.\");\n  }\n\n  private async getUserOperationReceipt(bundlerUrl: string, args: RequestParams) {\n    const url = new URL(bundlerUrl);\n    const response = await fetch(url, {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n      },\n      body: JSON.stringify(\n        formatJsonRpcRequest(\"eth_getUserOperationReceipt\", [args.request.params?.[0]]),\n      ),\n    });\n    if (!response.ok) {\n      throw new Error(`Failed to fetch user operation receipt - ${response.status}`);\n    }\n    return await response.json();\n  }\n\n  private getBundlerUrl(cap2ChainId: string, bundlerName: string) {\n    return `${BUNDLER_URL}?projectId=${this.client.core.projectId}&chainId=${cap2ChainId}&bundler=${bundlerName}`;\n  }\n}\n\nexport default Eip155Provider;\n", "import HttpConnection from \"@walletconnect/jsonrpc-http-connection\";\nimport { JsonRpcProvider } from \"@walletconnect/jsonrpc-provider\";\nimport Client from \"@walletconnect/sign-client\";\nimport { EngineTypes, SessionTypes } from \"@walletconnect/types\";\nimport EventEmitter from \"events\";\nimport { PROVIDER_EVENTS } from \"../constants\";\nimport {\n  IProvider,\n  RequestParams,\n  RpcProvidersMap,\n  SessionNamespace,\n  SubProviderOpts,\n} from \"../types\";\nimport { getChainId, getGlobal, getRpcUrl } from \"../utils\";\n\nclass SolanaProvider implements IProvider {\n  public name = \"solana\";\n  public client: Client;\n  public httpProviders: RpcProvidersMap;\n  public events: EventEmitter;\n  public namespace: SessionNamespace;\n  public chainId: string;\n\n  constructor(opts: SubProviderOpts) {\n    this.namespace = opts.namespace;\n    this.events = getGlobal(\"events\");\n    this.client = getGlobal(\"client\");\n    this.chainId = this.getDefaultChain();\n    this.httpProviders = this.createHttpProviders();\n  }\n\n  public updateNamespace(namespace: SessionTypes.Namespace) {\n    this.namespace = Object.assign(this.namespace, namespace);\n  }\n\n  public requestAccounts(): string[] {\n    return this.getAccounts();\n  }\n\n  public request<T = unknown>(args: RequestParams): Promise<T> {\n    if (this.namespace.methods.includes(args.request.method)) {\n      return this.client.request(args as EngineTypes.RequestParams);\n    }\n    return this.getHttpProvider().request(args.request);\n  }\n\n  public setDefaultChain(chainId: string, rpcUrl?: string | undefined) {\n    // http provider exists so just set the chainId\n    if (!this.httpProviders[chainId]) {\n      this.setHttpProvider(chainId, rpcUrl);\n    }\n    this.chainId = chainId;\n    this.events.emit(PROVIDER_EVENTS.DEFAULT_CHAIN_CHANGED, `${this.name}:${chainId}`);\n  }\n\n  public getDefaultChain(): string {\n    if (this.chainId) return this.chainId;\n    if (this.namespace.defaultChain) return this.namespace.defaultChain;\n\n    const chainId = this.namespace.chains[0];\n    if (!chainId) throw new Error(`ChainId not found`);\n\n    return chainId.split(\":\")[1];\n  }\n\n  // --------- PRIVATE --------- //\n\n  private getAccounts(): string[] {\n    const accounts = this.namespace.accounts;\n    if (!accounts) {\n      return [];\n    }\n\n    return [\n      ...new Set(\n        accounts\n          // get the accounts from the active chain\n          .filter((account) => account.split(\":\")[1] === this.chainId.toString())\n          // remove namespace & chainId from the string\n          .map((account) => account.split(\":\")[2]),\n      ),\n    ];\n  }\n\n  private createHttpProviders(): RpcProvidersMap {\n    const http = {};\n    this.namespace.chains.forEach((chain) => {\n      const parsedChainId = getChainId(chain);\n      http[parsedChainId] = this.createHttpProvider(parsedChainId, this.namespace.rpcMap?.[chain]);\n    });\n    return http;\n  }\n\n  private getHttpProvider(): JsonRpcProvider {\n    const chain = `${this.name}:${this.chainId}`;\n    const http = this.httpProviders[chain];\n    if (typeof http === \"undefined\") {\n      throw new Error(`JSON-RPC provider for ${chain} not found`);\n    }\n    return http;\n  }\n\n  private setHttpProvider(chainId: string, rpcUrl?: string): void {\n    const http = this.createHttpProvider(chainId, rpcUrl);\n    if (http) {\n      this.httpProviders[chainId] = http;\n    }\n  }\n\n  private createHttpProvider(\n    chainId: string,\n    rpcUrl?: string | undefined,\n  ): JsonRpcProvider | undefined {\n    const rpc = rpcUrl || getRpcUrl(chainId, this.namespace, this.client.core.projectId);\n    if (!rpc) {\n      throw new Error(`No RPC url provided for chainId: ${chainId}`);\n    }\n    const http = new JsonRpcProvider(new HttpConnection(rpc, getGlobal(\"disableProviderPing\")));\n    return http;\n  }\n}\n\nexport default SolanaProvider;\n", "import HttpConnection from \"@walletconnect/jsonrpc-http-connection\";\nimport { JsonRpcProvider } from \"@walletconnect/jsonrpc-provider\";\nimport Client from \"@walletconnect/sign-client\";\nimport { EngineTypes, SessionTypes } from \"@walletconnect/types\";\nimport EventEmitter from \"events\";\nimport { PROVIDER_EVENTS } from \"../constants\";\nimport {\n  IProvider,\n  RequestParams,\n  RpcProvidersMap,\n  SessionNamespace,\n  SubProviderOpts,\n} from \"../types\";\nimport { getChainId, getGlobal, getRpcUrl } from \"../utils\";\n\nclass CosmosProvider implements IProvider {\n  public name = \"cosmos\";\n  public client: Client;\n  public httpProviders: RpcProvidersMap;\n  public events: EventEmitter;\n  public namespace: SessionNamespace;\n  public chainId: string;\n\n  constructor(opts: SubProviderOpts) {\n    this.namespace = opts.namespace;\n    this.events = getGlobal(\"events\");\n    this.client = getGlobal(\"client\");\n    this.chainId = this.getDefaultChain();\n    this.httpProviders = this.createHttpProviders();\n  }\n\n  public updateNamespace(namespace: SessionTypes.Namespace) {\n    this.namespace = Object.assign(this.namespace, namespace);\n  }\n\n  public requestAccounts(): string[] {\n    return this.getAccounts();\n  }\n\n  public getDefaultChain(): string {\n    if (this.chainId) return this.chainId;\n    if (this.namespace.defaultChain) return this.namespace.defaultChain;\n\n    const chainId = this.namespace.chains[0];\n\n    if (!chainId) throw new Error(`ChainId not found`);\n\n    return chainId.split(\":\")[1];\n  }\n\n  public request<T = unknown>(args: RequestParams): Promise<T> {\n    if (this.namespace.methods.includes(args.request.method)) {\n      return this.client.request(args as EngineTypes.RequestParams);\n    }\n    return this.getHttpProvider().request(args.request);\n  }\n\n  public setDefaultChain(chainId: string, rpcUrl?: string | undefined) {\n    // http provider exists so just set the chainId\n    if (!this.httpProviders[chainId]) {\n      this.setHttpProvider(chainId, rpcUrl);\n    }\n    this.chainId = chainId;\n    this.events.emit(PROVIDER_EVENTS.DEFAULT_CHAIN_CHANGED, `${this.name}:${this.chainId}`);\n  }\n\n  // ---------------- PRIVATE ---------------- //\n\n  private getAccounts(): string[] {\n    const accounts = this.namespace.accounts;\n    if (!accounts) {\n      return [];\n    }\n\n    return [\n      ...new Set(\n        accounts\n          // get the accounts from the active chain\n          .filter((account) => account.split(\":\")[1] === this.chainId.toString())\n          // remove namespace & chainId from the string\n          .map((account) => account.split(\":\")[2]),\n      ),\n    ];\n  }\n\n  private createHttpProviders(): RpcProvidersMap {\n    const http = {};\n    this.namespace.chains.forEach((chain) => {\n      const parsedChainId = getChainId(chain);\n      http[parsedChainId] = this.createHttpProvider(parsedChainId, this.namespace.rpcMap?.[chain]);\n    });\n    return http;\n  }\n\n  private getHttpProvider(): JsonRpcProvider {\n    const chain = `${this.name}:${this.chainId}`;\n    const http = this.httpProviders[chain];\n    if (typeof http === \"undefined\") {\n      throw new Error(`JSON-RPC provider for ${chain} not found`);\n    }\n    return http;\n  }\n\n  private setHttpProvider(chainId: string, rpcUrl?: string): void {\n    const http = this.createHttpProvider(chainId, rpcUrl);\n    if (http) {\n      this.httpProviders[chainId] = http;\n    }\n  }\n\n  private createHttpProvider(\n    chainId: string,\n    rpcUrl?: string | undefined,\n  ): JsonRpcProvider | undefined {\n    const rpc = rpcUrl || getRpcUrl(chainId, this.namespace, this.client.core.projectId);\n    if (!rpc) {\n      throw new Error(`No RPC url provided for chainId: ${chainId}`);\n    }\n    const http = new JsonRpcProvider(new HttpConnection(rpc, getGlobal(\"disableProviderPing\")));\n    return http;\n  }\n}\n\nexport default CosmosProvider;\n", "import HttpConnection from \"@walletconnect/jsonrpc-http-connection\";\nimport { JsonRpcProvider } from \"@walletconnect/jsonrpc-provider\";\nimport Client from \"@walletconnect/sign-client\";\nimport { EngineTypes, SessionTypes } from \"@walletconnect/types\";\nimport EventEmitter from \"events\";\nimport { PROVIDER_EVENTS } from \"../constants\";\nimport {\n  IProvider,\n  RequestParams,\n  RpcProvidersMap,\n  SessionNamespace,\n  SubProviderOpts,\n} from \"../types\";\nimport { getGlobal, getRpcUrl } from \"../utils\";\n\nclass AlgorandProvider implements IProvider {\n  public name = \"algorand\";\n  public client: Client;\n  public httpProviders: RpcProvidersMap;\n  public events: EventEmitter;\n  public namespace: SessionNamespace;\n  public chainId: string;\n\n  constructor(opts: SubProviderOpts) {\n    this.namespace = opts.namespace;\n    this.events = getGlobal(\"events\");\n    this.client = getGlobal(\"client\");\n    this.chainId = this.getDefaultChain();\n    this.httpProviders = this.createHttpProviders();\n  }\n\n  public updateNamespace(namespace: SessionTypes.Namespace) {\n    this.namespace = Object.assign(this.namespace, namespace);\n  }\n\n  public requestAccounts(): string[] {\n    return this.getAccounts();\n  }\n\n  public request<T = unknown>(args: RequestParams): Promise<T> {\n    if (this.namespace.methods.includes(args.request.method)) {\n      return this.client.request(args as EngineTypes.RequestParams);\n    }\n    return this.getHttpProvider().request(args.request);\n  }\n\n  public setDefaultChain(chainId: string, rpcUrl?: string | undefined) {\n    // http provider exists so just set the chainId\n    if (!this.httpProviders[chainId]) {\n      const rpc =\n        rpcUrl || getRpcUrl(`${this.name}:${chainId}`, this.namespace, this.client.core.projectId);\n      if (!rpc) {\n        throw new Error(`No RPC url provided for chainId: ${chainId}`);\n      }\n      this.setHttpProvider(chainId, rpc);\n    }\n    this.chainId = chainId;\n    this.events.emit(PROVIDER_EVENTS.DEFAULT_CHAIN_CHANGED, `${this.name}:${this.chainId}`);\n  }\n\n  public getDefaultChain(): string {\n    if (this.chainId) return this.chainId;\n    if (this.namespace.defaultChain) return this.namespace.defaultChain;\n\n    const chainId = this.namespace.chains[0];\n    if (!chainId) throw new Error(`ChainId not found`);\n\n    return chainId.split(\":\")[1];\n  }\n\n  // --------- PRIVATE --------- //\n\n  private getAccounts(): string[] {\n    const accounts = this.namespace.accounts;\n    if (!accounts) {\n      return [];\n    }\n\n    return [\n      ...new Set(\n        accounts\n          // get the accounts from the active chain\n          .filter((account) => account.split(\":\")[1] === this.chainId.toString())\n          // remove namespace & chainId from the string\n          .map((account) => account.split(\":\")[2]),\n      ),\n    ];\n  }\n\n  private createHttpProviders(): RpcProvidersMap {\n    const http = {};\n    this.namespace.chains.forEach((chain) => {\n      http[chain] = this.createHttpProvider(chain, this.namespace.rpcMap?.[chain]);\n    });\n    return http;\n  }\n\n  private getHttpProvider(): JsonRpcProvider {\n    const chain = `${this.name}:${this.chainId}`;\n    const http = this.httpProviders[chain];\n    if (typeof http === \"undefined\") {\n      throw new Error(`JSON-RPC provider for ${chain} not found`);\n    }\n    return http;\n  }\n\n  private setHttpProvider(chainId: string, rpcUrl?: string): void {\n    const http = this.createHttpProvider(chainId, rpcUrl);\n    if (http) {\n      this.httpProviders[chainId] = http;\n    }\n  }\n\n  private createHttpProvider(\n    chainId: string,\n    rpcUrl?: string | undefined,\n  ): JsonRpcProvider | undefined {\n    const rpc = rpcUrl || getRpcUrl(chainId, this.namespace, this.client.core.projectId);\n    if (typeof rpc === \"undefined\") return undefined;\n    const http = new JsonRpcProvider(new HttpConnection(rpc, getGlobal(\"disableProviderPing\")));\n    return http;\n  }\n}\n\nexport default AlgorandProvider;\n", "import HttpConnection from \"@walletconnect/jsonrpc-http-connection\";\nimport { JsonRpcProvider } from \"@walletconnect/jsonrpc-provider\";\nimport Client from \"@walletconnect/sign-client\";\nimport { EngineTypes, SessionTypes } from \"@walletconnect/types\";\nimport EventEmitter from \"events\";\nimport { PROVIDER_EVENTS } from \"../constants\";\nimport {\n  IProvider,\n  RequestParams,\n  RpcProvidersMap,\n  SessionNamespace,\n  SubProviderOpts,\n} from \"../types\";\nimport { getChainId, getGlobal } from \"../utils\";\n\nclass CardanoProvider implements IProvider {\n  public name = \"cip34\";\n  public client: Client;\n  public httpProviders: RpcProvidersMap;\n  public events: EventEmitter;\n  public namespace: SessionNamespace;\n  public chainId: string;\n\n  constructor(opts: SubProviderOpts) {\n    this.namespace = opts.namespace;\n    this.events = getGlobal(\"events\");\n    this.client = getGlobal(\"client\");\n    this.chainId = this.getDefaultChain();\n    this.httpProviders = this.createHttpProviders();\n  }\n\n  public updateNamespace(namespace: SessionTypes.Namespace) {\n    this.namespace = Object.assign(this.namespace, namespace);\n  }\n\n  public requestAccounts(): string[] {\n    return this.getAccounts();\n  }\n\n  public getDefaultChain(): string {\n    if (this.chainId) return this.chainId;\n    if (this.namespace.defaultChain) return this.namespace.defaultChain;\n\n    const chainId = this.namespace.chains[0];\n    if (!chainId) throw new Error(`ChainId not found`);\n\n    return chainId.split(\":\")[1];\n  }\n\n  public request<T = unknown>(args: RequestParams): Promise<T> {\n    if (this.namespace.methods.includes(args.request.method)) {\n      return this.client.request(args as EngineTypes.RequestParams);\n    }\n    return this.getHttpProvider().request(args.request);\n  }\n\n  public setDefaultChain(chainId: string, rpcUrl?: string | undefined) {\n    // http provider exists so just set the chainId\n    if (!this.httpProviders[chainId]) {\n      this.setHttpProvider(chainId, rpcUrl);\n    }\n    this.chainId = chainId;\n    this.events.emit(PROVIDER_EVENTS.DEFAULT_CHAIN_CHANGED, `${this.name}:${this.chainId}`);\n  }\n\n  // ------------- PRIVATE -------------- /\n\n  private getAccounts(): string[] {\n    const accounts = this.namespace.accounts;\n    if (!accounts) {\n      return [];\n    }\n\n    return [\n      ...new Set(\n        accounts\n          // get the accounts from the active chain\n          .filter((account) => account.split(\":\")[1] === this.chainId.toString())\n          // remove namespace & chainId from the string\n          .map((account) => account.split(\":\")[2]),\n      ),\n    ];\n  }\n\n  private createHttpProviders(): RpcProvidersMap {\n    const http = {};\n    this.namespace.chains.forEach((chain) => {\n      const rpcURL = this.getCardanoRPCUrl(chain);\n      const parsedChain = getChainId(chain);\n      http[parsedChain] = this.createHttpProvider(parsedChain, rpcURL);\n    });\n    return http;\n  }\n\n  private getHttpProvider(): JsonRpcProvider {\n    const chain = `${this.name}:${this.chainId}`;\n    const http = this.httpProviders[chain];\n    if (typeof http === \"undefined\") {\n      throw new Error(`JSON-RPC provider for ${chain} not found`);\n    }\n    return http;\n  }\n\n  private getCardanoRPCUrl(chainId: string): string | undefined {\n    const rpcMap = this.namespace.rpcMap;\n    if (!rpcMap) return undefined;\n    return rpcMap[chainId];\n  }\n\n  private setHttpProvider(chainId: string, rpcUrl?: string): void {\n    const http = this.createHttpProvider(chainId, rpcUrl);\n    if (http) {\n      this.httpProviders[chainId] = http;\n    }\n  }\n\n  private createHttpProvider(\n    chainId: string,\n    rpcUrl?: string | undefined,\n  ): JsonRpcProvider | undefined {\n    const rpc = rpcUrl || this.getCardanoRPCUrl(chainId);\n    if (!rpc) {\n      throw new Error(`No RPC url provided for chainId: ${chainId}`);\n    }\n    const http = new JsonRpcProvider(new HttpConnection(rpc, getGlobal(\"disableProviderPing\")));\n    return http;\n  }\n}\n\nexport default CardanoProvider;\n", "import HttpConnection from \"@walletconnect/jsonrpc-http-connection\";\nimport { JsonRpcProvider } from \"@walletconnect/jsonrpc-provider\";\nimport Client from \"@walletconnect/sign-client\";\nimport { EngineTypes, SessionTypes } from \"@walletconnect/types\";\nimport EventEmitter from \"events\";\nimport { PROVIDER_EVENTS } from \"../constants\";\nimport {\n  IProvider,\n  RequestParams,\n  RpcProvidersMap,\n  SessionNamespace,\n  SubProviderOpts,\n} from \"../types\";\nimport { getChainId, getGlobal, getRpcUrl } from \"../utils\";\n\n// Old wallet connect provider for Elrond\nclass ElrondProvider implements IProvider {\n  public name = \"elrond\";\n  public client: Client;\n  public httpProviders: RpcProvidersMap;\n  public events: EventEmitter;\n  public namespace: SessionNamespace;\n  public chainId: string;\n\n  constructor(opts: SubProviderOpts) {\n    this.namespace = opts.namespace;\n    this.events = getGlobal(\"events\");\n    this.client = getGlobal(\"client\");\n    this.chainId = this.getDefaultChain();\n    this.httpProviders = this.createHttpProviders();\n  }\n\n  public updateNamespace(namespace: SessionTypes.Namespace) {\n    this.namespace = Object.assign(this.namespace, namespace);\n  }\n\n  public requestAccounts(): string[] {\n    return this.getAccounts();\n  }\n\n  public request<T = unknown>(args: RequestParams): Promise<T> {\n    if (this.namespace.methods.includes(args.request.method)) {\n      return this.client.request(args as EngineTypes.RequestParams);\n    }\n    return this.getHttpProvider().request(args.request);\n  }\n\n  public setDefaultChain(chainId: string, rpcUrl?: string | undefined) {\n    // http provider exists so just set the chainId\n    if (!this.httpProviders[chainId]) {\n      this.setHttpProvider(chainId, rpcUrl);\n    }\n    this.chainId = chainId;\n    this.events.emit(PROVIDER_EVENTS.DEFAULT_CHAIN_CHANGED, `${this.name}:${chainId}`);\n  }\n\n  public getDefaultChain(): string {\n    if (this.chainId) return this.chainId;\n    if (this.namespace.defaultChain) return this.namespace.defaultChain;\n\n    const chainId = this.namespace.chains[0];\n    if (!chainId) throw new Error(`ChainId not found`);\n\n    return chainId.split(\":\")[1];\n  }\n\n  // --------- PRIVATE --------- //\n\n  private getAccounts(): string[] {\n    const accounts = this.namespace.accounts;\n    if (!accounts) {\n      return [];\n    }\n\n    return [\n      ...new Set(\n        accounts\n          // get the accounts from the active chain\n          .filter((account) => account.split(\":\")[1] === this.chainId.toString())\n          // remove namespace & chainId from the string\n          .map((account) => account.split(\":\")[2]),\n      ),\n    ];\n  }\n\n  private createHttpProviders(): RpcProvidersMap {\n    const http = {};\n    this.namespace.chains.forEach((chain) => {\n      const parsedChainId = getChainId(chain);\n      http[parsedChainId] = this.createHttpProvider(parsedChainId, this.namespace.rpcMap?.[chain]);\n    });\n    return http;\n  }\n\n  private getHttpProvider(): JsonRpcProvider {\n    const chain = `${this.name}:${this.chainId}`;\n    const http = this.httpProviders[chain];\n    if (typeof http === \"undefined\") {\n      throw new Error(`JSON-RPC provider for ${chain} not found`);\n    }\n    return http;\n  }\n\n  private setHttpProvider(chainId: string, rpcUrl?: string): void {\n    const http = this.createHttpProvider(chainId, rpcUrl);\n    if (http) {\n      this.httpProviders[chainId] = http;\n    }\n  }\n\n  private createHttpProvider(\n    chainId: string,\n    rpcUrl?: string | undefined,\n  ): JsonRpcProvider | undefined {\n    const rpc = rpcUrl || getRpcUrl(chainId, this.namespace, this.client.core.projectId);\n    if (!rpc) {\n      throw new Error(`No RPC url provided for chainId: ${chainId}`);\n    }\n    const http = new JsonRpcProvider(new HttpConnection(rpc, getGlobal(\"disableProviderPing\")));\n    return http;\n  }\n}\n\nexport default ElrondProvider;\n", "import HttpConnection from \"@walletconnect/jsonrpc-http-connection\";\nimport { JsonRpcProvider } from \"@walletconnect/jsonrpc-provider\";\nimport Client from \"@walletconnect/sign-client\";\nimport { EngineTypes, SessionTypes } from \"@walletconnect/types\";\nimport EventEmitter from \"events\";\nimport { PROVIDER_EVENTS } from \"../constants\";\nimport {\n  IProvider,\n  RequestParams,\n  RpcProvidersMap,\n  SessionNamespace,\n  SubProviderOpts,\n} from \"../types\";\nimport { getChainId, getGlobal, getRpcUrl } from \"../utils\";\n\nclass MultiversXProvider implements IProvider {\n  public name = \"multiversx\";\n  public client: Client;\n  public httpProviders: RpcProvidersMap;\n  public events: EventEmitter;\n  public namespace: SessionNamespace;\n  public chainId: string;\n\n  constructor(opts: SubProviderOpts) {\n    this.namespace = opts.namespace;\n    this.events = getGlobal(\"events\");\n    this.client = getGlobal(\"client\");\n    this.chainId = this.getDefaultChain();\n    this.httpProviders = this.createHttpProviders();\n  }\n\n  public updateNamespace(namespace: SessionTypes.Namespace) {\n    this.namespace = Object.assign(this.namespace, namespace);\n  }\n\n  public requestAccounts(): string[] {\n    return this.getAccounts();\n  }\n\n  public request<T = unknown>(args: RequestParams): Promise<T> {\n    if (this.namespace.methods.includes(args.request.method)) {\n      return this.client.request(args as EngineTypes.RequestParams);\n    }\n    return this.getHttpProvider().request(args.request);\n  }\n\n  public setDefaultChain(chainId: string, rpcUrl?: string | undefined) {\n    // http provider exists so just set the chainId\n    if (!this.httpProviders[chainId]) {\n      this.setHttpProvider(chainId, rpcUrl);\n    }\n    this.chainId = chainId;\n    this.events.emit(PROVIDER_EVENTS.DEFAULT_CHAIN_CHANGED, `${this.name}:${chainId}`);\n  }\n\n  public getDefaultChain(): string {\n    if (this.chainId) return this.chainId;\n    if (this.namespace.defaultChain) return this.namespace.defaultChain;\n\n    const chainId = this.namespace.chains[0];\n    if (!chainId) throw new Error(`ChainId not found`);\n\n    return chainId.split(\":\")[1];\n  }\n\n  // --------- PRIVATE --------- //\n\n  private getAccounts(): string[] {\n    const accounts = this.namespace.accounts;\n    if (!accounts) {\n      return [];\n    }\n\n    return [\n      ...new Set(\n        accounts\n          // get the accounts from the active chain\n          .filter((account) => account.split(\":\")[1] === this.chainId.toString())\n          // remove namespace & chainId from the string\n          .map((account) => account.split(\":\")[2]),\n      ),\n    ];\n  }\n\n  private createHttpProviders(): RpcProvidersMap {\n    const http = {};\n    this.namespace.chains.forEach((chain) => {\n      const parsedChainId = getChainId(chain);\n      http[parsedChainId] = this.createHttpProvider(parsedChainId, this.namespace.rpcMap?.[chain]);\n    });\n    return http;\n  }\n\n  private getHttpProvider(): JsonRpcProvider {\n    const chain = `${this.name}:${this.chainId}`;\n    const http = this.httpProviders[chain];\n    if (typeof http === \"undefined\") {\n      throw new Error(`JSON-RPC provider for ${chain} not found`);\n    }\n    return http;\n  }\n\n  private setHttpProvider(chainId: string, rpcUrl?: string): void {\n    const http = this.createHttpProvider(chainId, rpcUrl);\n    if (http) {\n      this.httpProviders[chainId] = http;\n    }\n  }\n\n  private createHttpProvider(\n    chainId: string,\n    rpcUrl?: string | undefined,\n  ): JsonRpcProvider | undefined {\n    const rpc = rpcUrl || getRpcUrl(chainId, this.namespace, this.client.core.projectId);\n    if (!rpc) {\n      throw new Error(`No RPC url provided for chainId: ${chainId}`);\n    }\n    const http = new JsonRpcProvider(new HttpConnection(rpc, getGlobal(\"disableProviderPing\")));\n    return http;\n  }\n}\n\nexport default MultiversXProvider;\n", "import HttpConnection from \"@walletconnect/jsonrpc-http-connection\";\nimport { JsonRpcProvider } from \"@walletconnect/jsonrpc-provider\";\nimport Client from \"@walletconnect/sign-client\";\nimport { EngineTypes, SessionTypes } from \"@walletconnect/types\";\nimport EventEmitter from \"events\";\nimport { PROVIDER_EVENTS } from \"../constants\";\nimport {\n  IProvider,\n  RequestParams,\n  RpcProvidersMap,\n  SessionNamespace,\n  SubProviderOpts,\n} from \"../types\";\nimport { getGlobal, getRpcUrl } from \"../utils\";\n\nclass NearProvider implements IProvider {\n  public name = \"near\";\n  public client: Client;\n  public httpProviders: RpcProvidersMap;\n  public events: EventEmitter;\n  public namespace: SessionNamespace;\n  public chainId: string;\n\n  constructor(opts: SubProviderOpts) {\n    this.namespace = opts.namespace;\n    this.events = getGlobal(\"events\");\n    this.client = getGlobal(\"client\");\n    this.chainId = this.getDefaultChain();\n    this.httpProviders = this.createHttpProviders();\n  }\n\n  public updateNamespace(namespace: SessionTypes.Namespace) {\n    this.namespace = Object.assign(this.namespace, namespace);\n  }\n\n  public requestAccounts(): string[] {\n    return this.getAccounts();\n  }\n\n  public getDefaultChain(): string {\n    if (this.chainId) return this.chainId;\n    if (this.namespace.defaultChain) return this.namespace.defaultChain;\n\n    const chainId = this.namespace.chains[0];\n\n    if (!chainId) throw new Error(`ChainId not found`);\n\n    return chainId.split(\":\")[1];\n  }\n\n  public request<T = unknown>(args: RequestParams): Promise<T> {\n    if (this.namespace.methods.includes(args.request.method)) {\n      return this.client.request(args as EngineTypes.RequestParams);\n    }\n    return this.getHttpProvider().request(args.request);\n  }\n\n  public setDefaultChain(chainId: string, rpcUrl?: string | undefined) {\n    this.chainId = chainId;\n    // http provider exists so just set the chainId\n    if (!this.httpProviders[chainId]) {\n      const rpc = rpcUrl || getRpcUrl(`${this.name}:${chainId}`, this.namespace);\n      if (!rpc) {\n        throw new Error(`No RPC url provided for chainId: ${chainId}`);\n      }\n      this.setHttpProvider(chainId, rpc);\n    }\n\n    this.events.emit(PROVIDER_EVENTS.DEFAULT_CHAIN_CHANGED, `${this.name}:${this.chainId}`);\n  }\n\n  // ---------------- PRIVATE ---------------- //\n\n  private getAccounts(): string[] {\n    const accounts = this.namespace.accounts;\n    if (!accounts) {\n      return [];\n    }\n\n    return (\n      accounts\n        // get the accounts from the active chain\n        .filter((account) => account.split(\":\")[1] === this.chainId.toString())\n        // remove namespace & chainId from the string\n        .map((account) => account.split(\":\")[2]) || []\n    );\n  }\n\n  private createHttpProviders(): RpcProvidersMap {\n    const http = {};\n    this.namespace.chains.forEach((chain) => {\n      http[chain] = this.createHttpProvider(chain, this.namespace.rpcMap?.[chain]);\n    });\n    return http;\n  }\n\n  private getHttpProvider(): JsonRpcProvider {\n    const chain = `${this.name}:${this.chainId}`;\n    const http = this.httpProviders[chain];\n    if (typeof http === \"undefined\") {\n      throw new Error(`JSON-RPC provider for ${chain} not found`);\n    }\n    return http;\n  }\n\n  private setHttpProvider(chainId: string, rpcUrl?: string): void {\n    const http = this.createHttpProvider(chainId, rpcUrl);\n    if (http) {\n      this.httpProviders[chainId] = http;\n    }\n  }\n\n  private createHttpProvider(\n    chainId: string,\n    rpcUrl?: string | undefined,\n  ): JsonRpcProvider | undefined {\n    const rpc = rpcUrl || getRpcUrl(chainId, this.namespace);\n    if (typeof rpc === \"undefined\") return undefined;\n    const http = new JsonRpcProvider(new HttpConnection(rpc, getGlobal(\"disableProviderPing\")));\n    return http;\n  }\n}\n\nexport default NearProvider;\n", "import HttpConnection from \"@walletconnect/jsonrpc-http-connection\";\nimport { JsonRpcProvider } from \"@walletconnect/jsonrpc-provider\";\nimport Client from \"@walletconnect/sign-client\";\nimport { EngineTypes, SessionTypes } from \"@walletconnect/types\";\nimport EventEmitter from \"events\";\nimport { PROVIDER_EVENTS } from \"../constants\";\nimport {\n  IProvider,\n  RequestParams,\n  RpcProvidersMap,\n  SessionNamespace,\n  SubProviderOpts,\n} from \"../types\";\n\nimport { getRpcUrl, getGlobal } from \"../utils\";\n\nclass TezosProvider implements IProvider {\n  public name = \"tezos\";\n  public client: Client;\n  public httpProviders: RpcProvidersMap;\n  public events: EventEmitter;\n  public namespace: SessionNamespace;\n  public chainId: string;\n\n  constructor(opts: SubProviderOpts) {\n    this.namespace = opts.namespace;\n    this.events = getGlobal(\"events\");\n    this.client = getGlobal(\"client\");\n    this.chainId = this.getDefaultChain();\n    this.httpProviders = this.createHttpProviders();\n  }\n\n  public updateNamespace(namespace: SessionTypes.Namespace) {\n    this.namespace = Object.assign(this.namespace, namespace);\n  }\n\n  public requestAccounts(): string[] {\n    return this.getAccounts();\n  }\n\n  public getDefaultChain(): string {\n    if (this.chainId) return this.chainId;\n    if (this.namespace.defaultChain) return this.namespace.defaultChain;\n\n    const chainId = this.namespace.chains[0];\n\n    if (!chainId) throw new Error(`ChainId not found`);\n\n    return chainId.split(\":\")[1];\n  }\n\n  public request<T = unknown>(args: RequestParams): Promise<T> {\n    if (this.namespace.methods.includes(args.request.method)) {\n      return this.client.request(args as EngineTypes.RequestParams);\n    }\n    return this.getHttpProvider().request(args.request);\n  }\n\n  public setDefaultChain(chainId: string, rpcUrl?: string | undefined) {\n    this.chainId = chainId;\n    // http provider exists so just set the chainId\n    if (!this.httpProviders[chainId]) {\n      const rpc = rpcUrl || getRpcUrl(`${this.name}:${chainId}`, this.namespace);\n      if (!rpc) {\n        throw new Error(`No RPC url provided for chainId: ${chainId}`);\n      }\n      this.setHttpProvider(chainId, rpc);\n    }\n\n    this.events.emit(PROVIDER_EVENTS.DEFAULT_CHAIN_CHANGED, `${this.name}:${this.chainId}`);\n  }\n\n  // ---------------- PRIVATE ---------------- //\n\n  private getAccounts(): string[] {\n    const accounts = this.namespace.accounts;\n    if (!accounts) {\n      return [];\n    }\n\n    return (\n      accounts\n        // get the accounts from the active chain\n        .filter((account) => account.split(\":\")[1] === this.chainId.toString())\n        // remove namespace & chainId from the string\n        .map((account) => account.split(\":\")[2]) || []\n    );\n  }\n\n  private createHttpProviders(): RpcProvidersMap {\n    const http: any = {};\n    this.namespace.chains.forEach((chain) => {\n      http[chain] = this.createHttpProvider(chain);\n    });\n    return http;\n  }\n\n  private getHttpProvider(): JsonRpcProvider {\n    const chain = `${this.name}:${this.chainId}`;\n    const http = this.httpProviders[chain];\n    if (typeof http === \"undefined\") {\n      throw new Error(`JSON-RPC provider for ${chain} not found`);\n    }\n    return http;\n  }\n\n  private setHttpProvider(chainId: string, rpcUrl?: string): void {\n    const http = this.createHttpProvider(chainId, rpcUrl);\n    if (http) {\n      this.httpProviders[chainId] = http;\n    }\n  }\n\n  private createHttpProvider(\n    chainId: string,\n    rpcUrl?: string | undefined,\n  ): JsonRpcProvider | undefined {\n    const rpc = rpcUrl || getRpcUrl(chainId, this.namespace);\n    if (typeof rpc === \"undefined\") return undefined;\n    const http = new JsonRpcProvider(new HttpConnection(rpc));\n    return http;\n  }\n}\n\nexport default TezosProvider;\n", "import HttpConnection from \"@walletconnect/jsonrpc-http-connection\";\nimport { JsonRpcProvider } from \"@walletconnect/jsonrpc-provider\";\nimport Client from \"@walletconnect/sign-client\";\nimport { EngineTypes, SessionTypes } from \"@walletconnect/types\";\nimport EventEmitter from \"events\";\nimport { GENERIC_SUBPROVIDER_NAME, PROVIDER_EVENTS } from \"../constants\";\nimport {\n  IProvider,\n  RequestParams,\n  RpcProvidersMap,\n  SessionNamespace,\n  SubProviderOpts,\n} from \"../types\";\nimport { getGlobal, getRpcUrl } from \"../utils\";\nimport { parseChainId } from \"@walletconnect/utils\";\n\nclass GenericProvider implements IProvider {\n  public name = GENERIC_SUBPROVIDER_NAME;\n  public client: Client;\n  public httpProviders: RpcProvidersMap;\n  public events: EventEmitter;\n  public namespace: SessionNamespace;\n  public chainId: string;\n\n  constructor(opts: SubProviderOpts) {\n    this.namespace = opts.namespace;\n    this.events = getGlobal(\"events\");\n    this.client = getGlobal(\"client\");\n    this.chainId = this.getDefaultChain();\n    this.httpProviders = this.createHttpProviders();\n  }\n\n  public updateNamespace(namespace: SessionTypes.Namespace) {\n    this.namespace.chains = [\n      ...new Set((this.namespace.chains || []).concat(namespace.chains || [])),\n    ];\n    this.namespace.accounts = [\n      ...new Set((this.namespace.accounts || []).concat(namespace.accounts || [])),\n    ];\n    this.namespace.methods = [\n      ...new Set((this.namespace.methods || []).concat(namespace.methods || [])),\n    ];\n    this.namespace.events = [\n      ...new Set((this.namespace.events || []).concat(namespace.events || [])),\n    ];\n    this.httpProviders = this.createHttpProviders();\n  }\n\n  public requestAccounts(): string[] {\n    return this.getAccounts();\n  }\n\n  public request<T = unknown>(args: RequestParams): Promise<T> {\n    if (this.namespace.methods.includes(args.request.method)) {\n      return this.client.request(args as EngineTypes.RequestParams);\n    }\n    return this.getHttpProvider(args.chainId).request(args.request);\n  }\n\n  public setDefaultChain(chainId: string, rpcUrl?: string | undefined) {\n    // http provider exists so just set the chainId\n    if (!this.httpProviders[chainId]) {\n      this.setHttpProvider(chainId, rpcUrl);\n    }\n    this.chainId = chainId;\n    this.events.emit(PROVIDER_EVENTS.DEFAULT_CHAIN_CHANGED, `${this.name}:${chainId}`);\n  }\n\n  public getDefaultChain(): string {\n    if (this.chainId) return this.chainId;\n    if (this.namespace.defaultChain) return this.namespace.defaultChain;\n\n    const chainId = this.namespace.chains[0];\n    if (!chainId) throw new Error(`ChainId not found`);\n\n    return chainId.split(\":\")[1];\n  }\n\n  // --------- PRIVATE --------- //\n\n  private getAccounts(): string[] {\n    const accounts = this.namespace.accounts;\n    if (!accounts) {\n      return [];\n    }\n\n    return [\n      ...new Set(\n        accounts\n          // get the accounts from the active chain\n          .filter((account) => account.split(\":\")[1] === this.chainId.toString())\n          // remove namespace & chainId from the string\n          .map((account) => account.split(\":\")[2]),\n      ),\n    ];\n  }\n\n  private createHttpProviders(): RpcProvidersMap {\n    const http = {};\n    this.namespace?.accounts?.forEach((account) => {\n      const chain = parseChainId(account);\n      http[`${chain.namespace}:${chain.reference}`] = this.createHttpProvider(account);\n    });\n    return http;\n  }\n\n  private getHttpProvider(chain: string): JsonRpcProvider {\n    const http = this.httpProviders[chain];\n    if (typeof http === \"undefined\") {\n      throw new Error(`JSON-RPC provider for ${chain} not found`);\n    }\n    return http;\n  }\n\n  private setHttpProvider(chainId: string, rpcUrl?: string): void {\n    const http = this.createHttpProvider(chainId, rpcUrl);\n    if (http) {\n      this.httpProviders[chainId] = http;\n    }\n  }\n\n  private createHttpProvider(chainId: string, rpcUrl?: string): JsonRpcProvider | undefined {\n    const rpc = rpcUrl || getRpcUrl(chainId, this.namespace, this.client.core.projectId);\n    if (!rpc) {\n      throw new Error(`No RPC url provided for chainId: ${chainId}`);\n    }\n    const http = new JsonRpcProvider(new HttpConnection(rpc, getGlobal(\"disableProviderPing\")));\n    return http;\n  }\n}\n\nexport default GenericProvider;\n", "import SignClient from \"@walletconnect/sign-client\";\nimport { SessionTypes } from \"@walletconnect/types\";\nimport { JsonRpcResult } from \"@walletconnect/jsonrpc-types\";\nimport { getSdkError, isValidArray, parseNamespaceKey } from \"@walletconnect/utils\";\nimport { getDefaultLoggerOptions, Logger, pino } from \"@walletconnect/logger\";\nimport {\n  convertChainIdToNumber,\n  getAccountsFromSession,\n  getChainsFromApprovedSession,\n  mergeRequiredOptionalNamespaces,\n  parseCaip10Account,\n  populateNamespacesChains,\n  setGlobal,\n} from \"./utils\";\nimport PolkadotProvider from \"./providers/polkadot\";\nimport Eip155Provider from \"./providers/eip155\";\nimport SolanaProvider from \"./providers/solana\";\nimport CosmosProvider from \"./providers/cosmos\";\nimport AlgorandProvider from \"./providers/algorand\";\nimport CardanoProvider from \"./providers/cardano\";\nimport ElrondProvider from \"./providers/elrond\";\nimport MultiversXProvider from \"./providers/multiversx\";\nimport NearProvider from \"./providers/near\";\nimport TezosProvider from \"./providers/tezos\";\nimport GenericProvider from \"./providers/generic\";\n\nimport {\n  IUniversalProvider,\n  IProvider,\n  RpcProviderMap,\n  ConnectParams,\n  RequestArguments,\n  UniversalProviderOpts,\n  NamespaceConfig,\n  PairingsCleanupOpts,\n  ProviderAccounts,\n  AuthenticateParams,\n} from \"./types\";\n\nimport { RELAY_URL, LOGGER, STORAGE, PROVIDER_EVENTS, GENERIC_SUBPROVIDER_NAME } from \"./constants\";\nimport EventEmitter from \"events\";\nimport { formatJsonRpcResult } from \"@walletconnect/jsonrpc-utils\";\n\nexport class UniversalProvider implements IUniversalProvider {\n  public client!: SignClient;\n  public namespaces?: NamespaceConfig;\n  public optionalNamespaces?: NamespaceConfig;\n  public sessionProperties?: SessionTypes.SessionProperties;\n  public scopedProperties?: SessionTypes.ScopedProperties;\n  public events: EventEmitter = new EventEmitter();\n  public rpcProviders: RpcProviderMap = {};\n  public session?: SessionTypes.Struct;\n  public providerOpts: UniversalProviderOpts;\n  public logger: Logger;\n  public uri: string | undefined;\n\n  private disableProviderPing = false;\n\n  static async init(opts: UniversalProviderOpts) {\n    const provider = new UniversalProvider(opts);\n    await provider.initialize();\n    return provider;\n  }\n\n  constructor(opts: UniversalProviderOpts) {\n    this.providerOpts = opts;\n    this.logger =\n      typeof opts?.logger !== \"undefined\" && typeof opts?.logger !== \"string\"\n        ? opts.logger\n        : pino(getDefaultLoggerOptions({ level: opts?.logger || LOGGER }));\n    this.disableProviderPing = opts?.disableProviderPing || false;\n  }\n\n  public async request<T = unknown>(\n    args: RequestArguments,\n    chain?: string | undefined,\n    expiry?: number | undefined,\n  ): Promise<T> {\n    const [namespace, chainId] = this.validateChain(chain);\n\n    if (!this.session) {\n      throw new Error(\"Please call connect() before request()\");\n    }\n\n    return await this.getProvider(namespace).request({\n      request: {\n        ...args,\n      },\n      chainId: `${namespace}:${chainId}`,\n      topic: this.session.topic,\n      expiry,\n    });\n  }\n\n  public sendAsync(\n    args: RequestArguments,\n    callback: (error: Error | null, response: JsonRpcResult) => void,\n    chain?: string | undefined,\n    expiry?: number | undefined,\n  ): void {\n    const id = new Date().getTime();\n    this.request(args, chain, expiry)\n      .then((response) => callback(null, formatJsonRpcResult(id, response)))\n      .catch((error) => callback(error, undefined as any));\n  }\n\n  public async enable(): Promise<ProviderAccounts> {\n    if (!this.client) {\n      throw new Error(\"Sign Client not initialized\");\n    }\n    if (!this.session) {\n      await this.connect({\n        namespaces: this.namespaces,\n        optionalNamespaces: this.optionalNamespaces,\n        sessionProperties: this.sessionProperties,\n        scopedProperties: this.scopedProperties,\n      });\n    }\n    const accounts = await this.requestAccounts();\n    return accounts as ProviderAccounts;\n  }\n\n  public async disconnect(): Promise<void> {\n    if (!this.session) {\n      throw new Error(\"Please call connect() before enable()\");\n    }\n    await this.client.disconnect({\n      topic: this.session?.topic,\n      reason: getSdkError(\"USER_DISCONNECTED\"),\n    });\n    await this.cleanup();\n  }\n\n  public async connect(opts: ConnectParams): Promise<SessionTypes.Struct | undefined> {\n    if (!this.client) {\n      throw new Error(\"Sign Client not initialized\");\n    }\n    this.setNamespaces(opts);\n    await this.cleanupPendingPairings();\n    if (opts.skipPairing) return;\n\n    return await this.pair(opts.pairingTopic);\n  }\n\n  public async authenticate(opts: AuthenticateParams, walletUniversalLink?: string) {\n    if (!this.client) {\n      throw new Error(\"Sign Client not initialized\");\n    }\n    this.setNamespaces(opts);\n    await this.cleanupPendingPairings();\n\n    const { uri, response } = await this.client.authenticate(opts, walletUniversalLink);\n    if (uri) {\n      this.uri = uri;\n      this.events.emit(\"display_uri\", uri);\n    }\n    const result = await response();\n    this.session = result.session;\n    if (this.session) {\n      // assign namespaces from session if not already defined\n      const approved = populateNamespacesChains(this.session.namespaces) as NamespaceConfig;\n      this.namespaces = mergeRequiredOptionalNamespaces(this.namespaces, approved);\n      await this.persist(\"namespaces\", this.namespaces);\n      this.onConnect();\n    }\n    return result;\n  }\n\n  public on(event: any, listener: any): void {\n    this.events.on(event, listener);\n  }\n\n  public once(event: string, listener: any): void {\n    this.events.once(event, listener);\n  }\n\n  public removeListener(event: string, listener: any): void {\n    this.events.removeListener(event, listener);\n  }\n\n  public off(event: string, listener: any): void {\n    this.events.off(event, listener);\n  }\n\n  get isWalletConnect() {\n    return true;\n  }\n\n  public async pair(pairingTopic: string | undefined): Promise<SessionTypes.Struct> {\n    const { uri, approval } = await this.client.connect({\n      pairingTopic,\n      requiredNamespaces: this.namespaces,\n      optionalNamespaces: this.optionalNamespaces,\n      sessionProperties: this.sessionProperties,\n      scopedProperties: this.scopedProperties,\n    });\n\n    if (uri) {\n      this.uri = uri;\n      this.events.emit(\"display_uri\", uri);\n    }\n\n    const session = await approval();\n    this.session = session;\n    // assign namespaces from session if not already defined\n    const approved = populateNamespacesChains(session.namespaces) as NamespaceConfig;\n    this.namespaces = mergeRequiredOptionalNamespaces(this.namespaces, approved);\n    await this.persist(\"namespaces\", this.namespaces);\n    await this.persist(\"optionalNamespaces\", this.optionalNamespaces);\n\n    this.onConnect();\n    return this.session;\n  }\n\n  public setDefaultChain(chain: string, rpcUrl?: string | undefined) {\n    try {\n      // ignore without active session\n      if (!this.session) return;\n      const [namespace, chainId] = this.validateChain(chain);\n      const provider = this.getProvider(namespace);\n      // @ts-expect-error\n      if (provider.name === GENERIC_SUBPROVIDER_NAME) {\n        provider.setDefaultChain(`${namespace}:${chainId}`, rpcUrl);\n      } else {\n        provider.setDefaultChain(chainId, rpcUrl);\n      }\n    } catch (error) {\n      // ignore the error if the fx is used prematurely before namespaces are set\n      if (!/Please call connect/.test((error as Error).message)) throw error;\n    }\n  }\n\n  public async cleanupPendingPairings(opts: PairingsCleanupOpts = {}): Promise<void> {\n    this.logger.info(\"Cleaning up inactive pairings...\");\n    const inactivePairings = this.client.pairing.getAll();\n\n    if (!isValidArray(inactivePairings)) return;\n\n    for (const pairing of inactivePairings) {\n      if (opts.deletePairings) {\n        this.client.core.expirer.set(pairing.topic, 0);\n      } else {\n        await this.client.core.relayer.subscriber.unsubscribe(pairing.topic);\n      }\n    }\n\n    this.logger.info(`Inactive pairings cleared: ${inactivePairings.length}`);\n  }\n\n  public abortPairingAttempt() {\n    this.logger.warn(\"abortPairingAttempt is deprecated. This is now a no-op.\");\n  }\n\n  // ---------- Private ----------------------------------------------- //\n\n  private async checkStorage() {\n    this.namespaces = (await this.getFromStore(`namespaces`)) || {};\n    this.optionalNamespaces = (await this.getFromStore(`optionalNamespaces`)) || {};\n    if (this.session) this.createProviders();\n  }\n\n  private async initialize() {\n    this.logger.trace(`Initialized`);\n    await this.createClient();\n    await this.checkStorage();\n    this.registerEventListeners();\n  }\n\n  private async createClient() {\n    this.client =\n      this.providerOpts.client ||\n      (await SignClient.init({\n        core: this.providerOpts.core,\n        logger: this.providerOpts.logger || LOGGER,\n        relayUrl: this.providerOpts.relayUrl || RELAY_URL,\n        projectId: this.providerOpts.projectId,\n        metadata: this.providerOpts.metadata,\n        storageOptions: this.providerOpts.storageOptions,\n        storage: this.providerOpts.storage,\n        name: this.providerOpts.name,\n        customStoragePrefix: this.providerOpts.customStoragePrefix,\n        telemetryEnabled: this.providerOpts.telemetryEnabled,\n      }));\n\n    if (this.providerOpts.session) {\n      try {\n        this.session = this.client.session.get(this.providerOpts.session.topic);\n      } catch (error) {\n        this.logger.error(\"Failed to get session\", error);\n        throw new Error(\n          `The provided session: ${this.providerOpts?.session?.topic} doesn't exist in the Sign client`,\n        );\n      }\n    } else {\n      const sessions = this.client.session.getAll();\n      this.session = sessions[0];\n    }\n    this.logger.trace(`SignClient Initialized`);\n  }\n\n  private createProviders(): void {\n    if (!this.client) {\n      throw new Error(\"Sign Client not initialized\");\n    }\n\n    if (!this.session) {\n      throw new Error(\"Session not initialized. Please call connect() before enable()\");\n    }\n\n    const providersToCreate = [\n      ...new Set(\n        Object.keys(this.session.namespaces).map((namespace) => parseNamespaceKey(namespace)),\n      ),\n    ];\n\n    setGlobal(\"client\", this.client);\n    setGlobal(\"events\", this.events);\n    setGlobal(\"disableProviderPing\", this.disableProviderPing);\n\n    providersToCreate.forEach((namespace) => {\n      if (!this.session) return;\n      const accounts = getAccountsFromSession(namespace, this.session);\n      const approvedChains = getChainsFromApprovedSession(accounts);\n      const mergedNamespaces = mergeRequiredOptionalNamespaces(\n        this.namespaces,\n        this.optionalNamespaces,\n      );\n      const combinedNamespace = {\n        ...mergedNamespaces[namespace],\n        accounts,\n        chains: approvedChains,\n      };\n      switch (namespace) {\n        case \"eip155\":\n          this.rpcProviders[namespace] = new Eip155Provider({\n            namespace: combinedNamespace,\n          });\n          break;\n        case \"algorand\":\n          this.rpcProviders[namespace] = new AlgorandProvider({\n            namespace: combinedNamespace,\n          });\n          break;\n        case \"solana\":\n          this.rpcProviders[namespace] = new SolanaProvider({\n            namespace: combinedNamespace,\n          });\n          break;\n        case \"cosmos\":\n          this.rpcProviders[namespace] = new CosmosProvider({\n            namespace: combinedNamespace,\n          });\n          break;\n        case \"polkadot\":\n          this.rpcProviders[namespace] = new PolkadotProvider({\n            namespace: combinedNamespace,\n          });\n          break;\n        case \"cip34\":\n          this.rpcProviders[namespace] = new CardanoProvider({\n            namespace: combinedNamespace,\n          });\n          break;\n        case \"elrond\":\n          this.rpcProviders[namespace] = new ElrondProvider({\n            namespace: combinedNamespace,\n          });\n          break;\n        case \"multiversx\":\n          this.rpcProviders[namespace] = new MultiversXProvider({\n            namespace: combinedNamespace,\n          });\n          break;\n        case \"near\":\n          this.rpcProviders[namespace] = new NearProvider({\n            namespace: combinedNamespace,\n          });\n          break;\n        case \"tezos\":\n          this.rpcProviders[namespace] = new TezosProvider({\n            namespace: combinedNamespace,\n          });\n          break;\n        default:\n          if (!this.rpcProviders[GENERIC_SUBPROVIDER_NAME]) {\n            this.rpcProviders[GENERIC_SUBPROVIDER_NAME] = new GenericProvider({\n              namespace: combinedNamespace,\n            });\n          } else {\n            this.rpcProviders[GENERIC_SUBPROVIDER_NAME].updateNamespace(combinedNamespace);\n          }\n      }\n    });\n  }\n\n  private registerEventListeners(): void {\n    if (typeof this.client === \"undefined\") {\n      throw new Error(\"Sign Client is not initialized\");\n    }\n\n    this.client.on(\"session_ping\", (args) => {\n      const { topic } = args;\n      if (topic !== this.session?.topic) return;\n      this.events.emit(\"session_ping\", args);\n    });\n\n    this.client.on(\"session_event\", (args) => {\n      const { params, topic } = args;\n      if (topic !== this.session?.topic) return;\n      const { event } = params;\n      if (event.name === \"accountsChanged\") {\n        const accounts = event.data;\n        if (accounts && isValidArray(accounts))\n          this.events.emit(\"accountsChanged\", accounts.map(parseCaip10Account));\n      } else if (event.name === \"chainChanged\") {\n        const requestChainId = params.chainId;\n        const payloadChainId = params.event.data as number;\n        const namespace = parseNamespaceKey(requestChainId);\n        // chainIds might differ between the request & payload - request is always in CAIP2 format, while payload might be string, number, CAIP2 or hex\n        // take priority of the payload chainId\n        const chainIdToProcess =\n          convertChainIdToNumber(requestChainId) !== convertChainIdToNumber(payloadChainId)\n            ? `${namespace}:${convertChainIdToNumber(payloadChainId)}`\n            : requestChainId;\n\n        this.onChainChanged(chainIdToProcess);\n      } else {\n        this.events.emit(event.name, event.data);\n      }\n\n      this.events.emit(\"session_event\", args);\n    });\n\n    this.client.on(\"session_update\", ({ topic, params }) => {\n      if (topic !== this.session?.topic) return;\n      const { namespaces } = params;\n      const _session = this.client?.session.get(topic);\n      this.session = { ..._session, namespaces } as SessionTypes.Struct;\n      this.onSessionUpdate();\n      this.events.emit(\"session_update\", { topic, params });\n    });\n\n    this.client.on(\"session_delete\", async (payload) => {\n      if (payload.topic !== this.session?.topic) return;\n      await this.cleanup();\n      this.events.emit(\"session_delete\", payload);\n      this.events.emit(\"disconnect\", {\n        ...getSdkError(\"USER_DISCONNECTED\"),\n        data: payload.topic,\n      });\n    });\n\n    this.on(PROVIDER_EVENTS.DEFAULT_CHAIN_CHANGED, (caip2ChainId: string) => {\n      this.onChainChanged(caip2ChainId, true);\n    });\n  }\n\n  private getProvider(namespace: string): IProvider {\n    return this.rpcProviders[namespace] || this.rpcProviders[GENERIC_SUBPROVIDER_NAME];\n  }\n\n  private onSessionUpdate(): void {\n    Object.keys(this.rpcProviders).forEach((namespace: string) => {\n      this.getProvider(namespace).updateNamespace(\n        this.session?.namespaces[namespace] as SessionTypes.BaseNamespace,\n      );\n    });\n  }\n\n  private setNamespaces(params: ConnectParams): void {\n    const {\n      namespaces = {},\n      optionalNamespaces = {},\n      sessionProperties,\n      scopedProperties,\n    } = params;\n\n    // requiredNamespaces are deprecated, assign them to optionalNamespaces\n    this.optionalNamespaces = mergeRequiredOptionalNamespaces(namespaces, optionalNamespaces);\n    this.sessionProperties = sessionProperties;\n    this.scopedProperties = scopedProperties;\n  }\n\n  private validateChain(chain?: string): [string, string] {\n    const [namespace, chainId] = chain?.split(\":\") || [\"\", \"\"];\n    if (!this.namespaces || !Object.keys(this.namespaces).length) return [namespace, chainId];\n    // validate namespace\n    if (namespace) {\n      if (\n        // some namespaces might be defined with inline chainId e.g. eip155:1\n        // and we need to parse them\n        !Object.keys(this.namespaces || {})\n          .map((key) => parseNamespaceKey(key))\n          .includes(namespace)\n      ) {\n        throw new Error(\n          `Namespace '${namespace}' is not configured. Please call connect() first with namespace config.`,\n        );\n      }\n    }\n    if (namespace && chainId) {\n      return [namespace, chainId];\n    }\n    const defaultNamespace = parseNamespaceKey(Object.keys(this.namespaces)[0]);\n    const defaultChain = this.rpcProviders[defaultNamespace].getDefaultChain();\n    return [defaultNamespace, defaultChain];\n  }\n\n  private async requestAccounts(): Promise<string[]> {\n    const [namespace] = this.validateChain();\n    return await this.getProvider(namespace).requestAccounts();\n  }\n\n  private async onChainChanged(caip2Chain: string, internal = false): Promise<void> {\n    if (!this.namespaces) return;\n\n    const [namespace, chainId] = this.validateChain(caip2Chain);\n\n    if (!chainId) return;\n\n    this.updateNamespaceChain(namespace, chainId);\n\n    this.events.emit(\"chainChanged\", chainId);\n\n    const previousChainId = this.getProvider(namespace).getDefaultChain();\n    if (!internal) {\n      this.getProvider(namespace).setDefaultChain(chainId);\n    }\n\n    this.emitAccountsChangedOnChainChange({ namespace, previousChainId, newChainId: caip2Chain });\n    await this.persist(\"namespaces\", this.namespaces);\n  }\n\n  /**\n   * Emits `accountsChanged` event when a chain is changed and there are new accounts on the new chain\n   */\n  private emitAccountsChangedOnChainChange({\n    namespace,\n    previousChainId,\n    newChainId,\n  }: {\n    namespace: string;\n    previousChainId: string;\n    newChainId: string;\n  }): void {\n    try {\n      if (previousChainId === newChainId) {\n        return;\n      }\n\n      const accounts = this.session?.namespaces[namespace]?.accounts;\n      if (!accounts) return;\n      const newChainIdAccounts = accounts\n        .filter((account) => account.includes(`${newChainId}:`))\n        .map(parseCaip10Account);\n      if (!isValidArray(newChainIdAccounts)) return;\n      this.events.emit(\"accountsChanged\", newChainIdAccounts);\n    } catch (error) {\n      this.logger.warn(\"Failed to emit accountsChanged on chain change\", error);\n    }\n  }\n\n  private updateNamespaceChain(namespace: string, chainId: string): void {\n    if (!this.namespaces) return;\n\n    const namespaceKey = this.namespaces[namespace] ? namespace : `${namespace}:${chainId}`;\n\n    const defaultNamespace = {\n      chains: [],\n      methods: [],\n      events: [],\n      defaultChain: chainId,\n    };\n\n    if (!this.namespaces[namespaceKey]) {\n      this.namespaces[namespaceKey] = defaultNamespace;\n    } else if (this.namespaces[namespaceKey]) {\n      this.namespaces[namespaceKey].defaultChain = chainId;\n    }\n  }\n\n  private onConnect() {\n    this.createProviders();\n    this.events.emit(\"connect\", { session: this.session });\n  }\n\n  private async cleanup() {\n    this.namespaces = undefined;\n    this.optionalNamespaces = undefined;\n    this.sessionProperties = undefined;\n    await this.deleteFromStore(\"namespaces\");\n    await this.deleteFromStore(\"optionalNamespaces\");\n    await this.deleteFromStore(\"sessionProperties\");\n    // reset the session after removing from store as the topic is used there\n    this.session = undefined;\n    await this.cleanupPendingPairings({ deletePairings: true });\n    await this.cleanupStorage();\n  }\n\n  private async persist(key: string, data: unknown) {\n    const topic = this.session?.topic || \"\";\n    await this.client.core.storage.setItem(`${STORAGE}/${key}${topic}`, data);\n  }\n\n  private async getFromStore(key: string) {\n    const topic = this.session?.topic || \"\";\n    return await this.client.core.storage.getItem(`${STORAGE}/${key}${topic}`);\n  }\n\n  private async deleteFromStore(key: string) {\n    const topic = this.session?.topic || \"\";\n    await this.client.core.storage.removeItem(`${STORAGE}/${key}${topic}`);\n  }\n\n  // remove all storage items if there are no sessions left\n  private async cleanupStorage() {\n    try {\n      if (this.client?.session.length > 0) {\n        return;\n      }\n      const keys = await this.client.core.storage.getKeys();\n      for (const key of keys) {\n        if (key.startsWith(STORAGE)) {\n          await this.client.core.storage.removeItem(key);\n        }\n      }\n    } catch (error) {\n      this.logger.warn(\"Failed to cleanup storage\", error);\n    }\n  }\n}\nexport default UniversalProvider;\n", "import { UniversalProvider as Provider } from \"./UniversalProvider\";\nexport * from \"./types\";\nexport const UniversalProvider = Provider;\nexport default Provider;\n"], "names": ["isTypedArray", "cloneDeepWith", "chainId", "rpc", "projectId", "_a", "chain", "parseChainId", "RPC_URL", "accounts", "address", "namespace", "session", "matchedNamespaceKeys", "key", "accountsForNamespace", "required", "optional", "requiredNamespaces", "optionalNamespaces", "merge", "namespaces", "_b", "_c", "_d", "_e", "normalizedNamespaces", "isValidObject", "values", "chains", "isCaipNamespace", "methods", "events", "rpcMap", "normalizedKey", "parseNamespaceKey", "__spreadProps", "__spreadValues", "mergeArrays", "caip10Account", "parsedNamespaces", "globals", "value", "p", "a", "r", "<PERSON><PERSON><PERSON><PERSON>", "opts", "__publicField", "getGlobal", "args", "rpcUrl", "PROVIDER_EVENTS", "account", "http", "parsed<PERSON><PERSON><PERSON><PERSON>d", "get<PERSON>hainId", "getRpcUrl", "JsonRpcProvider", "HttpConnection", "Eip155Provider", "parsed<PERSON><PERSON><PERSON>", "hexChainId", "chainIds", "capabilitiesKey", "sessionCapabilities", "capabilities", "error", "bundlerName", "bundlerUrl", "customUrl", "url", "response", "formatJsonRpcRequest", "cap2ChainId", "BUNDLER_URL", "Solana<PERSON><PERSON><PERSON>", "CosmosProvider", "AlgorandProvider", "CardanoProvider", "rpcURL", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MultiversXProvider", "NearProvider", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "GenericProvider", "GENERIC_SUBPROVIDER_NAME", "UniversalProvider", "EventEmitter", "pino", "getDefaultLoggerOptions", "LOGGER", "provider", "expiry", "callback", "id", "formatJsonRpcResult", "getSdkError", "walletUniversalLink", "uri", "result", "approved", "populateNamespacesChains", "mergeRequiredOptionalNamespaces", "event", "listener", "pairingTopic", "approval", "inactivePairings", "isValidArray", "pairing", "SignClient", "RELAY_URL", "sessions", "providersToCreate", "setGlobal", "getAccountsFromSession", "<PERSON><PERSON><PERSON><PERSON>", "getChainsFromApprovedSession", "mergedNamespaces", "combinedNamespace", "topic", "params", "parseCaip10Account", "requestChainId", "payloadChainId", "chainIdToProcess", "convertChainIdToNumber", "_session", "payload", "caip2ChainId", "sessionProperties", "scopedProperties", "defaultNamespace", "defaultChain", "caip2Chain", "internal", "previousChainId", "newChainId", "newChainIdAccounts", "namespaceKey", "data", "STORAGE", "keys", "Provider"], "mappings": "", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32], "debugId": null}}, {"offset": {"line": 1482, "column": 0}, "map": {"version": 3, "file": "index.es.js", "sources": ["file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/node_modules/%40reown/appkit-controllers/node_modules/%40walletconnect/universal-provider/src/constants/values.ts", "file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/node_modules/%40reown/appkit-controllers/node_modules/%40walletconnect/universal-provider/src/constants/events.ts", "file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/node_modules/%40reown/appkit-controllers/node_modules/node_modules/es-toolkit/dist/function/noop.mjs", "file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/node_modules/%40reown/appkit-controllers/node_modules/node_modules/es-toolkit/dist/predicate/isPrimitive.mjs", "file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/node_modules/%40reown/appkit-controllers/node_modules/node_modules/es-toolkit/dist/predicate/isTypedArray.mjs", "file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/node_modules/%40reown/appkit-controllers/node_modules/node_modules/es-toolkit/dist/object/clone.mjs", "file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/node_modules/%40reown/appkit-controllers/node_modules/node_modules/es-toolkit/dist/compat/predicate/isObjectLike.mjs", "file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/node_modules/%40reown/appkit-controllers/node_modules/node_modules/es-toolkit/dist/compat/_internal/getSymbols.mjs", "file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/node_modules/%40reown/appkit-controllers/node_modules/node_modules/es-toolkit/dist/compat/_internal/getTag.mjs", "file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/node_modules/%40reown/appkit-controllers/node_modules/node_modules/es-toolkit/dist/compat/_internal/tags.mjs", "file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/node_modules/%40reown/appkit-controllers/node_modules/node_modules/es-toolkit/dist/object/cloneDeepWith.mjs", "file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/node_modules/%40reown/appkit-controllers/node_modules/node_modules/es-toolkit/dist/compat/object/cloneDeepWith.mjs", "file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/node_modules/%40reown/appkit-controllers/node_modules/node_modules/es-toolkit/dist/compat/object/cloneDeep.mjs", "file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/node_modules/%40reown/appkit-controllers/node_modules/node_modules/es-toolkit/dist/compat/predicate/isArguments.mjs", "file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/node_modules/%40reown/appkit-controllers/node_modules/node_modules/es-toolkit/dist/compat/predicate/isTypedArray.mjs", "file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/node_modules/%40reown/appkit-controllers/node_modules/node_modules/es-toolkit/dist/compat/predicate/isPlainObject.mjs", "file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/node_modules/%40reown/appkit-controllers/node_modules/node_modules/es-toolkit/dist/compat/object/mergeWith.mjs", "file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/node_modules/%40reown/appkit-controllers/node_modules/node_modules/es-toolkit/dist/compat/object/merge.mjs", "file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/node_modules/%40reown/appkit-controllers/node_modules/%40walletconnect/universal-provider/src/utils/misc.ts", "file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/node_modules/%40reown/appkit-controllers/node_modules/%40walletconnect/universal-provider/src/utils/globals.ts", "file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/node_modules/%40reown/appkit-controllers/node_modules/%40walletconnect/universal-provider/src/providers/polkadot.ts", "file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/node_modules/%40reown/appkit-controllers/node_modules/%40walletconnect/universal-provider/src/providers/eip155.ts", "file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/node_modules/%40reown/appkit-controllers/node_modules/%40walletconnect/universal-provider/src/providers/solana.ts", "file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/node_modules/%40reown/appkit-controllers/node_modules/%40walletconnect/universal-provider/src/providers/cosmos.ts", "file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/node_modules/%40reown/appkit-controllers/node_modules/%40walletconnect/universal-provider/src/providers/algorand.ts", "file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/node_modules/%40reown/appkit-controllers/node_modules/%40walletconnect/universal-provider/src/providers/cardano.ts", "file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/node_modules/%40reown/appkit-controllers/node_modules/%40walletconnect/universal-provider/src/providers/elrond.ts", "file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/node_modules/%40reown/appkit-controllers/node_modules/%40walletconnect/universal-provider/src/providers/multiversx.ts", "file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/node_modules/%40reown/appkit-controllers/node_modules/%40walletconnect/universal-provider/src/providers/near.ts", "file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/node_modules/%40reown/appkit-controllers/node_modules/%40walletconnect/universal-provider/src/providers/tezos.ts", "file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/node_modules/%40reown/appkit-controllers/node_modules/%40walletconnect/universal-provider/src/providers/generic.ts", "file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/node_modules/%40reown/appkit-controllers/node_modules/%40walletconnect/universal-provider/src/UniversalProvider.ts", "file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/node_modules/%40reown/appkit-controllers/node_modules/%40walletconnect/universal-provider/src/index.ts"], "sourcesContent": ["export const LOGGER = \"error\";\n\nexport const RELAY_URL = \"wss://relay.walletconnect.org\";\n\nexport const PROTOCOL = \"wc\";\nexport const WC_VERSION = 2;\nexport const CONTEXT = \"universal_provider\";\n\nexport const STORAGE = `${PROTOCOL}@${WC_VERSION}:${CONTEXT}:`;\n\nexport const RPC_URL = \"https://rpc.walletconnect.org/v1/\";\n\nexport const GENERIC_SUBPROVIDER_NAME = \"generic\";\n\nexport const BUNDLER_URL = `${RPC_URL}bundler`;\n", "export const PROVIDER_EVENTS = {\n  DEFAULT_CHAIN_CHANGED: \"default_chain_changed\",\n};\n", "function noop() { }\n\nexport { noop };\n", "function isPrimitive(value) {\n    return value == null || (typeof value !== 'object' && typeof value !== 'function');\n}\n\nexport { isPrimitive };\n", "function isTypedArray(x) {\n    return ArrayBuffer.isView(x) && !(x instanceof DataView);\n}\n\nexport { isTypedArray };\n", "import { isPrimitive } from '../predicate/isPrimitive.mjs';\nimport { isTypedArray } from '../predicate/isTypedArray.mjs';\n\nfunction clone(obj) {\n    if (isPrimitive(obj)) {\n        return obj;\n    }\n    if (Array.isArray(obj) ||\n        isTypedArray(obj) ||\n        obj instanceof ArrayBuffer ||\n        (typeof SharedArrayBuffer !== 'undefined' && obj instanceof SharedArrayBuffer)) {\n        return obj.slice(0);\n    }\n    const prototype = Object.getPrototypeOf(obj);\n    const Constructor = prototype.constructor;\n    if (obj instanceof Date || obj instanceof Map || obj instanceof Set) {\n        return new Constructor(obj);\n    }\n    if (obj instanceof RegExp) {\n        const newRegExp = new Constructor(obj);\n        newRegExp.lastIndex = obj.lastIndex;\n        return newRegExp;\n    }\n    if (obj instanceof DataView) {\n        return new Constructor(obj.buffer.slice(0));\n    }\n    if (obj instanceof Error) {\n        const newError = new Constructor(obj.message);\n        newError.stack = obj.stack;\n        newError.name = obj.name;\n        newError.cause = obj.cause;\n        return newError;\n    }\n    if (typeof File !== 'undefined' && obj instanceof File) {\n        const newFile = new Constructor([obj], obj.name, { type: obj.type, lastModified: obj.lastModified });\n        return newFile;\n    }\n    if (typeof obj === 'object') {\n        const newObject = Object.create(prototype);\n        return Object.assign(newObject, obj);\n    }\n    return obj;\n}\n\nexport { clone };\n", "function isObjectLike(value) {\n    return typeof value === 'object' && value !== null;\n}\n\nexport { isObjectLike };\n", "function getSymbols(object) {\n    return Object.getOwnPropertySymbols(object).filter(symbol => Object.prototype.propertyIsEnumerable.call(object, symbol));\n}\n\nexport { getSymbols };\n", "function getTag(value) {\n    if (value == null) {\n        return value === undefined ? '[object Undefined]' : '[object Null]';\n    }\n    return Object.prototype.toString.call(value);\n}\n\nexport { getTag };\n", "const regexpTag = '[object RegExp]';\nconst stringTag = '[object String]';\nconst numberTag = '[object Number]';\nconst booleanTag = '[object Boolean]';\nconst argumentsTag = '[object Arguments]';\nconst symbolTag = '[object Symbol]';\nconst dateTag = '[object Date]';\nconst mapTag = '[object Map]';\nconst setTag = '[object Set]';\nconst arrayTag = '[object Array]';\nconst functionTag = '[object Function]';\nconst arrayBufferTag = '[object ArrayBuffer]';\nconst objectTag = '[object Object]';\nconst errorTag = '[object Error]';\nconst dataViewTag = '[object DataView]';\nconst uint8ArrayTag = '[object Uint8Array]';\nconst uint8ClampedArrayTag = '[object Uint8ClampedArray]';\nconst uint16ArrayTag = '[object Uint16Array]';\nconst uint32ArrayTag = '[object Uint32Array]';\nconst bigUint64ArrayTag = '[object BigUint64Array]';\nconst int8ArrayTag = '[object Int8Array]';\nconst int16ArrayTag = '[object Int16Array]';\nconst int32ArrayTag = '[object Int32Array]';\nconst bigInt64ArrayTag = '[object BigInt64Array]';\nconst float32ArrayTag = '[object Float32Array]';\nconst float64ArrayTag = '[object Float64Array]';\n\nexport { argumentsTag, arrayBufferTag, arrayTag, bigInt64ArrayTag, bigUint64ArrayTag, booleanTag, dataViewTag, dateTag, errorTag, float32ArrayTag, float64ArrayTag, functionTag, int16ArrayTag, int32ArrayTag, int8ArrayTag, mapTag, numberTag, objectTag, regexpTag, setTag, stringTag, symbolTag, uint16ArrayTag, uint32ArrayTag, uint8ArrayTag, uint8ClampedArrayTag };\n", "import { getSymbols } from '../compat/_internal/getSymbols.mjs';\nimport { getTag } from '../compat/_internal/getTag.mjs';\nimport { uint32ArrayTag, uint16ArrayTag, uint8ClampedArrayTag, uint8ArrayTag, symbolTag, stringTag, setTag, regexpTag, objectTag, numberTag, mapTag, int32ArrayTag, int16ArrayTag, int8ArrayTag, float64ArrayTag, float32ArrayTag, dateTag, booleanTag, dataViewTag, arrayBufferTag, arrayTag, argumentsTag } from '../compat/_internal/tags.mjs';\nimport { isPrimitive } from '../predicate/isPrimitive.mjs';\nimport { isTypedArray } from '../predicate/isTypedArray.mjs';\n\nfunction cloneDeepWith(obj, cloneValue) {\n    return cloneDeepWithImpl(obj, undefined, obj, new Map(), cloneValue);\n}\nfunction cloneDeepWithImpl(valueToClone, keyToClone, objectToClone, stack = new Map(), cloneValue = undefined) {\n    const cloned = cloneValue?.(valueToClone, keyToClone, objectToClone, stack);\n    if (cloned != null) {\n        return cloned;\n    }\n    if (isPrimitive(valueToClone)) {\n        return valueToClone;\n    }\n    if (stack.has(valueToClone)) {\n        return stack.get(valueToClone);\n    }\n    if (Array.isArray(valueToClone)) {\n        const result = new Array(valueToClone.length);\n        stack.set(valueToClone, result);\n        for (let i = 0; i < valueToClone.length; i++) {\n            result[i] = cloneDeepWithImpl(valueToClone[i], i, objectToClone, stack, cloneValue);\n        }\n        if (Object.hasOwn(valueToClone, 'index')) {\n            result.index = valueToClone.index;\n        }\n        if (Object.hasOwn(valueToClone, 'input')) {\n            result.input = valueToClone.input;\n        }\n        return result;\n    }\n    if (valueToClone instanceof Date) {\n        return new Date(valueToClone.getTime());\n    }\n    if (valueToClone instanceof RegExp) {\n        const result = new RegExp(valueToClone.source, valueToClone.flags);\n        result.lastIndex = valueToClone.lastIndex;\n        return result;\n    }\n    if (valueToClone instanceof Map) {\n        const result = new Map();\n        stack.set(valueToClone, result);\n        for (const [key, value] of valueToClone) {\n            result.set(key, cloneDeepWithImpl(value, key, objectToClone, stack, cloneValue));\n        }\n        return result;\n    }\n    if (valueToClone instanceof Set) {\n        const result = new Set();\n        stack.set(valueToClone, result);\n        for (const value of valueToClone) {\n            result.add(cloneDeepWithImpl(value, undefined, objectToClone, stack, cloneValue));\n        }\n        return result;\n    }\n    if (typeof Buffer !== 'undefined' && Buffer.isBuffer(valueToClone)) {\n        return valueToClone.subarray();\n    }\n    if (isTypedArray(valueToClone)) {\n        const result = new (Object.getPrototypeOf(valueToClone).constructor)(valueToClone.length);\n        stack.set(valueToClone, result);\n        for (let i = 0; i < valueToClone.length; i++) {\n            result[i] = cloneDeepWithImpl(valueToClone[i], i, objectToClone, stack, cloneValue);\n        }\n        return result;\n    }\n    if (valueToClone instanceof ArrayBuffer ||\n        (typeof SharedArrayBuffer !== 'undefined' && valueToClone instanceof SharedArrayBuffer)) {\n        return valueToClone.slice(0);\n    }\n    if (valueToClone instanceof DataView) {\n        const result = new DataView(valueToClone.buffer.slice(0), valueToClone.byteOffset, valueToClone.byteLength);\n        stack.set(valueToClone, result);\n        copyProperties(result, valueToClone, objectToClone, stack, cloneValue);\n        return result;\n    }\n    if (typeof File !== 'undefined' && valueToClone instanceof File) {\n        const result = new File([valueToClone], valueToClone.name, {\n            type: valueToClone.type,\n        });\n        stack.set(valueToClone, result);\n        copyProperties(result, valueToClone, objectToClone, stack, cloneValue);\n        return result;\n    }\n    if (valueToClone instanceof Blob) {\n        const result = new Blob([valueToClone], { type: valueToClone.type });\n        stack.set(valueToClone, result);\n        copyProperties(result, valueToClone, objectToClone, stack, cloneValue);\n        return result;\n    }\n    if (valueToClone instanceof Error) {\n        const result = new valueToClone.constructor();\n        stack.set(valueToClone, result);\n        result.message = valueToClone.message;\n        result.name = valueToClone.name;\n        result.stack = valueToClone.stack;\n        result.cause = valueToClone.cause;\n        copyProperties(result, valueToClone, objectToClone, stack, cloneValue);\n        return result;\n    }\n    if (typeof valueToClone === 'object' && isCloneableObject(valueToClone)) {\n        const result = Object.create(Object.getPrototypeOf(valueToClone));\n        stack.set(valueToClone, result);\n        copyProperties(result, valueToClone, objectToClone, stack, cloneValue);\n        return result;\n    }\n    return valueToClone;\n}\nfunction copyProperties(target, source, objectToClone = target, stack, cloneValue) {\n    const keys = [...Object.keys(source), ...getSymbols(source)];\n    for (let i = 0; i < keys.length; i++) {\n        const key = keys[i];\n        const descriptor = Object.getOwnPropertyDescriptor(target, key);\n        if (descriptor == null || descriptor.writable) {\n            target[key] = cloneDeepWithImpl(source[key], key, objectToClone, stack, cloneValue);\n        }\n    }\n}\nfunction isCloneableObject(object) {\n    switch (getTag(object)) {\n        case argumentsTag:\n        case arrayTag:\n        case arrayBufferTag:\n        case dataViewTag:\n        case booleanTag:\n        case dateTag:\n        case float32ArrayTag:\n        case float64ArrayTag:\n        case int8ArrayTag:\n        case int16ArrayTag:\n        case int32ArrayTag:\n        case mapTag:\n        case numberTag:\n        case objectTag:\n        case regexpTag:\n        case setTag:\n        case stringTag:\n        case symbolTag:\n        case uint8ArrayTag:\n        case uint8ClampedArrayTag:\n        case uint16ArrayTag:\n        case uint32ArrayTag: {\n            return true;\n        }\n        default: {\n            return false;\n        }\n    }\n}\n\nexport { cloneDeepWith, cloneDeepWithImpl, copyProperties };\n", "import { cloneDeep<PERSON>ith as cloneDeep<PERSON>ith$1, copyProperties } from '../../object/cloneDeepWith.mjs';\nimport { argumentsTag, booleanTag, stringTag, numberTag } from '../_internal/tags.mjs';\n\nfunction cloneDeepWith(obj, cloneValue) {\n    return cloneDeepWith$1(obj, (value, key, object, stack) => {\n        const cloned = cloneValue?.(value, key, object, stack);\n        if (cloned != null) {\n            return cloned;\n        }\n        if (typeof obj !== 'object') {\n            return undefined;\n        }\n        switch (Object.prototype.toString.call(obj)) {\n            case numberTag:\n            case stringTag:\n            case booleanTag: {\n                const result = new obj.constructor(obj?.valueOf());\n                copyProperties(result, obj);\n                return result;\n            }\n            case argumentsTag: {\n                const result = {};\n                copyProperties(result, obj);\n                result.length = obj.length;\n                result[Symbol.iterator] = obj[Symbol.iterator];\n                return result;\n            }\n            default: {\n                return undefined;\n            }\n        }\n    });\n}\n\nexport { cloneDeepWith };\n", "import { cloneDeepWith } from './cloneDeepWith.mjs';\n\nfunction cloneDeep(obj) {\n    return cloneDeepWith(obj);\n}\n\nexport { cloneDeep };\n", "import { getTag } from '../_internal/getTag.mjs';\n\nfunction isArguments(value) {\n    return value !== null && typeof value === 'object' && getTag(value) === '[object Arguments]';\n}\n\nexport { isArguments };\n", "import { isTypedArray as isTypedArray$1 } from '../../predicate/isTypedArray.mjs';\n\nfunction isTypedArray(x) {\n    return isTypedArray$1(x);\n}\n\nexport { isTypedArray };\n", "function isPlainObject(object) {\n    if (typeof object !== 'object') {\n        return false;\n    }\n    if (object == null) {\n        return false;\n    }\n    if (Object.getPrototypeOf(object) === null) {\n        return true;\n    }\n    if (Object.prototype.toString.call(object) !== '[object Object]') {\n        const tag = object[Symbol.toStringTag];\n        if (tag == null) {\n            return false;\n        }\n        const isTagReadonly = !Object.getOwnPropertyDescriptor(object, Symbol.toStringTag)?.writable;\n        if (isTagReadonly) {\n            return false;\n        }\n        return object.toString() === `[object ${tag}]`;\n    }\n    let proto = object;\n    while (Object.getPrototypeOf(proto) !== null) {\n        proto = Object.getPrototypeOf(proto);\n    }\n    return Object.getPrototypeOf(object) === proto;\n}\n\nexport { isPlainObject };\n", "import { cloneDeep } from './cloneDeep.mjs';\nimport { clone } from '../../object/clone.mjs';\nimport { isPrimitive } from '../../predicate/isPrimitive.mjs';\nimport { getSymbols } from '../_internal/getSymbols.mjs';\nimport { isArguments } from '../predicate/isArguments.mjs';\nimport { isObjectLike } from '../predicate/isObjectLike.mjs';\nimport { isPlainObject } from '../predicate/isPlainObject.mjs';\nimport { isTypedArray } from '../predicate/isTypedArray.mjs';\n\nfunction mergeWith(object, ...otherArgs) {\n    const sources = otherArgs.slice(0, -1);\n    const merge = otherArgs[otherArgs.length - 1];\n    let result = object;\n    for (let i = 0; i < sources.length; i++) {\n        const source = sources[i];\n        result = mergeWithDeep(result, source, merge, new Map());\n    }\n    return result;\n}\nfunction mergeWithDeep(target, source, merge, stack) {\n    if (isPrimitive(target)) {\n        target = Object(target);\n    }\n    if (source == null || typeof source !== 'object') {\n        return target;\n    }\n    if (stack.has(source)) {\n        return clone(stack.get(source));\n    }\n    stack.set(source, target);\n    if (Array.isArray(source)) {\n        source = source.slice();\n        for (let i = 0; i < source.length; i++) {\n            source[i] = source[i] ?? undefined;\n        }\n    }\n    const sourceKeys = [...Object.keys(source), ...getSymbols(source)];\n    for (let i = 0; i < sourceKeys.length; i++) {\n        const key = sourceKeys[i];\n        let sourceValue = source[key];\n        let targetValue = target[key];\n        if (isArguments(sourceValue)) {\n            sourceValue = { ...sourceValue };\n        }\n        if (isArguments(targetValue)) {\n            targetValue = { ...targetValue };\n        }\n        if (typeof Buffer !== 'undefined' && Buffer.isBuffer(sourceValue)) {\n            sourceValue = cloneDeep(sourceValue);\n        }\n        if (Array.isArray(sourceValue)) {\n            if (typeof targetValue === 'object' && targetValue != null) {\n                const cloned = [];\n                const targetKeys = Reflect.ownKeys(targetValue);\n                for (let i = 0; i < targetKeys.length; i++) {\n                    const targetKey = targetKeys[i];\n                    cloned[targetKey] = targetValue[targetKey];\n                }\n                targetValue = cloned;\n            }\n            else {\n                targetValue = [];\n            }\n        }\n        const merged = merge(targetValue, sourceValue, key, target, source, stack);\n        if (merged != null) {\n            target[key] = merged;\n        }\n        else if (Array.isArray(sourceValue)) {\n            target[key] = mergeWithDeep(targetValue, sourceValue, merge, stack);\n        }\n        else if (isObjectLike(targetValue) && isObjectLike(sourceValue)) {\n            target[key] = mergeWithDeep(targetValue, sourceValue, merge, stack);\n        }\n        else if (targetValue == null && isPlainObject(sourceValue)) {\n            target[key] = mergeWithDeep({}, sourceValue, merge, stack);\n        }\n        else if (targetValue == null && isTypedArray(sourceValue)) {\n            target[key] = cloneDeep(sourceValue);\n        }\n        else if (targetValue === undefined || sourceValue !== undefined) {\n            target[key] = sourceValue;\n        }\n    }\n    return target;\n}\n\nexport { mergeWith };\n", "import { mergeWith } from './mergeWith.mjs';\nimport { noop } from '../../function/noop.mjs';\n\nfunction merge(object, ...sources) {\n    return mergeWith(object, ...sources, noop);\n}\n\nexport { merge };\n", "import { SessionTypes } from \"@walletconnect/types\";\nimport {\n  isCaipNamespace,\n  isValidObject,\n  mergeArrays,\n  parseChainId,\n  parseNamespaceKey,\n} from \"@walletconnect/utils\";\nimport { RPC_URL } from \"../constants\";\nimport { Namespace, NamespaceConfig } from \"../types\";\nimport { merge } from \"es-toolkit/compat\";\n\nexport function getRpcUrl(chainId: string, rpc: Namespace, projectId?: string): string | undefined {\n  const chain = parseChainId(chainId);\n  return (\n    rpc.rpcMap?.[chain.reference] ||\n    `${RPC_URL}?chainId=${chain.namespace}:${chain.reference}&projectId=${projectId}`\n  );\n}\n\nexport function getChainId(chain: string): string {\n  return chain.includes(\":\") ? chain.split(\":\")[1] : chain;\n}\n\nexport function validateChainApproval(chain: string, chains: string[]): void {\n  if (!chains.includes(chain)) {\n    throw new Error(\n      `Chain '${chain}' not approved. Please use one of the following: ${chains.toString()}`,\n    );\n  }\n}\n\nexport function getChainsFromApprovedSession(accounts: string[]): string[] {\n  return accounts.map((address) => `${address.split(\":\")[0]}:${address.split(\":\")[1]}`);\n}\n\nexport function getAccountsFromSession(namespace: string, session: SessionTypes.Struct): string[] {\n  // match namespaces e.g. eip155 with eip155:1\n  const matchedNamespaceKeys = Object.keys(session.namespaces).filter((key) =>\n    key.includes(namespace),\n  );\n  if (!matchedNamespaceKeys.length) return [];\n  const accounts: string[] = [];\n  matchedNamespaceKeys.forEach((key) => {\n    const accountsForNamespace = session.namespaces[key].accounts;\n    accounts.push(...accountsForNamespace);\n  });\n  return accounts;\n}\n\nexport function mergeRequiredOptionalNamespaces(\n  required: NamespaceConfig = {},\n  optional: NamespaceConfig = {},\n) {\n  const requiredNamespaces = normalizeNamespaces(required);\n  const optionalNamespaces = normalizeNamespaces(optional);\n  return merge(requiredNamespaces, optionalNamespaces);\n}\n\n/**\n * Converts\n * {\n *  \"eip155:1\": {...},\n *  \"eip155:2\": {...},\n * }\n * into\n * {\n *  \"eip155\": {\n *      chains: [\"eip155:1\", \"eip155:2\"],\n *      ...\n *    }\n * }\n *\n */\nexport function normalizeNamespaces(namespaces: NamespaceConfig): NamespaceConfig {\n  const normalizedNamespaces: NamespaceConfig = {};\n  if (!isValidObject(namespaces)) return normalizedNamespaces;\n\n  for (const [key, values] of Object.entries(namespaces)) {\n    const chains = isCaipNamespace(key) ? [key] : values.chains;\n    const methods = values.methods || [];\n    const events = values.events || [];\n    const rpcMap = values.rpcMap || {};\n    const normalizedKey = parseNamespaceKey(key);\n    normalizedNamespaces[normalizedKey] = {\n      ...normalizedNamespaces[normalizedKey],\n      ...values,\n      chains: mergeArrays(chains, normalizedNamespaces[normalizedKey]?.chains),\n      methods: mergeArrays(methods, normalizedNamespaces[normalizedKey]?.methods),\n      events: mergeArrays(events, normalizedNamespaces[normalizedKey]?.events),\n    };\n    // avoid adding empty `rpcMap: {}` if there are no values for it\n    if (isValidObject(rpcMap) || isValidObject(normalizedNamespaces[normalizedKey]?.rpcMap || {})) {\n      normalizedNamespaces[normalizedKey].rpcMap = {\n        ...rpcMap,\n        ...normalizedNamespaces[normalizedKey]?.rpcMap,\n      };\n    }\n  }\n  return normalizedNamespaces;\n}\n\nexport function parseCaip10Account(caip10Account: string): string {\n  return caip10Account.includes(\":\") ? caip10Account.split(\":\")[2] : caip10Account;\n}\n\n/**\n * Populates the chains array for each namespace with the chains extracted from the accounts if are otherwise missing\n */\nexport function populateNamespacesChains(\n  namespaces: SessionTypes.Namespaces,\n): Record<string, SessionTypes.Namespace> {\n  const parsedNamespaces: Record<string, SessionTypes.Namespace> = {};\n  for (const [key, values] of Object.entries(namespaces)) {\n    const methods = values.methods || [];\n    const events = values.events || [];\n    const accounts = values.accounts || [];\n    // If the key includes a CAIP separator `:` we know it's a namespace + chainId (e.g. `eip155:1`)\n    const chains = isCaipNamespace(key)\n      ? [key]\n      : values.chains\n        ? values.chains\n        : getChainsFromApprovedSession(values.accounts);\n    parsedNamespaces[key] = {\n      chains,\n      methods,\n      events,\n      accounts,\n    };\n  }\n  return parsedNamespaces;\n}\n\nexport function convertChainIdToNumber(chainId: string | number): number | string {\n  if (typeof chainId === \"number\") return chainId;\n  if (chainId.includes(\"0x\")) {\n    return parseInt(chainId, 16);\n  }\n\n  chainId = chainId.includes(\":\") ? chainId.split(\":\")[1] : chainId;\n  return isNaN(Number(chainId)) ? chainId : Number(chainId);\n}\n", "const globals = {};\nexport const getGlobal = (key: string) => {\n  return globals[key];\n};\n\nexport const setGlobal = (key: string, value: unknown) => {\n  globals[key] = value;\n};\n", "import HttpConnection from \"@walletconnect/jsonrpc-http-connection\";\nimport { JsonRpcProvider } from \"@walletconnect/jsonrpc-provider\";\nimport Client from \"@walletconnect/sign-client\";\nimport { EngineTypes, SessionTypes } from \"@walletconnect/types\";\nimport EventEmitter from \"events\";\nimport { PROVIDER_EVENTS } from \"../constants\";\nimport {\n  IProvider,\n  RequestParams,\n  RpcProvidersMap,\n  SessionNamespace,\n  SubProviderOpts,\n} from \"../types\";\n\nimport { getChainId, getGlobal, getRpcUrl } from \"../utils\";\n\nclass PolkadotProvider implements IProvider {\n  public name = \"polkadot\";\n  public client: Client;\n  public httpProviders: RpcProvidersMap;\n  public events: EventEmitter;\n  public namespace: SessionNamespace;\n  public chainId: string;\n\n  constructor(opts: SubProviderOpts) {\n    this.namespace = opts.namespace;\n    this.events = getGlobal(\"events\");\n    this.client = getGlobal(\"client\");\n    this.chainId = this.getDefaultChain();\n    this.httpProviders = this.createHttpProviders();\n  }\n\n  public updateNamespace(namespace: SessionTypes.Namespace) {\n    this.namespace = Object.assign(this.namespace, namespace);\n  }\n\n  public requestAccounts(): string[] {\n    return this.getAccounts();\n  }\n\n  public getDefaultChain(): string {\n    if (this.chainId) return this.chainId;\n    if (this.namespace.defaultChain) return this.namespace.defaultChain;\n\n    const chainId = this.namespace.chains[0];\n\n    if (!chainId) throw new Error(`ChainId not found`);\n\n    return chainId.split(\":\")[1];\n  }\n\n  public request<T = unknown>(args: RequestParams): Promise<T> {\n    if (this.namespace.methods.includes(args.request.method)) {\n      return this.client.request(args as EngineTypes.RequestParams);\n    }\n    return this.getHttpProvider().request(args.request);\n  }\n\n  public setDefaultChain(chainId: string, rpcUrl?: string | undefined) {\n    // http provider exists so just set the chainId\n    if (!this.httpProviders[chainId]) {\n      this.setHttpProvider(chainId, rpcUrl);\n    }\n    this.chainId = chainId;\n    this.events.emit(PROVIDER_EVENTS.DEFAULT_CHAIN_CHANGED, `${this.name}:${chainId}`);\n  }\n\n  // ---------------- PRIVATE ---------------- //\n\n  private getAccounts(): string[] {\n    const accounts = this.namespace.accounts;\n    if (!accounts) {\n      return [];\n    }\n\n    return (\n      accounts\n        // get the accounts from the active chain\n        .filter((account) => account.split(\":\")[1] === this.chainId.toString())\n        // remove namespace & chainId from the string\n        .map((account) => account.split(\":\")[2]) || []\n    );\n  }\n\n  private createHttpProviders(): RpcProvidersMap {\n    const http = {};\n    this.namespace.chains.forEach((chain) => {\n      const parsedChainId = getChainId(chain);\n      http[parsedChainId] = this.createHttpProvider(parsedChainId, this.namespace.rpcMap?.[chain]);\n    });\n    return http;\n  }\n\n  private getHttpProvider(): JsonRpcProvider {\n    const chain = `${this.name}:${this.chainId}`;\n    const http = this.httpProviders[chain];\n    if (typeof http === \"undefined\") {\n      throw new Error(`JSON-RPC provider for ${chain} not found`);\n    }\n    return http;\n  }\n\n  private setHttpProvider(chainId: string, rpcUrl?: string): void {\n    const http = this.createHttpProvider(chainId, rpcUrl);\n    if (http) {\n      this.httpProviders[chainId] = http;\n    }\n  }\n\n  private createHttpProvider(\n    chainId: string,\n    rpcUrl?: string | undefined,\n  ): JsonRpcProvider | undefined {\n    const rpc = rpcUrl || getRpcUrl(chainId, this.namespace, this.client.core.projectId);\n    if (!rpc) {\n      throw new Error(`No RPC url provided for chainId: ${chainId}`);\n    }\n    const http = new JsonRpcProvider(new HttpConnection(rpc, getGlobal(\"disableProviderPing\")));\n    return http;\n  }\n}\n\nexport default PolkadotProvider;\n", "import Client from \"@walletconnect/sign-client\";\nimport { JsonRpcProvider } from \"@walletconnect/jsonrpc-provider\";\nimport { HttpConnection } from \"@walletconnect/jsonrpc-http-connection\";\nimport { EngineTypes, SessionTypes } from \"@walletconnect/types\";\n\nimport {\n  IProvider,\n  RpcProvidersMap,\n  SubProviderOpts,\n  RequestParams,\n  SessionNamespace,\n} from \"../types\";\n\nimport { getChainId, getGlobal, getRpcUrl } from \"../utils\";\nimport EventEmitter from \"events\";\nimport { BUNDLER_URL, PROVIDER_EVENTS } from \"../constants\";\nimport { formatJsonRpcRequest } from \"@walletconnect/jsonrpc-utils\";\n\nclass Eip155Provider implements IProvider {\n  public name = \"eip155\";\n  public client: Client;\n  // the active chainId on the dapp\n  public chainId: number;\n  public namespace: SessionNamespace;\n  public httpProviders: RpcProvidersMap;\n  public events: EventEmitter;\n\n  constructor(opts: SubProviderOpts) {\n    this.namespace = opts.namespace;\n    this.events = getGlobal(\"events\");\n    this.client = getGlobal(\"client\");\n    this.httpProviders = this.createHttpProviders();\n    this.chainId = parseInt(this.getDefaultChain());\n  }\n\n  public async request<T = unknown>(args: RequestParams): Promise<T> {\n    switch (args.request.method) {\n      case \"eth_requestAccounts\":\n        return this.getAccounts() as unknown as T;\n      case \"eth_accounts\":\n        return this.getAccounts() as unknown as T;\n      case \"wallet_switchEthereumChain\": {\n        return (await this.handleSwitchChain(args)) as unknown as T;\n      }\n      case \"eth_chainId\":\n        return parseInt(this.getDefaultChain()) as unknown as T;\n      case \"wallet_getCapabilities\":\n        return (await this.getCapabilities(args)) as unknown as T;\n      case \"wallet_getCallsStatus\":\n        return (await this.getCallStatus(args)) as unknown as T;\n      default:\n        break;\n    }\n    if (this.namespace.methods.includes(args.request.method)) {\n      return await this.client.request(args as EngineTypes.RequestParams);\n    }\n    return this.getHttpProvider().request(args.request);\n  }\n\n  public updateNamespace(namespace: SessionTypes.Namespace) {\n    this.namespace = Object.assign(this.namespace, namespace);\n  }\n\n  public setDefaultChain(chainId: string, rpcUrl?: string | undefined) {\n    // http provider exists so just set the chainId\n    if (!this.httpProviders[chainId]) {\n      this.setHttpProvider(parseInt(chainId), rpcUrl);\n    }\n    this.chainId = parseInt(chainId);\n    this.events.emit(PROVIDER_EVENTS.DEFAULT_CHAIN_CHANGED, `${this.name}:${chainId}`);\n  }\n\n  public requestAccounts(): string[] {\n    return this.getAccounts();\n  }\n\n  public getDefaultChain(): string {\n    if (this.chainId) return this.chainId.toString();\n    if (this.namespace.defaultChain) return this.namespace.defaultChain;\n\n    const chainId = this.namespace.chains[0];\n    if (!chainId) throw new Error(`ChainId not found`);\n\n    return chainId.split(\":\")[1];\n  }\n\n  // ---------- Private ----------------------------------------------- //\n\n  private createHttpProvider(\n    chainId: number,\n    rpcUrl?: string | undefined,\n  ): JsonRpcProvider | undefined {\n    const rpc =\n      rpcUrl || getRpcUrl(`${this.name}:${chainId}`, this.namespace, this.client.core.projectId);\n    if (!rpc) {\n      throw new Error(`No RPC url provided for chainId: ${chainId}`);\n    }\n    const http = new JsonRpcProvider(new HttpConnection(rpc, getGlobal(\"disableProviderPing\")));\n    return http;\n  }\n\n  private setHttpProvider(chainId: number, rpcUrl?: string): void {\n    const http = this.createHttpProvider(chainId, rpcUrl);\n    if (http) {\n      this.httpProviders[chainId] = http;\n    }\n  }\n\n  private createHttpProviders(): RpcProvidersMap {\n    const http = {};\n    this.namespace.chains.forEach((chain) => {\n      const parsedChain = parseInt(getChainId(chain));\n      http[parsedChain] = this.createHttpProvider(parsedChain, this.namespace.rpcMap?.[chain]);\n    });\n    return http;\n  }\n\n  private getAccounts(): string[] {\n    const accounts = this.namespace.accounts;\n    if (!accounts) {\n      return [];\n    }\n    return [\n      ...new Set(\n        accounts\n          // get the accounts from the active chain\n          .filter((account) => account.split(\":\")[1] === this.chainId.toString())\n          // remove namespace & chainId from the string\n          .map((account) => account.split(\":\")[2]),\n      ),\n    ];\n  }\n\n  private getHttpProvider(): JsonRpcProvider {\n    const chain = this.chainId;\n    const http = this.httpProviders[chain];\n    if (typeof http === \"undefined\") {\n      throw new Error(`JSON-RPC provider for ${chain} not found`);\n    }\n    return http;\n  }\n\n  private async handleSwitchChain(args: RequestParams): Promise<any> {\n    let hexChainId = args.request.params ? args.request.params[0]?.chainId : \"0x0\";\n    hexChainId = hexChainId.startsWith(\"0x\") ? hexChainId : `0x${hexChainId}`;\n    const parsedChainId = parseInt(hexChainId, 16);\n    // if chainId is already approved, switch locally\n    if (this.isChainApproved(parsedChainId)) {\n      this.setDefaultChain(`${parsedChainId}`);\n    } else if (this.namespace.methods.includes(\"wallet_switchEthereumChain\")) {\n      // try to switch chain within the wallet\n      await this.client.request({\n        topic: args.topic,\n        request: {\n          method: args.request.method,\n          params: [\n            {\n              chainId: hexChainId,\n            },\n          ],\n        },\n        chainId: this.namespace.chains?.[0], // Sending a previously unapproved chainId will cause namespace validation failure so we must set request chainId to the first chainId in the namespace to avoid it\n      } as EngineTypes.RequestParams);\n      this.setDefaultChain(`${parsedChainId}`);\n    } else {\n      throw new Error(\n        `Failed to switch to chain 'eip155:${parsedChainId}'. The chain is not approved or the wallet does not support 'wallet_switchEthereumChain' method.`,\n      );\n    }\n    return null;\n  }\n\n  private isChainApproved(chainId: number): boolean {\n    return this.namespace.chains.includes(`${this.name}:${chainId}`);\n  }\n\n  private async getCapabilities(args: RequestParams) {\n    // if capabilities are stored in the session, return them, else send the request to the wallet\n    const address = args.request?.params?.[0];\n    const chainIds: string[] = args.request?.params?.[1] || [];\n\n    // cache key is address + chainIds to allow requests to be made to different chains\n    const capabilitiesKey = `${address}${chainIds.join(\",\")}`;\n    if (!address) throw new Error(\"Missing address parameter in `wallet_getCapabilities` request\");\n    const session = this.client.session.get(args.topic);\n    const sessionCapabilities = session?.sessionProperties?.capabilities || {};\n\n    if (sessionCapabilities?.[capabilitiesKey]) {\n      return sessionCapabilities?.[capabilitiesKey];\n    }\n\n    // intentionally omit catching errors/rejection during `request` to allow the error to bubble up\n    const capabilities = await this.client.request(args as EngineTypes.RequestParams);\n    try {\n      // update the session with the capabilities so they can be retrieved later\n      await this.client.session.update(args.topic, {\n        sessionProperties: {\n          ...(session.sessionProperties || {}),\n          capabilities: {\n            ...(sessionCapabilities || {}),\n            [capabilitiesKey]: capabilities,\n          } as any, // by spec sessionProperties should be <string, string> but here are used as objects?\n        },\n      });\n    } catch (error) {\n      console.warn(\"Failed to update session with capabilities\", error);\n    }\n    return capabilities;\n  }\n\n  private async getCallStatus(args: RequestParams) {\n    const session = this.client.session.get(args.topic);\n    const bundlerName = session.sessionProperties?.bundler_name;\n    if (bundlerName) {\n      const bundlerUrl = this.getBundlerUrl(args.chainId, bundlerName);\n      try {\n        return await this.getUserOperationReceipt(bundlerUrl, args);\n      } catch (error) {\n        console.warn(\"Failed to fetch call status from bundler\", error, bundlerUrl);\n      }\n    }\n    const customUrl = session.sessionProperties?.bundler_url;\n    if (customUrl) {\n      try {\n        return await this.getUserOperationReceipt(customUrl, args);\n      } catch (error) {\n        console.warn(\"Failed to fetch call status from custom bundler\", error, customUrl);\n      }\n    }\n\n    if (this.namespace.methods.includes(args.request.method)) {\n      return await this.client.request(args as EngineTypes.RequestParams);\n    }\n\n    throw new Error(\"Fetching call status not approved by the wallet.\");\n  }\n\n  private async getUserOperationReceipt(bundlerUrl: string, args: RequestParams) {\n    const url = new URL(bundlerUrl);\n    const response = await fetch(url, {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n      },\n      body: JSON.stringify(\n        formatJsonRpcRequest(\"eth_getUserOperationReceipt\", [args.request.params?.[0]]),\n      ),\n    });\n    if (!response.ok) {\n      throw new Error(`Failed to fetch user operation receipt - ${response.status}`);\n    }\n    return await response.json();\n  }\n\n  private getBundlerUrl(cap2ChainId: string, bundlerName: string) {\n    return `${BUNDLER_URL}?projectId=${this.client.core.projectId}&chainId=${cap2ChainId}&bundler=${bundlerName}`;\n  }\n}\n\nexport default Eip155Provider;\n", "import HttpConnection from \"@walletconnect/jsonrpc-http-connection\";\nimport { JsonRpcProvider } from \"@walletconnect/jsonrpc-provider\";\nimport Client from \"@walletconnect/sign-client\";\nimport { EngineTypes, SessionTypes } from \"@walletconnect/types\";\nimport EventEmitter from \"events\";\nimport { PROVIDER_EVENTS } from \"../constants\";\nimport {\n  IProvider,\n  RequestParams,\n  RpcProvidersMap,\n  SessionNamespace,\n  SubProviderOpts,\n} from \"../types\";\nimport { getChainId, getGlobal, getRpcUrl } from \"../utils\";\n\nclass SolanaProvider implements IProvider {\n  public name = \"solana\";\n  public client: Client;\n  public httpProviders: RpcProvidersMap;\n  public events: EventEmitter;\n  public namespace: SessionNamespace;\n  public chainId: string;\n\n  constructor(opts: SubProviderOpts) {\n    this.namespace = opts.namespace;\n    this.events = getGlobal(\"events\");\n    this.client = getGlobal(\"client\");\n    this.chainId = this.getDefaultChain();\n    this.httpProviders = this.createHttpProviders();\n  }\n\n  public updateNamespace(namespace: SessionTypes.Namespace) {\n    this.namespace = Object.assign(this.namespace, namespace);\n  }\n\n  public requestAccounts(): string[] {\n    return this.getAccounts();\n  }\n\n  public request<T = unknown>(args: RequestParams): Promise<T> {\n    if (this.namespace.methods.includes(args.request.method)) {\n      return this.client.request(args as EngineTypes.RequestParams);\n    }\n    return this.getHttpProvider().request(args.request);\n  }\n\n  public setDefaultChain(chainId: string, rpcUrl?: string | undefined) {\n    // http provider exists so just set the chainId\n    if (!this.httpProviders[chainId]) {\n      this.setHttpProvider(chainId, rpcUrl);\n    }\n    this.chainId = chainId;\n    this.events.emit(PROVIDER_EVENTS.DEFAULT_CHAIN_CHANGED, `${this.name}:${chainId}`);\n  }\n\n  public getDefaultChain(): string {\n    if (this.chainId) return this.chainId;\n    if (this.namespace.defaultChain) return this.namespace.defaultChain;\n\n    const chainId = this.namespace.chains[0];\n    if (!chainId) throw new Error(`ChainId not found`);\n\n    return chainId.split(\":\")[1];\n  }\n\n  // --------- PRIVATE --------- //\n\n  private getAccounts(): string[] {\n    const accounts = this.namespace.accounts;\n    if (!accounts) {\n      return [];\n    }\n\n    return [\n      ...new Set(\n        accounts\n          // get the accounts from the active chain\n          .filter((account) => account.split(\":\")[1] === this.chainId.toString())\n          // remove namespace & chainId from the string\n          .map((account) => account.split(\":\")[2]),\n      ),\n    ];\n  }\n\n  private createHttpProviders(): RpcProvidersMap {\n    const http = {};\n    this.namespace.chains.forEach((chain) => {\n      const parsedChainId = getChainId(chain);\n      http[parsedChainId] = this.createHttpProvider(parsedChainId, this.namespace.rpcMap?.[chain]);\n    });\n    return http;\n  }\n\n  private getHttpProvider(): JsonRpcProvider {\n    const chain = `${this.name}:${this.chainId}`;\n    const http = this.httpProviders[chain];\n    if (typeof http === \"undefined\") {\n      throw new Error(`JSON-RPC provider for ${chain} not found`);\n    }\n    return http;\n  }\n\n  private setHttpProvider(chainId: string, rpcUrl?: string): void {\n    const http = this.createHttpProvider(chainId, rpcUrl);\n    if (http) {\n      this.httpProviders[chainId] = http;\n    }\n  }\n\n  private createHttpProvider(\n    chainId: string,\n    rpcUrl?: string | undefined,\n  ): JsonRpcProvider | undefined {\n    const rpc = rpcUrl || getRpcUrl(chainId, this.namespace, this.client.core.projectId);\n    if (!rpc) {\n      throw new Error(`No RPC url provided for chainId: ${chainId}`);\n    }\n    const http = new JsonRpcProvider(new HttpConnection(rpc, getGlobal(\"disableProviderPing\")));\n    return http;\n  }\n}\n\nexport default SolanaProvider;\n", "import HttpConnection from \"@walletconnect/jsonrpc-http-connection\";\nimport { JsonRpcProvider } from \"@walletconnect/jsonrpc-provider\";\nimport Client from \"@walletconnect/sign-client\";\nimport { EngineTypes, SessionTypes } from \"@walletconnect/types\";\nimport EventEmitter from \"events\";\nimport { PROVIDER_EVENTS } from \"../constants\";\nimport {\n  IProvider,\n  RequestParams,\n  RpcProvidersMap,\n  SessionNamespace,\n  SubProviderOpts,\n} from \"../types\";\nimport { getChainId, getGlobal, getRpcUrl } from \"../utils\";\n\nclass CosmosProvider implements IProvider {\n  public name = \"cosmos\";\n  public client: Client;\n  public httpProviders: RpcProvidersMap;\n  public events: EventEmitter;\n  public namespace: SessionNamespace;\n  public chainId: string;\n\n  constructor(opts: SubProviderOpts) {\n    this.namespace = opts.namespace;\n    this.events = getGlobal(\"events\");\n    this.client = getGlobal(\"client\");\n    this.chainId = this.getDefaultChain();\n    this.httpProviders = this.createHttpProviders();\n  }\n\n  public updateNamespace(namespace: SessionTypes.Namespace) {\n    this.namespace = Object.assign(this.namespace, namespace);\n  }\n\n  public requestAccounts(): string[] {\n    return this.getAccounts();\n  }\n\n  public getDefaultChain(): string {\n    if (this.chainId) return this.chainId;\n    if (this.namespace.defaultChain) return this.namespace.defaultChain;\n\n    const chainId = this.namespace.chains[0];\n\n    if (!chainId) throw new Error(`ChainId not found`);\n\n    return chainId.split(\":\")[1];\n  }\n\n  public request<T = unknown>(args: RequestParams): Promise<T> {\n    if (this.namespace.methods.includes(args.request.method)) {\n      return this.client.request(args as EngineTypes.RequestParams);\n    }\n    return this.getHttpProvider().request(args.request);\n  }\n\n  public setDefaultChain(chainId: string, rpcUrl?: string | undefined) {\n    // http provider exists so just set the chainId\n    if (!this.httpProviders[chainId]) {\n      this.setHttpProvider(chainId, rpcUrl);\n    }\n    this.chainId = chainId;\n    this.events.emit(PROVIDER_EVENTS.DEFAULT_CHAIN_CHANGED, `${this.name}:${this.chainId}`);\n  }\n\n  // ---------------- PRIVATE ---------------- //\n\n  private getAccounts(): string[] {\n    const accounts = this.namespace.accounts;\n    if (!accounts) {\n      return [];\n    }\n\n    return [\n      ...new Set(\n        accounts\n          // get the accounts from the active chain\n          .filter((account) => account.split(\":\")[1] === this.chainId.toString())\n          // remove namespace & chainId from the string\n          .map((account) => account.split(\":\")[2]),\n      ),\n    ];\n  }\n\n  private createHttpProviders(): RpcProvidersMap {\n    const http = {};\n    this.namespace.chains.forEach((chain) => {\n      const parsedChainId = getChainId(chain);\n      http[parsedChainId] = this.createHttpProvider(parsedChainId, this.namespace.rpcMap?.[chain]);\n    });\n    return http;\n  }\n\n  private getHttpProvider(): JsonRpcProvider {\n    const chain = `${this.name}:${this.chainId}`;\n    const http = this.httpProviders[chain];\n    if (typeof http === \"undefined\") {\n      throw new Error(`JSON-RPC provider for ${chain} not found`);\n    }\n    return http;\n  }\n\n  private setHttpProvider(chainId: string, rpcUrl?: string): void {\n    const http = this.createHttpProvider(chainId, rpcUrl);\n    if (http) {\n      this.httpProviders[chainId] = http;\n    }\n  }\n\n  private createHttpProvider(\n    chainId: string,\n    rpcUrl?: string | undefined,\n  ): JsonRpcProvider | undefined {\n    const rpc = rpcUrl || getRpcUrl(chainId, this.namespace, this.client.core.projectId);\n    if (!rpc) {\n      throw new Error(`No RPC url provided for chainId: ${chainId}`);\n    }\n    const http = new JsonRpcProvider(new HttpConnection(rpc, getGlobal(\"disableProviderPing\")));\n    return http;\n  }\n}\n\nexport default CosmosProvider;\n", "import HttpConnection from \"@walletconnect/jsonrpc-http-connection\";\nimport { JsonRpcProvider } from \"@walletconnect/jsonrpc-provider\";\nimport Client from \"@walletconnect/sign-client\";\nimport { EngineTypes, SessionTypes } from \"@walletconnect/types\";\nimport EventEmitter from \"events\";\nimport { PROVIDER_EVENTS } from \"../constants\";\nimport {\n  IProvider,\n  RequestParams,\n  RpcProvidersMap,\n  SessionNamespace,\n  SubProviderOpts,\n} from \"../types\";\nimport { getGlobal, getRpcUrl } from \"../utils\";\n\nclass AlgorandProvider implements IProvider {\n  public name = \"algorand\";\n  public client: Client;\n  public httpProviders: RpcProvidersMap;\n  public events: EventEmitter;\n  public namespace: SessionNamespace;\n  public chainId: string;\n\n  constructor(opts: SubProviderOpts) {\n    this.namespace = opts.namespace;\n    this.events = getGlobal(\"events\");\n    this.client = getGlobal(\"client\");\n    this.chainId = this.getDefaultChain();\n    this.httpProviders = this.createHttpProviders();\n  }\n\n  public updateNamespace(namespace: SessionTypes.Namespace) {\n    this.namespace = Object.assign(this.namespace, namespace);\n  }\n\n  public requestAccounts(): string[] {\n    return this.getAccounts();\n  }\n\n  public request<T = unknown>(args: RequestParams): Promise<T> {\n    if (this.namespace.methods.includes(args.request.method)) {\n      return this.client.request(args as EngineTypes.RequestParams);\n    }\n    return this.getHttpProvider().request(args.request);\n  }\n\n  public setDefaultChain(chainId: string, rpcUrl?: string | undefined) {\n    // http provider exists so just set the chainId\n    if (!this.httpProviders[chainId]) {\n      const rpc =\n        rpcUrl || getRpcUrl(`${this.name}:${chainId}`, this.namespace, this.client.core.projectId);\n      if (!rpc) {\n        throw new Error(`No RPC url provided for chainId: ${chainId}`);\n      }\n      this.setHttpProvider(chainId, rpc);\n    }\n    this.chainId = chainId;\n    this.events.emit(PROVIDER_EVENTS.DEFAULT_CHAIN_CHANGED, `${this.name}:${this.chainId}`);\n  }\n\n  public getDefaultChain(): string {\n    if (this.chainId) return this.chainId;\n    if (this.namespace.defaultChain) return this.namespace.defaultChain;\n\n    const chainId = this.namespace.chains[0];\n    if (!chainId) throw new Error(`ChainId not found`);\n\n    return chainId.split(\":\")[1];\n  }\n\n  // --------- PRIVATE --------- //\n\n  private getAccounts(): string[] {\n    const accounts = this.namespace.accounts;\n    if (!accounts) {\n      return [];\n    }\n\n    return [\n      ...new Set(\n        accounts\n          // get the accounts from the active chain\n          .filter((account) => account.split(\":\")[1] === this.chainId.toString())\n          // remove namespace & chainId from the string\n          .map((account) => account.split(\":\")[2]),\n      ),\n    ];\n  }\n\n  private createHttpProviders(): RpcProvidersMap {\n    const http = {};\n    this.namespace.chains.forEach((chain) => {\n      http[chain] = this.createHttpProvider(chain, this.namespace.rpcMap?.[chain]);\n    });\n    return http;\n  }\n\n  private getHttpProvider(): JsonRpcProvider {\n    const chain = `${this.name}:${this.chainId}`;\n    const http = this.httpProviders[chain];\n    if (typeof http === \"undefined\") {\n      throw new Error(`JSON-RPC provider for ${chain} not found`);\n    }\n    return http;\n  }\n\n  private setHttpProvider(chainId: string, rpcUrl?: string): void {\n    const http = this.createHttpProvider(chainId, rpcUrl);\n    if (http) {\n      this.httpProviders[chainId] = http;\n    }\n  }\n\n  private createHttpProvider(\n    chainId: string,\n    rpcUrl?: string | undefined,\n  ): JsonRpcProvider | undefined {\n    const rpc = rpcUrl || getRpcUrl(chainId, this.namespace, this.client.core.projectId);\n    if (typeof rpc === \"undefined\") return undefined;\n    const http = new JsonRpcProvider(new HttpConnection(rpc, getGlobal(\"disableProviderPing\")));\n    return http;\n  }\n}\n\nexport default AlgorandProvider;\n", "import HttpConnection from \"@walletconnect/jsonrpc-http-connection\";\nimport { JsonRpcProvider } from \"@walletconnect/jsonrpc-provider\";\nimport Client from \"@walletconnect/sign-client\";\nimport { EngineTypes, SessionTypes } from \"@walletconnect/types\";\nimport EventEmitter from \"events\";\nimport { PROVIDER_EVENTS } from \"../constants\";\nimport {\n  IProvider,\n  RequestParams,\n  RpcProvidersMap,\n  SessionNamespace,\n  SubProviderOpts,\n} from \"../types\";\nimport { getChainId, getGlobal } from \"../utils\";\n\nclass CardanoProvider implements IProvider {\n  public name = \"cip34\";\n  public client: Client;\n  public httpProviders: RpcProvidersMap;\n  public events: EventEmitter;\n  public namespace: SessionNamespace;\n  public chainId: string;\n\n  constructor(opts: SubProviderOpts) {\n    this.namespace = opts.namespace;\n    this.events = getGlobal(\"events\");\n    this.client = getGlobal(\"client\");\n    this.chainId = this.getDefaultChain();\n    this.httpProviders = this.createHttpProviders();\n  }\n\n  public updateNamespace(namespace: SessionTypes.Namespace) {\n    this.namespace = Object.assign(this.namespace, namespace);\n  }\n\n  public requestAccounts(): string[] {\n    return this.getAccounts();\n  }\n\n  public getDefaultChain(): string {\n    if (this.chainId) return this.chainId;\n    if (this.namespace.defaultChain) return this.namespace.defaultChain;\n\n    const chainId = this.namespace.chains[0];\n    if (!chainId) throw new Error(`ChainId not found`);\n\n    return chainId.split(\":\")[1];\n  }\n\n  public request<T = unknown>(args: RequestParams): Promise<T> {\n    if (this.namespace.methods.includes(args.request.method)) {\n      return this.client.request(args as EngineTypes.RequestParams);\n    }\n    return this.getHttpProvider().request(args.request);\n  }\n\n  public setDefaultChain(chainId: string, rpcUrl?: string | undefined) {\n    // http provider exists so just set the chainId\n    if (!this.httpProviders[chainId]) {\n      this.setHttpProvider(chainId, rpcUrl);\n    }\n    this.chainId = chainId;\n    this.events.emit(PROVIDER_EVENTS.DEFAULT_CHAIN_CHANGED, `${this.name}:${this.chainId}`);\n  }\n\n  // ------------- PRIVATE -------------- /\n\n  private getAccounts(): string[] {\n    const accounts = this.namespace.accounts;\n    if (!accounts) {\n      return [];\n    }\n\n    return [\n      ...new Set(\n        accounts\n          // get the accounts from the active chain\n          .filter((account) => account.split(\":\")[1] === this.chainId.toString())\n          // remove namespace & chainId from the string\n          .map((account) => account.split(\":\")[2]),\n      ),\n    ];\n  }\n\n  private createHttpProviders(): RpcProvidersMap {\n    const http = {};\n    this.namespace.chains.forEach((chain) => {\n      const rpcURL = this.getCardanoRPCUrl(chain);\n      const parsedChain = getChainId(chain);\n      http[parsedChain] = this.createHttpProvider(parsedChain, rpcURL);\n    });\n    return http;\n  }\n\n  private getHttpProvider(): JsonRpcProvider {\n    const chain = `${this.name}:${this.chainId}`;\n    const http = this.httpProviders[chain];\n    if (typeof http === \"undefined\") {\n      throw new Error(`JSON-RPC provider for ${chain} not found`);\n    }\n    return http;\n  }\n\n  private getCardanoRPCUrl(chainId: string): string | undefined {\n    const rpcMap = this.namespace.rpcMap;\n    if (!rpcMap) return undefined;\n    return rpcMap[chainId];\n  }\n\n  private setHttpProvider(chainId: string, rpcUrl?: string): void {\n    const http = this.createHttpProvider(chainId, rpcUrl);\n    if (http) {\n      this.httpProviders[chainId] = http;\n    }\n  }\n\n  private createHttpProvider(\n    chainId: string,\n    rpcUrl?: string | undefined,\n  ): JsonRpcProvider | undefined {\n    const rpc = rpcUrl || this.getCardanoRPCUrl(chainId);\n    if (!rpc) {\n      throw new Error(`No RPC url provided for chainId: ${chainId}`);\n    }\n    const http = new JsonRpcProvider(new HttpConnection(rpc, getGlobal(\"disableProviderPing\")));\n    return http;\n  }\n}\n\nexport default CardanoProvider;\n", "import HttpConnection from \"@walletconnect/jsonrpc-http-connection\";\nimport { JsonRpcProvider } from \"@walletconnect/jsonrpc-provider\";\nimport Client from \"@walletconnect/sign-client\";\nimport { EngineTypes, SessionTypes } from \"@walletconnect/types\";\nimport EventEmitter from \"events\";\nimport { PROVIDER_EVENTS } from \"../constants\";\nimport {\n  IProvider,\n  RequestParams,\n  RpcProvidersMap,\n  SessionNamespace,\n  SubProviderOpts,\n} from \"../types\";\nimport { getChainId, getGlobal, getRpcUrl } from \"../utils\";\n\n// Old wallet connect provider for Elrond\nclass ElrondProvider implements IProvider {\n  public name = \"elrond\";\n  public client: Client;\n  public httpProviders: RpcProvidersMap;\n  public events: EventEmitter;\n  public namespace: SessionNamespace;\n  public chainId: string;\n\n  constructor(opts: SubProviderOpts) {\n    this.namespace = opts.namespace;\n    this.events = getGlobal(\"events\");\n    this.client = getGlobal(\"client\");\n    this.chainId = this.getDefaultChain();\n    this.httpProviders = this.createHttpProviders();\n  }\n\n  public updateNamespace(namespace: SessionTypes.Namespace) {\n    this.namespace = Object.assign(this.namespace, namespace);\n  }\n\n  public requestAccounts(): string[] {\n    return this.getAccounts();\n  }\n\n  public request<T = unknown>(args: RequestParams): Promise<T> {\n    if (this.namespace.methods.includes(args.request.method)) {\n      return this.client.request(args as EngineTypes.RequestParams);\n    }\n    return this.getHttpProvider().request(args.request);\n  }\n\n  public setDefaultChain(chainId: string, rpcUrl?: string | undefined) {\n    // http provider exists so just set the chainId\n    if (!this.httpProviders[chainId]) {\n      this.setHttpProvider(chainId, rpcUrl);\n    }\n    this.chainId = chainId;\n    this.events.emit(PROVIDER_EVENTS.DEFAULT_CHAIN_CHANGED, `${this.name}:${chainId}`);\n  }\n\n  public getDefaultChain(): string {\n    if (this.chainId) return this.chainId;\n    if (this.namespace.defaultChain) return this.namespace.defaultChain;\n\n    const chainId = this.namespace.chains[0];\n    if (!chainId) throw new Error(`ChainId not found`);\n\n    return chainId.split(\":\")[1];\n  }\n\n  // --------- PRIVATE --------- //\n\n  private getAccounts(): string[] {\n    const accounts = this.namespace.accounts;\n    if (!accounts) {\n      return [];\n    }\n\n    return [\n      ...new Set(\n        accounts\n          // get the accounts from the active chain\n          .filter((account) => account.split(\":\")[1] === this.chainId.toString())\n          // remove namespace & chainId from the string\n          .map((account) => account.split(\":\")[2]),\n      ),\n    ];\n  }\n\n  private createHttpProviders(): RpcProvidersMap {\n    const http = {};\n    this.namespace.chains.forEach((chain) => {\n      const parsedChainId = getChainId(chain);\n      http[parsedChainId] = this.createHttpProvider(parsedChainId, this.namespace.rpcMap?.[chain]);\n    });\n    return http;\n  }\n\n  private getHttpProvider(): JsonRpcProvider {\n    const chain = `${this.name}:${this.chainId}`;\n    const http = this.httpProviders[chain];\n    if (typeof http === \"undefined\") {\n      throw new Error(`JSON-RPC provider for ${chain} not found`);\n    }\n    return http;\n  }\n\n  private setHttpProvider(chainId: string, rpcUrl?: string): void {\n    const http = this.createHttpProvider(chainId, rpcUrl);\n    if (http) {\n      this.httpProviders[chainId] = http;\n    }\n  }\n\n  private createHttpProvider(\n    chainId: string,\n    rpcUrl?: string | undefined,\n  ): JsonRpcProvider | undefined {\n    const rpc = rpcUrl || getRpcUrl(chainId, this.namespace, this.client.core.projectId);\n    if (!rpc) {\n      throw new Error(`No RPC url provided for chainId: ${chainId}`);\n    }\n    const http = new JsonRpcProvider(new HttpConnection(rpc, getGlobal(\"disableProviderPing\")));\n    return http;\n  }\n}\n\nexport default ElrondProvider;\n", "import HttpConnection from \"@walletconnect/jsonrpc-http-connection\";\nimport { JsonRpcProvider } from \"@walletconnect/jsonrpc-provider\";\nimport Client from \"@walletconnect/sign-client\";\nimport { EngineTypes, SessionTypes } from \"@walletconnect/types\";\nimport EventEmitter from \"events\";\nimport { PROVIDER_EVENTS } from \"../constants\";\nimport {\n  IProvider,\n  RequestParams,\n  RpcProvidersMap,\n  SessionNamespace,\n  SubProviderOpts,\n} from \"../types\";\nimport { getChainId, getGlobal, getRpcUrl } from \"../utils\";\n\nclass MultiversXProvider implements IProvider {\n  public name = \"multiversx\";\n  public client: Client;\n  public httpProviders: RpcProvidersMap;\n  public events: EventEmitter;\n  public namespace: SessionNamespace;\n  public chainId: string;\n\n  constructor(opts: SubProviderOpts) {\n    this.namespace = opts.namespace;\n    this.events = getGlobal(\"events\");\n    this.client = getGlobal(\"client\");\n    this.chainId = this.getDefaultChain();\n    this.httpProviders = this.createHttpProviders();\n  }\n\n  public updateNamespace(namespace: SessionTypes.Namespace) {\n    this.namespace = Object.assign(this.namespace, namespace);\n  }\n\n  public requestAccounts(): string[] {\n    return this.getAccounts();\n  }\n\n  public request<T = unknown>(args: RequestParams): Promise<T> {\n    if (this.namespace.methods.includes(args.request.method)) {\n      return this.client.request(args as EngineTypes.RequestParams);\n    }\n    return this.getHttpProvider().request(args.request);\n  }\n\n  public setDefaultChain(chainId: string, rpcUrl?: string | undefined) {\n    // http provider exists so just set the chainId\n    if (!this.httpProviders[chainId]) {\n      this.setHttpProvider(chainId, rpcUrl);\n    }\n    this.chainId = chainId;\n    this.events.emit(PROVIDER_EVENTS.DEFAULT_CHAIN_CHANGED, `${this.name}:${chainId}`);\n  }\n\n  public getDefaultChain(): string {\n    if (this.chainId) return this.chainId;\n    if (this.namespace.defaultChain) return this.namespace.defaultChain;\n\n    const chainId = this.namespace.chains[0];\n    if (!chainId) throw new Error(`ChainId not found`);\n\n    return chainId.split(\":\")[1];\n  }\n\n  // --------- PRIVATE --------- //\n\n  private getAccounts(): string[] {\n    const accounts = this.namespace.accounts;\n    if (!accounts) {\n      return [];\n    }\n\n    return [\n      ...new Set(\n        accounts\n          // get the accounts from the active chain\n          .filter((account) => account.split(\":\")[1] === this.chainId.toString())\n          // remove namespace & chainId from the string\n          .map((account) => account.split(\":\")[2]),\n      ),\n    ];\n  }\n\n  private createHttpProviders(): RpcProvidersMap {\n    const http = {};\n    this.namespace.chains.forEach((chain) => {\n      const parsedChainId = getChainId(chain);\n      http[parsedChainId] = this.createHttpProvider(parsedChainId, this.namespace.rpcMap?.[chain]);\n    });\n    return http;\n  }\n\n  private getHttpProvider(): JsonRpcProvider {\n    const chain = `${this.name}:${this.chainId}`;\n    const http = this.httpProviders[chain];\n    if (typeof http === \"undefined\") {\n      throw new Error(`JSON-RPC provider for ${chain} not found`);\n    }\n    return http;\n  }\n\n  private setHttpProvider(chainId: string, rpcUrl?: string): void {\n    const http = this.createHttpProvider(chainId, rpcUrl);\n    if (http) {\n      this.httpProviders[chainId] = http;\n    }\n  }\n\n  private createHttpProvider(\n    chainId: string,\n    rpcUrl?: string | undefined,\n  ): JsonRpcProvider | undefined {\n    const rpc = rpcUrl || getRpcUrl(chainId, this.namespace, this.client.core.projectId);\n    if (!rpc) {\n      throw new Error(`No RPC url provided for chainId: ${chainId}`);\n    }\n    const http = new JsonRpcProvider(new HttpConnection(rpc, getGlobal(\"disableProviderPing\")));\n    return http;\n  }\n}\n\nexport default MultiversXProvider;\n", "import HttpConnection from \"@walletconnect/jsonrpc-http-connection\";\nimport { JsonRpcProvider } from \"@walletconnect/jsonrpc-provider\";\nimport Client from \"@walletconnect/sign-client\";\nimport { EngineTypes, SessionTypes } from \"@walletconnect/types\";\nimport EventEmitter from \"events\";\nimport { PROVIDER_EVENTS } from \"../constants\";\nimport {\n  IProvider,\n  RequestParams,\n  RpcProvidersMap,\n  SessionNamespace,\n  SubProviderOpts,\n} from \"../types\";\nimport { getGlobal, getRpcUrl } from \"../utils\";\n\nclass NearProvider implements IProvider {\n  public name = \"near\";\n  public client: Client;\n  public httpProviders: RpcProvidersMap;\n  public events: EventEmitter;\n  public namespace: SessionNamespace;\n  public chainId: string;\n\n  constructor(opts: SubProviderOpts) {\n    this.namespace = opts.namespace;\n    this.events = getGlobal(\"events\");\n    this.client = getGlobal(\"client\");\n    this.chainId = this.getDefaultChain();\n    this.httpProviders = this.createHttpProviders();\n  }\n\n  public updateNamespace(namespace: SessionTypes.Namespace) {\n    this.namespace = Object.assign(this.namespace, namespace);\n  }\n\n  public requestAccounts(): string[] {\n    return this.getAccounts();\n  }\n\n  public getDefaultChain(): string {\n    if (this.chainId) return this.chainId;\n    if (this.namespace.defaultChain) return this.namespace.defaultChain;\n\n    const chainId = this.namespace.chains[0];\n\n    if (!chainId) throw new Error(`ChainId not found`);\n\n    return chainId.split(\":\")[1];\n  }\n\n  public request<T = unknown>(args: RequestParams): Promise<T> {\n    if (this.namespace.methods.includes(args.request.method)) {\n      return this.client.request(args as EngineTypes.RequestParams);\n    }\n    return this.getHttpProvider().request(args.request);\n  }\n\n  public setDefaultChain(chainId: string, rpcUrl?: string | undefined) {\n    this.chainId = chainId;\n    // http provider exists so just set the chainId\n    if (!this.httpProviders[chainId]) {\n      const rpc = rpcUrl || getRpcUrl(`${this.name}:${chainId}`, this.namespace);\n      if (!rpc) {\n        throw new Error(`No RPC url provided for chainId: ${chainId}`);\n      }\n      this.setHttpProvider(chainId, rpc);\n    }\n\n    this.events.emit(PROVIDER_EVENTS.DEFAULT_CHAIN_CHANGED, `${this.name}:${this.chainId}`);\n  }\n\n  // ---------------- PRIVATE ---------------- //\n\n  private getAccounts(): string[] {\n    const accounts = this.namespace.accounts;\n    if (!accounts) {\n      return [];\n    }\n\n    return (\n      accounts\n        // get the accounts from the active chain\n        .filter((account) => account.split(\":\")[1] === this.chainId.toString())\n        // remove namespace & chainId from the string\n        .map((account) => account.split(\":\")[2]) || []\n    );\n  }\n\n  private createHttpProviders(): RpcProvidersMap {\n    const http = {};\n    this.namespace.chains.forEach((chain) => {\n      http[chain] = this.createHttpProvider(chain, this.namespace.rpcMap?.[chain]);\n    });\n    return http;\n  }\n\n  private getHttpProvider(): JsonRpcProvider {\n    const chain = `${this.name}:${this.chainId}`;\n    const http = this.httpProviders[chain];\n    if (typeof http === \"undefined\") {\n      throw new Error(`JSON-RPC provider for ${chain} not found`);\n    }\n    return http;\n  }\n\n  private setHttpProvider(chainId: string, rpcUrl?: string): void {\n    const http = this.createHttpProvider(chainId, rpcUrl);\n    if (http) {\n      this.httpProviders[chainId] = http;\n    }\n  }\n\n  private createHttpProvider(\n    chainId: string,\n    rpcUrl?: string | undefined,\n  ): JsonRpcProvider | undefined {\n    const rpc = rpcUrl || getRpcUrl(chainId, this.namespace);\n    if (typeof rpc === \"undefined\") return undefined;\n    const http = new JsonRpcProvider(new HttpConnection(rpc, getGlobal(\"disableProviderPing\")));\n    return http;\n  }\n}\n\nexport default NearProvider;\n", "import HttpConnection from \"@walletconnect/jsonrpc-http-connection\";\nimport { JsonRpcProvider } from \"@walletconnect/jsonrpc-provider\";\nimport Client from \"@walletconnect/sign-client\";\nimport { EngineTypes, SessionTypes } from \"@walletconnect/types\";\nimport EventEmitter from \"events\";\nimport { PROVIDER_EVENTS } from \"../constants\";\nimport {\n  IProvider,\n  RequestParams,\n  RpcProvidersMap,\n  SessionNamespace,\n  SubProviderOpts,\n} from \"../types\";\n\nimport { getRpcUrl, getGlobal } from \"../utils\";\n\nclass TezosProvider implements IProvider {\n  public name = \"tezos\";\n  public client: Client;\n  public httpProviders: RpcProvidersMap;\n  public events: EventEmitter;\n  public namespace: SessionNamespace;\n  public chainId: string;\n\n  constructor(opts: SubProviderOpts) {\n    this.namespace = opts.namespace;\n    this.events = getGlobal(\"events\");\n    this.client = getGlobal(\"client\");\n    this.chainId = this.getDefaultChain();\n    this.httpProviders = this.createHttpProviders();\n  }\n\n  public updateNamespace(namespace: SessionTypes.Namespace) {\n    this.namespace = Object.assign(this.namespace, namespace);\n  }\n\n  public requestAccounts(): string[] {\n    return this.getAccounts();\n  }\n\n  public getDefaultChain(): string {\n    if (this.chainId) return this.chainId;\n    if (this.namespace.defaultChain) return this.namespace.defaultChain;\n\n    const chainId = this.namespace.chains[0];\n\n    if (!chainId) throw new Error(`ChainId not found`);\n\n    return chainId.split(\":\")[1];\n  }\n\n  public request<T = unknown>(args: RequestParams): Promise<T> {\n    if (this.namespace.methods.includes(args.request.method)) {\n      return this.client.request(args as EngineTypes.RequestParams);\n    }\n    return this.getHttpProvider().request(args.request);\n  }\n\n  public setDefaultChain(chainId: string, rpcUrl?: string | undefined) {\n    this.chainId = chainId;\n    // http provider exists so just set the chainId\n    if (!this.httpProviders[chainId]) {\n      const rpc = rpcUrl || getRpcUrl(`${this.name}:${chainId}`, this.namespace);\n      if (!rpc) {\n        throw new Error(`No RPC url provided for chainId: ${chainId}`);\n      }\n      this.setHttpProvider(chainId, rpc);\n    }\n\n    this.events.emit(PROVIDER_EVENTS.DEFAULT_CHAIN_CHANGED, `${this.name}:${this.chainId}`);\n  }\n\n  // ---------------- PRIVATE ---------------- //\n\n  private getAccounts(): string[] {\n    const accounts = this.namespace.accounts;\n    if (!accounts) {\n      return [];\n    }\n\n    return (\n      accounts\n        // get the accounts from the active chain\n        .filter((account) => account.split(\":\")[1] === this.chainId.toString())\n        // remove namespace & chainId from the string\n        .map((account) => account.split(\":\")[2]) || []\n    );\n  }\n\n  private createHttpProviders(): RpcProvidersMap {\n    const http: any = {};\n    this.namespace.chains.forEach((chain) => {\n      http[chain] = this.createHttpProvider(chain);\n    });\n    return http;\n  }\n\n  private getHttpProvider(): JsonRpcProvider {\n    const chain = `${this.name}:${this.chainId}`;\n    const http = this.httpProviders[chain];\n    if (typeof http === \"undefined\") {\n      throw new Error(`JSON-RPC provider for ${chain} not found`);\n    }\n    return http;\n  }\n\n  private setHttpProvider(chainId: string, rpcUrl?: string): void {\n    const http = this.createHttpProvider(chainId, rpcUrl);\n    if (http) {\n      this.httpProviders[chainId] = http;\n    }\n  }\n\n  private createHttpProvider(\n    chainId: string,\n    rpcUrl?: string | undefined,\n  ): JsonRpcProvider | undefined {\n    const rpc = rpcUrl || getRpcUrl(chainId, this.namespace);\n    if (typeof rpc === \"undefined\") return undefined;\n    const http = new JsonRpcProvider(new HttpConnection(rpc));\n    return http;\n  }\n}\n\nexport default TezosProvider;\n", "import HttpConnection from \"@walletconnect/jsonrpc-http-connection\";\nimport { JsonRpcProvider } from \"@walletconnect/jsonrpc-provider\";\nimport Client from \"@walletconnect/sign-client\";\nimport { EngineTypes, SessionTypes } from \"@walletconnect/types\";\nimport EventEmitter from \"events\";\nimport { GENERIC_SUBPROVIDER_NAME, PROVIDER_EVENTS } from \"../constants\";\nimport {\n  IProvider,\n  RequestParams,\n  RpcProvidersMap,\n  SessionNamespace,\n  SubProviderOpts,\n} from \"../types\";\nimport { getGlobal, getRpcUrl } from \"../utils\";\nimport { parseChainId } from \"@walletconnect/utils\";\n\nclass GenericProvider implements IProvider {\n  public name = GENERIC_SUBPROVIDER_NAME;\n  public client: Client;\n  public httpProviders: RpcProvidersMap;\n  public events: EventEmitter;\n  public namespace: SessionNamespace;\n  public chainId: string;\n\n  constructor(opts: SubProviderOpts) {\n    this.namespace = opts.namespace;\n    this.events = getGlobal(\"events\");\n    this.client = getGlobal(\"client\");\n    this.chainId = this.getDefaultChain();\n    this.httpProviders = this.createHttpProviders();\n  }\n\n  public updateNamespace(namespace: SessionTypes.Namespace) {\n    this.namespace.chains = [\n      ...new Set((this.namespace.chains || []).concat(namespace.chains || [])),\n    ];\n    this.namespace.accounts = [\n      ...new Set((this.namespace.accounts || []).concat(namespace.accounts || [])),\n    ];\n    this.namespace.methods = [\n      ...new Set((this.namespace.methods || []).concat(namespace.methods || [])),\n    ];\n    this.namespace.events = [\n      ...new Set((this.namespace.events || []).concat(namespace.events || [])),\n    ];\n    this.httpProviders = this.createHttpProviders();\n  }\n\n  public requestAccounts(): string[] {\n    return this.getAccounts();\n  }\n\n  public request<T = unknown>(args: RequestParams): Promise<T> {\n    if (this.namespace.methods.includes(args.request.method)) {\n      return this.client.request(args as EngineTypes.RequestParams);\n    }\n    return this.getHttpProvider(args.chainId).request(args.request);\n  }\n\n  public setDefaultChain(chainId: string, rpcUrl?: string | undefined) {\n    // http provider exists so just set the chainId\n    if (!this.httpProviders[chainId]) {\n      this.setHttpProvider(chainId, rpcUrl);\n    }\n    this.chainId = chainId;\n    this.events.emit(PROVIDER_EVENTS.DEFAULT_CHAIN_CHANGED, `${this.name}:${chainId}`);\n  }\n\n  public getDefaultChain(): string {\n    if (this.chainId) return this.chainId;\n    if (this.namespace.defaultChain) return this.namespace.defaultChain;\n\n    const chainId = this.namespace.chains[0];\n    if (!chainId) throw new Error(`ChainId not found`);\n\n    return chainId.split(\":\")[1];\n  }\n\n  // --------- PRIVATE --------- //\n\n  private getAccounts(): string[] {\n    const accounts = this.namespace.accounts;\n    if (!accounts) {\n      return [];\n    }\n\n    return [\n      ...new Set(\n        accounts\n          // get the accounts from the active chain\n          .filter((account) => account.split(\":\")[1] === this.chainId.toString())\n          // remove namespace & chainId from the string\n          .map((account) => account.split(\":\")[2]),\n      ),\n    ];\n  }\n\n  private createHttpProviders(): RpcProvidersMap {\n    const http = {};\n    this.namespace?.accounts?.forEach((account) => {\n      const chain = parseChainId(account);\n      http[`${chain.namespace}:${chain.reference}`] = this.createHttpProvider(account);\n    });\n    return http;\n  }\n\n  private getHttpProvider(chain: string): JsonRpcProvider {\n    const http = this.httpProviders[chain];\n    if (typeof http === \"undefined\") {\n      throw new Error(`JSON-RPC provider for ${chain} not found`);\n    }\n    return http;\n  }\n\n  private setHttpProvider(chainId: string, rpcUrl?: string): void {\n    const http = this.createHttpProvider(chainId, rpcUrl);\n    if (http) {\n      this.httpProviders[chainId] = http;\n    }\n  }\n\n  private createHttpProvider(chainId: string, rpcUrl?: string): JsonRpcProvider | undefined {\n    const rpc = rpcUrl || getRpcUrl(chainId, this.namespace, this.client.core.projectId);\n    if (!rpc) {\n      throw new Error(`No RPC url provided for chainId: ${chainId}`);\n    }\n    const http = new JsonRpcProvider(new HttpConnection(rpc, getGlobal(\"disableProviderPing\")));\n    return http;\n  }\n}\n\nexport default GenericProvider;\n", "import SignClient from \"@walletconnect/sign-client\";\nimport { SessionTypes } from \"@walletconnect/types\";\nimport { JsonRpcResult } from \"@walletconnect/jsonrpc-types\";\nimport { getSdkError, isValidArray, parseNamespaceKey } from \"@walletconnect/utils\";\nimport { getDefaultLoggerOptions, Logger, pino } from \"@walletconnect/logger\";\nimport {\n  convertChainIdToNumber,\n  getAccountsFromSession,\n  getChainsFromApprovedSession,\n  mergeRequiredOptionalNamespaces,\n  parseCaip10Account,\n  populateNamespacesChains,\n  setGlobal,\n} from \"./utils\";\nimport PolkadotProvider from \"./providers/polkadot\";\nimport Eip155Provider from \"./providers/eip155\";\nimport SolanaProvider from \"./providers/solana\";\nimport CosmosProvider from \"./providers/cosmos\";\nimport AlgorandProvider from \"./providers/algorand\";\nimport CardanoProvider from \"./providers/cardano\";\nimport ElrondProvider from \"./providers/elrond\";\nimport MultiversXProvider from \"./providers/multiversx\";\nimport NearProvider from \"./providers/near\";\nimport TezosProvider from \"./providers/tezos\";\nimport GenericProvider from \"./providers/generic\";\n\nimport {\n  IUniversalProvider,\n  IProvider,\n  RpcProviderMap,\n  ConnectParams,\n  RequestArguments,\n  UniversalProviderOpts,\n  NamespaceConfig,\n  PairingsCleanupOpts,\n  ProviderAccounts,\n  AuthenticateParams,\n} from \"./types\";\n\nimport { RELAY_URL, LOGGER, STORAGE, PROVIDER_EVENTS, GENERIC_SUBPROVIDER_NAME } from \"./constants\";\nimport EventEmitter from \"events\";\nimport { formatJsonRpcResult } from \"@walletconnect/jsonrpc-utils\";\n\nexport class UniversalProvider implements IUniversalProvider {\n  public client!: SignClient;\n  public namespaces?: NamespaceConfig;\n  public optionalNamespaces?: NamespaceConfig;\n  public sessionProperties?: SessionTypes.SessionProperties;\n  public scopedProperties?: SessionTypes.ScopedProperties;\n  public events: EventEmitter = new EventEmitter();\n  public rpcProviders: RpcProviderMap = {};\n  public session?: SessionTypes.Struct;\n  public providerOpts: UniversalProviderOpts;\n  public logger: Logger;\n  public uri: string | undefined;\n\n  private disableProviderPing = false;\n\n  static async init(opts: UniversalProviderOpts) {\n    const provider = new UniversalProvider(opts);\n    await provider.initialize();\n    return provider;\n  }\n\n  constructor(opts: UniversalProviderOpts) {\n    this.providerOpts = opts;\n    this.logger =\n      typeof opts?.logger !== \"undefined\" && typeof opts?.logger !== \"string\"\n        ? opts.logger\n        : pino(getDefaultLoggerOptions({ level: opts?.logger || LOGGER }));\n    this.disableProviderPing = opts?.disableProviderPing || false;\n  }\n\n  public async request<T = unknown>(\n    args: RequestArguments,\n    chain?: string | undefined,\n    expiry?: number | undefined,\n  ): Promise<T> {\n    const [namespace, chainId] = this.validateChain(chain);\n\n    if (!this.session) {\n      throw new Error(\"Please call connect() before request()\");\n    }\n\n    return await this.getProvider(namespace).request({\n      request: {\n        ...args,\n      },\n      chainId: `${namespace}:${chainId}`,\n      topic: this.session.topic,\n      expiry,\n    });\n  }\n\n  public sendAsync(\n    args: RequestArguments,\n    callback: (error: Error | null, response: JsonRpcResult) => void,\n    chain?: string | undefined,\n    expiry?: number | undefined,\n  ): void {\n    const id = new Date().getTime();\n    this.request(args, chain, expiry)\n      .then((response) => callback(null, formatJsonRpcResult(id, response)))\n      .catch((error) => callback(error, undefined as any));\n  }\n\n  public async enable(): Promise<ProviderAccounts> {\n    if (!this.client) {\n      throw new Error(\"Sign Client not initialized\");\n    }\n    if (!this.session) {\n      await this.connect({\n        namespaces: this.namespaces,\n        optionalNamespaces: this.optionalNamespaces,\n        sessionProperties: this.sessionProperties,\n        scopedProperties: this.scopedProperties,\n      });\n    }\n    const accounts = await this.requestAccounts();\n    return accounts as ProviderAccounts;\n  }\n\n  public async disconnect(): Promise<void> {\n    if (!this.session) {\n      throw new Error(\"Please call connect() before enable()\");\n    }\n    await this.client.disconnect({\n      topic: this.session?.topic,\n      reason: getSdkError(\"USER_DISCONNECTED\"),\n    });\n    await this.cleanup();\n  }\n\n  public async connect(opts: ConnectParams): Promise<SessionTypes.Struct | undefined> {\n    if (!this.client) {\n      throw new Error(\"Sign Client not initialized\");\n    }\n    this.setNamespaces(opts);\n    await this.cleanupPendingPairings();\n    if (opts.skipPairing) return;\n\n    return await this.pair(opts.pairingTopic);\n  }\n\n  public async authenticate(opts: AuthenticateParams, walletUniversalLink?: string) {\n    if (!this.client) {\n      throw new Error(\"Sign Client not initialized\");\n    }\n    this.setNamespaces(opts);\n    await this.cleanupPendingPairings();\n\n    const { uri, response } = await this.client.authenticate(opts, walletUniversalLink);\n    if (uri) {\n      this.uri = uri;\n      this.events.emit(\"display_uri\", uri);\n    }\n    const result = await response();\n    this.session = result.session;\n    if (this.session) {\n      // assign namespaces from session if not already defined\n      const approved = populateNamespacesChains(this.session.namespaces) as NamespaceConfig;\n      this.namespaces = mergeRequiredOptionalNamespaces(this.namespaces, approved);\n      await this.persist(\"namespaces\", this.namespaces);\n      this.onConnect();\n    }\n    return result;\n  }\n\n  public on(event: any, listener: any): void {\n    this.events.on(event, listener);\n  }\n\n  public once(event: string, listener: any): void {\n    this.events.once(event, listener);\n  }\n\n  public removeListener(event: string, listener: any): void {\n    this.events.removeListener(event, listener);\n  }\n\n  public off(event: string, listener: any): void {\n    this.events.off(event, listener);\n  }\n\n  get isWalletConnect() {\n    return true;\n  }\n\n  public async pair(pairingTopic: string | undefined): Promise<SessionTypes.Struct> {\n    const { uri, approval } = await this.client.connect({\n      pairingTopic,\n      requiredNamespaces: this.namespaces,\n      optionalNamespaces: this.optionalNamespaces,\n      sessionProperties: this.sessionProperties,\n      scopedProperties: this.scopedProperties,\n    });\n\n    if (uri) {\n      this.uri = uri;\n      this.events.emit(\"display_uri\", uri);\n    }\n\n    const session = await approval();\n    this.session = session;\n    // assign namespaces from session if not already defined\n    const approved = populateNamespacesChains(session.namespaces) as NamespaceConfig;\n    this.namespaces = mergeRequiredOptionalNamespaces(this.namespaces, approved);\n    await this.persist(\"namespaces\", this.namespaces);\n    await this.persist(\"optionalNamespaces\", this.optionalNamespaces);\n\n    this.onConnect();\n    return this.session;\n  }\n\n  public setDefaultChain(chain: string, rpcUrl?: string | undefined) {\n    try {\n      // ignore without active session\n      if (!this.session) return;\n      const [namespace, chainId] = this.validateChain(chain);\n      const provider = this.getProvider(namespace);\n      // @ts-expect-error\n      if (provider.name === GENERIC_SUBPROVIDER_NAME) {\n        provider.setDefaultChain(`${namespace}:${chainId}`, rpcUrl);\n      } else {\n        provider.setDefaultChain(chainId, rpcUrl);\n      }\n    } catch (error) {\n      // ignore the error if the fx is used prematurely before namespaces are set\n      if (!/Please call connect/.test((error as Error).message)) throw error;\n    }\n  }\n\n  public async cleanupPendingPairings(opts: PairingsCleanupOpts = {}): Promise<void> {\n    this.logger.info(\"Cleaning up inactive pairings...\");\n    const inactivePairings = this.client.pairing.getAll();\n\n    if (!isValidArray(inactivePairings)) return;\n\n    for (const pairing of inactivePairings) {\n      if (opts.deletePairings) {\n        this.client.core.expirer.set(pairing.topic, 0);\n      } else {\n        await this.client.core.relayer.subscriber.unsubscribe(pairing.topic);\n      }\n    }\n\n    this.logger.info(`Inactive pairings cleared: ${inactivePairings.length}`);\n  }\n\n  public abortPairingAttempt() {\n    this.logger.warn(\"abortPairingAttempt is deprecated. This is now a no-op.\");\n  }\n\n  // ---------- Private ----------------------------------------------- //\n\n  private async checkStorage() {\n    this.namespaces = (await this.getFromStore(`namespaces`)) || {};\n    this.optionalNamespaces = (await this.getFromStore(`optionalNamespaces`)) || {};\n    if (this.session) this.createProviders();\n  }\n\n  private async initialize() {\n    this.logger.trace(`Initialized`);\n    await this.createClient();\n    await this.checkStorage();\n    this.registerEventListeners();\n  }\n\n  private async createClient() {\n    this.client =\n      this.providerOpts.client ||\n      (await SignClient.init({\n        core: this.providerOpts.core,\n        logger: this.providerOpts.logger || LOGGER,\n        relayUrl: this.providerOpts.relayUrl || RELAY_URL,\n        projectId: this.providerOpts.projectId,\n        metadata: this.providerOpts.metadata,\n        storageOptions: this.providerOpts.storageOptions,\n        storage: this.providerOpts.storage,\n        name: this.providerOpts.name,\n        customStoragePrefix: this.providerOpts.customStoragePrefix,\n        telemetryEnabled: this.providerOpts.telemetryEnabled,\n      }));\n\n    if (this.providerOpts.session) {\n      try {\n        this.session = this.client.session.get(this.providerOpts.session.topic);\n      } catch (error) {\n        this.logger.error(\"Failed to get session\", error);\n        throw new Error(\n          `The provided session: ${this.providerOpts?.session?.topic} doesn't exist in the Sign client`,\n        );\n      }\n    } else {\n      const sessions = this.client.session.getAll();\n      this.session = sessions[0];\n    }\n    this.logger.trace(`SignClient Initialized`);\n  }\n\n  private createProviders(): void {\n    if (!this.client) {\n      throw new Error(\"Sign Client not initialized\");\n    }\n\n    if (!this.session) {\n      throw new Error(\"Session not initialized. Please call connect() before enable()\");\n    }\n\n    const providersToCreate = [\n      ...new Set(\n        Object.keys(this.session.namespaces).map((namespace) => parseNamespaceKey(namespace)),\n      ),\n    ];\n\n    setGlobal(\"client\", this.client);\n    setGlobal(\"events\", this.events);\n    setGlobal(\"disableProviderPing\", this.disableProviderPing);\n\n    providersToCreate.forEach((namespace) => {\n      if (!this.session) return;\n      const accounts = getAccountsFromSession(namespace, this.session);\n      const approvedChains = getChainsFromApprovedSession(accounts);\n      const mergedNamespaces = mergeRequiredOptionalNamespaces(\n        this.namespaces,\n        this.optionalNamespaces,\n      );\n      const combinedNamespace = {\n        ...mergedNamespaces[namespace],\n        accounts,\n        chains: approvedChains,\n      };\n      switch (namespace) {\n        case \"eip155\":\n          this.rpcProviders[namespace] = new Eip155Provider({\n            namespace: combinedNamespace,\n          });\n          break;\n        case \"algorand\":\n          this.rpcProviders[namespace] = new AlgorandProvider({\n            namespace: combinedNamespace,\n          });\n          break;\n        case \"solana\":\n          this.rpcProviders[namespace] = new SolanaProvider({\n            namespace: combinedNamespace,\n          });\n          break;\n        case \"cosmos\":\n          this.rpcProviders[namespace] = new CosmosProvider({\n            namespace: combinedNamespace,\n          });\n          break;\n        case \"polkadot\":\n          this.rpcProviders[namespace] = new PolkadotProvider({\n            namespace: combinedNamespace,\n          });\n          break;\n        case \"cip34\":\n          this.rpcProviders[namespace] = new CardanoProvider({\n            namespace: combinedNamespace,\n          });\n          break;\n        case \"elrond\":\n          this.rpcProviders[namespace] = new ElrondProvider({\n            namespace: combinedNamespace,\n          });\n          break;\n        case \"multiversx\":\n          this.rpcProviders[namespace] = new MultiversXProvider({\n            namespace: combinedNamespace,\n          });\n          break;\n        case \"near\":\n          this.rpcProviders[namespace] = new NearProvider({\n            namespace: combinedNamespace,\n          });\n          break;\n        case \"tezos\":\n          this.rpcProviders[namespace] = new TezosProvider({\n            namespace: combinedNamespace,\n          });\n          break;\n        default:\n          if (!this.rpcProviders[GENERIC_SUBPROVIDER_NAME]) {\n            this.rpcProviders[GENERIC_SUBPROVIDER_NAME] = new GenericProvider({\n              namespace: combinedNamespace,\n            });\n          } else {\n            this.rpcProviders[GENERIC_SUBPROVIDER_NAME].updateNamespace(combinedNamespace);\n          }\n      }\n    });\n  }\n\n  private registerEventListeners(): void {\n    if (typeof this.client === \"undefined\") {\n      throw new Error(\"Sign Client is not initialized\");\n    }\n\n    this.client.on(\"session_ping\", (args) => {\n      const { topic } = args;\n      if (topic !== this.session?.topic) return;\n      this.events.emit(\"session_ping\", args);\n    });\n\n    this.client.on(\"session_event\", (args) => {\n      const { params, topic } = args;\n      if (topic !== this.session?.topic) return;\n      const { event } = params;\n      if (event.name === \"accountsChanged\") {\n        const accounts = event.data;\n        if (accounts && isValidArray(accounts))\n          this.events.emit(\"accountsChanged\", accounts.map(parseCaip10Account));\n      } else if (event.name === \"chainChanged\") {\n        const requestChainId = params.chainId;\n        const payloadChainId = params.event.data as number;\n        const namespace = parseNamespaceKey(requestChainId);\n        // chainIds might differ between the request & payload - request is always in CAIP2 format, while payload might be string, number, CAIP2 or hex\n        // take priority of the payload chainId\n        const chainIdToProcess =\n          convertChainIdToNumber(requestChainId) !== convertChainIdToNumber(payloadChainId)\n            ? `${namespace}:${convertChainIdToNumber(payloadChainId)}`\n            : requestChainId;\n\n        this.onChainChanged(chainIdToProcess);\n      } else {\n        this.events.emit(event.name, event.data);\n      }\n\n      this.events.emit(\"session_event\", args);\n    });\n\n    this.client.on(\"session_update\", ({ topic, params }) => {\n      if (topic !== this.session?.topic) return;\n      const { namespaces } = params;\n      const _session = this.client?.session.get(topic);\n      this.session = { ..._session, namespaces } as SessionTypes.Struct;\n      this.onSessionUpdate();\n      this.events.emit(\"session_update\", { topic, params });\n    });\n\n    this.client.on(\"session_delete\", async (payload) => {\n      if (payload.topic !== this.session?.topic) return;\n      await this.cleanup();\n      this.events.emit(\"session_delete\", payload);\n      this.events.emit(\"disconnect\", {\n        ...getSdkError(\"USER_DISCONNECTED\"),\n        data: payload.topic,\n      });\n    });\n\n    this.on(PROVIDER_EVENTS.DEFAULT_CHAIN_CHANGED, (caip2ChainId: string) => {\n      this.onChainChanged(caip2ChainId, true);\n    });\n  }\n\n  private getProvider(namespace: string): IProvider {\n    return this.rpcProviders[namespace] || this.rpcProviders[GENERIC_SUBPROVIDER_NAME];\n  }\n\n  private onSessionUpdate(): void {\n    Object.keys(this.rpcProviders).forEach((namespace: string) => {\n      this.getProvider(namespace).updateNamespace(\n        this.session?.namespaces[namespace] as SessionTypes.BaseNamespace,\n      );\n    });\n  }\n\n  private setNamespaces(params: ConnectParams): void {\n    const {\n      namespaces = {},\n      optionalNamespaces = {},\n      sessionProperties,\n      scopedProperties,\n    } = params;\n\n    // requiredNamespaces are deprecated, assign them to optionalNamespaces\n    this.optionalNamespaces = mergeRequiredOptionalNamespaces(namespaces, optionalNamespaces);\n    this.sessionProperties = sessionProperties;\n    this.scopedProperties = scopedProperties;\n  }\n\n  private validateChain(chain?: string): [string, string] {\n    const [namespace, chainId] = chain?.split(\":\") || [\"\", \"\"];\n    if (!this.namespaces || !Object.keys(this.namespaces).length) return [namespace, chainId];\n    // validate namespace\n    if (namespace) {\n      if (\n        // some namespaces might be defined with inline chainId e.g. eip155:1\n        // and we need to parse them\n        !Object.keys(this.namespaces || {})\n          .map((key) => parseNamespaceKey(key))\n          .includes(namespace)\n      ) {\n        throw new Error(\n          `Namespace '${namespace}' is not configured. Please call connect() first with namespace config.`,\n        );\n      }\n    }\n    if (namespace && chainId) {\n      return [namespace, chainId];\n    }\n    const defaultNamespace = parseNamespaceKey(Object.keys(this.namespaces)[0]);\n    const defaultChain = this.rpcProviders[defaultNamespace].getDefaultChain();\n    return [defaultNamespace, defaultChain];\n  }\n\n  private async requestAccounts(): Promise<string[]> {\n    const [namespace] = this.validateChain();\n    return await this.getProvider(namespace).requestAccounts();\n  }\n\n  private async onChainChanged(caip2Chain: string, internal = false): Promise<void> {\n    if (!this.namespaces) return;\n\n    const [namespace, chainId] = this.validateChain(caip2Chain);\n\n    if (!chainId) return;\n\n    this.updateNamespaceChain(namespace, chainId);\n\n    this.events.emit(\"chainChanged\", chainId);\n\n    const previousChainId = this.getProvider(namespace).getDefaultChain();\n    if (!internal) {\n      this.getProvider(namespace).setDefaultChain(chainId);\n    }\n\n    this.emitAccountsChangedOnChainChange({ namespace, previousChainId, newChainId: caip2Chain });\n    await this.persist(\"namespaces\", this.namespaces);\n  }\n\n  /**\n   * Emits `accountsChanged` event when a chain is changed and there are new accounts on the new chain\n   */\n  private emitAccountsChangedOnChainChange({\n    namespace,\n    previousChainId,\n    newChainId,\n  }: {\n    namespace: string;\n    previousChainId: string;\n    newChainId: string;\n  }): void {\n    try {\n      if (previousChainId === newChainId) {\n        return;\n      }\n\n      const accounts = this.session?.namespaces[namespace]?.accounts;\n      if (!accounts) return;\n      const newChainIdAccounts = accounts\n        .filter((account) => account.includes(`${newChainId}:`))\n        .map(parseCaip10Account);\n      if (!isValidArray(newChainIdAccounts)) return;\n      this.events.emit(\"accountsChanged\", newChainIdAccounts);\n    } catch (error) {\n      this.logger.warn(\"Failed to emit accountsChanged on chain change\", error);\n    }\n  }\n\n  private updateNamespaceChain(namespace: string, chainId: string): void {\n    if (!this.namespaces) return;\n\n    const namespaceKey = this.namespaces[namespace] ? namespace : `${namespace}:${chainId}`;\n\n    const defaultNamespace = {\n      chains: [],\n      methods: [],\n      events: [],\n      defaultChain: chainId,\n    };\n\n    if (!this.namespaces[namespaceKey]) {\n      this.namespaces[namespaceKey] = defaultNamespace;\n    } else if (this.namespaces[namespaceKey]) {\n      this.namespaces[namespaceKey].defaultChain = chainId;\n    }\n  }\n\n  private onConnect() {\n    this.createProviders();\n    this.events.emit(\"connect\", { session: this.session });\n  }\n\n  private async cleanup() {\n    this.namespaces = undefined;\n    this.optionalNamespaces = undefined;\n    this.sessionProperties = undefined;\n    await this.deleteFromStore(\"namespaces\");\n    await this.deleteFromStore(\"optionalNamespaces\");\n    await this.deleteFromStore(\"sessionProperties\");\n    // reset the session after removing from store as the topic is used there\n    this.session = undefined;\n    await this.cleanupPendingPairings({ deletePairings: true });\n    await this.cleanupStorage();\n  }\n\n  private async persist(key: string, data: unknown) {\n    const topic = this.session?.topic || \"\";\n    await this.client.core.storage.setItem(`${STORAGE}/${key}${topic}`, data);\n  }\n\n  private async getFromStore(key: string) {\n    const topic = this.session?.topic || \"\";\n    return await this.client.core.storage.getItem(`${STORAGE}/${key}${topic}`);\n  }\n\n  private async deleteFromStore(key: string) {\n    const topic = this.session?.topic || \"\";\n    await this.client.core.storage.removeItem(`${STORAGE}/${key}${topic}`);\n  }\n\n  // remove all storage items if there are no sessions left\n  private async cleanupStorage() {\n    try {\n      if (this.client?.session.length > 0) {\n        return;\n      }\n      const keys = await this.client.core.storage.getKeys();\n      for (const key of keys) {\n        if (key.startsWith(STORAGE)) {\n          await this.client.core.storage.removeItem(key);\n        }\n      }\n    } catch (error) {\n      this.logger.warn(\"Failed to cleanup storage\", error);\n    }\n  }\n}\nexport default UniversalProvider;\n", "import { UniversalProvider as Provider } from \"./UniversalProvider\";\nexport * from \"./types\";\nexport const UniversalProvider = Provider;\nexport default Provider;\n"], "names": ["isTypedArray", "cloneDeepWith", "chainId", "rpc", "projectId", "_a", "chain", "parseChainId", "RPC_URL", "accounts", "address", "namespace", "session", "matchedNamespaceKeys", "key", "accountsForNamespace", "required", "optional", "requiredNamespaces", "optionalNamespaces", "merge", "namespaces", "_b", "_c", "_d", "_e", "normalizedNamespaces", "isValidObject", "values", "chains", "isCaipNamespace", "methods", "events", "rpcMap", "normalizedKey", "parseNamespaceKey", "__spreadProps", "__spreadValues", "mergeArrays", "caip10Account", "parsedNamespaces", "globals", "value", "p", "a", "r", "<PERSON><PERSON><PERSON><PERSON>", "opts", "__publicField", "getGlobal", "args", "rpcUrl", "PROVIDER_EVENTS", "account", "http", "parsed<PERSON><PERSON><PERSON><PERSON>d", "get<PERSON>hainId", "getRpcUrl", "JsonRpcProvider", "HttpConnection", "Eip155Provider", "parsed<PERSON><PERSON><PERSON>", "hexChainId", "chainIds", "capabilitiesKey", "sessionCapabilities", "capabilities", "error", "bundlerName", "bundlerUrl", "customUrl", "url", "response", "formatJsonRpcRequest", "cap2ChainId", "BUNDLER_URL", "Solana<PERSON><PERSON><PERSON>", "CosmosProvider", "AlgorandProvider", "CardanoProvider", "rpcURL", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MultiversXProvider", "NearProvider", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "GenericProvider", "GENERIC_SUBPROVIDER_NAME", "UniversalProvider", "EventEmitter", "pino", "getDefaultLoggerOptions", "LOGGER", "provider", "expiry", "callback", "id", "formatJsonRpcResult", "getSdkError", "walletUniversalLink", "uri", "result", "approved", "populateNamespacesChains", "mergeRequiredOptionalNamespaces", "event", "listener", "pairingTopic", "approval", "inactivePairings", "isValidArray", "pairing", "SignClient", "RELAY_URL", "sessions", "providersToCreate", "setGlobal", "getAccountsFromSession", "<PERSON><PERSON><PERSON><PERSON>", "getChainsFromApprovedSession", "mergedNamespaces", "combinedNamespace", "topic", "params", "parseCaip10Account", "requestChainId", "payloadChainId", "chainIdToProcess", "convertChainIdToNumber", "_session", "payload", "caip2ChainId", "sessionProperties", "scopedProperties", "defaultNamespace", "defaultChain", "caip2Chain", "internal", "previousChainId", "newChainId", "newChainIdAccounts", "namespaceKey", "data", "STORAGE", "keys", "Provider"], "mappings": "", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32], "debugId": null}}]}