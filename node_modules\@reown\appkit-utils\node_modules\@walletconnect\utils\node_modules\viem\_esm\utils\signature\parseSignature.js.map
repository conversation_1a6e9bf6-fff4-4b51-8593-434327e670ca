{"version": 3, "file": "parseSignature.js", "sourceRoot": "", "sources": ["../../../utils/signature/parseSignature.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,yBAAyB,CAAA;AAInD,OAAO,EAEL,WAAW,GACZ,MAAM,+BAA+B,CAAA;AAItC;;;;;;;;;GASG;AACH,MAAM,UAAU,cAAc,CAAC,YAAiB;IAC9C,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,SAAS,CAAC,SAAS,CAAC,WAAW,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAA;IAC5E,MAAM,UAAU,GAAG,MAAM,CAAC,KAAK,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;IACzD,MAAM,CAAC,CAAC,EAAE,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE;QACzB,IAAI,UAAU,KAAK,CAAC,IAAI,UAAU,KAAK,CAAC;YAAE,OAAO,CAAC,SAAS,EAAE,UAAU,CAAC,CAAA;QACxE,IAAI,UAAU,KAAK,EAAE;YAAE,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAA;QACrD,IAAI,UAAU,KAAK,EAAE;YAAE,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAA;QACrD,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAA;IAC7C,CAAC,CAAC,EAAE,CAAA;IAEJ,IAAI,OAAO,CAAC,KAAK,WAAW;QAC1B,OAAO;YACL,CAAC,EAAE,WAAW,CAAC,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;YAC/B,CAAC,EAAE,WAAW,CAAC,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;YAC/B,CAAC;YACD,OAAO;SACY,CAAA;IACvB,OAAO;QACL,CAAC,EAAE,WAAW,CAAC,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;QAC/B,CAAC,EAAE,WAAW,CAAC,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;QAC/B,OAAO;KACY,CAAA;AACvB,CAAC"}