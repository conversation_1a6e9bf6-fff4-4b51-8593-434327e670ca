# 🛠️ P2P System Development Tasks (Srd.Exchange)

---

## ✅ Task 1: Project Setup
1.1 - ✅ Initialize frontend project (Next.js or Vite)
1.2 - ✅ Setup Supabase project at [https://supabase.com](https://supabase.com)
1.3 - ✅ Install Supabase client SDK
1.4 - ✅ Connect Supabase with frontend (`.env` setup)
1.5 - ❌ Setup Git repo & push initial commit (Skipped)

---

## ✅ Task 2: Supabase Database Schema
2.1 - ✅ Create `users` table: wallet, upi_id, bank_details, created_at
2.2 - ✅ Create `orders` table: type, amount, rate, status, user_wallet, created_at
2.3 - ✅ Create `proofs` table: order_id, buyer_wallet, proof_url, created_at
2.4 - ✅ Enable Supabase Storage for image uploads (bucket: `payment_proofs`)
2.5 - ✅ Setup Row Level Security (RLS) rules

---

##  Task 3: Wallet Integration & Auth
3.1 - Integrate Metamask and Trust Wallet  
3.2 - On connect, check if user exists in `users` table  
3.3 - If not, prompt for UPI/bank details  
3.4 - Save user info to Supabase

---

##  Task 4: Sell USDT Flow
4.1 - Create Sell form with input (min. 11 USDT)  
4.2 - On confirm, transfer USDT to Admin wallet (via smart contract or Web3 call)  
4.3 - Save order to Supabase with `status = pending`  
4.4 - Display sell orders in Admin dashboard

---

##  Task 5: Buy USDT Flow
5.1 - Show live sell orders from Supabase  
5.2 - Show seller’s UPI/bank details  
5.3 - Buyer inputs amount and uploads payment proof  
5.4 - Upload image to Supabase Storage  
5.5 - Save proof info to Supabase with order_id

---

##  Task 6: Admin Verification
6.1 - Build Admin dashboard: show users, orders, proofs  
6.2 - On proof upload, admin verifies payment  
6.3 - Confirm with seller externally  
6.4 - On confirmation, click "Release" (transfers USDT from Admin wallet to buyer)  
6.5 - Update order status to `completed`

---

##  Task 7: Notifications
7.1 - Use Supabase `onSnapshot`/`subscribe` to watch `proofs` table  
7.2 - Trigger alerts to admin on new uploads  
7.3 - (Optional) Notify seller via webhook/email/SMS

---

##  Task 8: UI Implementation
8.1 - Landing Page with "Connect Wallet" button  
8.2 - User dashboard: Buy/Sell toggle  
8.3 - Forms for UPI/Bank info, Sell input, Buy proof upload  
8.4 - Admin dashboard: full control on orders

---

##  Task 9: Final Testing
9.1 - Test full Buy/Sell flow with dummy wallets  
9.2 - Validate data saved properly in Supabase  
9.3 - Test proof upload and admin release  
9.4 - Confirm all edge cases handled (missing UPI, payment delays, etc.)
