{"version": 3, "sources": [], "sections": [{"offset": {"line": 119, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n\n// BSC Mainnet Configuration\nexport const BSC_CONFIG = {\n  chainId: 56,\n  name: 'BSC Mainnet',\n  currency: 'BNB',\n  rpcUrl: 'https://bsc-dataseed1.binance.org/',\n  blockExplorer: 'https://bscscan.com'\n}\n\n// USDT Contract Address on BSC Mainnet\nexport const USDT_CONTRACT_ADDRESS = '******************************************'\n\n// Database types\nexport interface User {\n  wallet: string\n  upi_id?: string\n  bank_details?: string\n  created_at: string\n}\n\nexport interface Order {\n  id: string\n  type: 'buy' | 'sell'\n  amount: number\n  rate: number\n  status: 'pending' | 'completed' | 'cancelled'\n  user_wallet: string\n  created_at: string\n}\n\nexport interface Proof {\n  id: string\n  order_id: string\n  buyer_wallet: string\n  proof_url: string\n  created_at: string\n}\n\n// Database helper functions\nexport const dbHelpers = {\n  // Get user by wallet address\n  async getUser(wallet: string) {\n    const { data, error } = await supabase\n      .from('users')\n      .select('*')\n      .eq('wallet', wallet)\n      .single()\n\n    return { data, error }\n  },\n\n  // Create or update user\n  async upsertUser(user: Omit<User, 'created_at'>) {\n    const { data, error } = await supabase\n      .from('users')\n      .upsert(user)\n      .select()\n      .single()\n\n    return { data, error }\n  },\n\n  // Get all sell orders (for buyers)\n  async getSellOrders() {\n    const { data, error } = await supabase\n      .from('orders')\n      .select(`\n        *,\n        users!orders_user_wallet_fkey(wallet, upi_id, bank_details)\n      `)\n      .eq('type', 'sell')\n      .eq('status', 'pending')\n      .order('created_at', { ascending: false })\n\n    return { data, error }\n  },\n\n  // Create new order\n  async createOrder(order: Omit<Order, 'id' | 'created_at'>) {\n    const { data, error } = await supabase\n      .from('orders')\n      .insert(order)\n      .select()\n      .single()\n\n    return { data, error }\n  },\n\n  // Get all orders (for admin)\n  async getAllOrders() {\n    const { data, error } = await supabase\n      .from('orders')\n      .select(`\n        *,\n        users!orders_user_wallet_fkey(wallet, upi_id, bank_details)\n      `)\n      .order('created_at', { ascending: false })\n\n    return { data, error }\n  },\n\n  // Update order status\n  async updateOrderStatus(orderId: string, status: 'pending' | 'completed' | 'cancelled') {\n    const { data, error } = await supabase\n      .from('orders')\n      .update({ status })\n      .eq('id', orderId)\n      .select()\n      .single()\n\n    return { data, error }\n  },\n\n  // Get all proofs (for admin)\n  async getAllProofs() {\n    const { data, error } = await supabase\n      .from('proofs')\n      .select(`\n        *,\n        orders(\n          *,\n          users(wallet, upi_id, bank_details)\n        )\n      `)\n      .order('created_at', { ascending: false })\n\n    console.log('getAllProofs result:', { data, error })\n    return { data, error }\n  },\n\n  // Get all users (for admin)\n  async getAllUsers() {\n    const { data, error } = await supabase\n      .from('users')\n      .select('*')\n      .order('created_at', { ascending: false })\n\n    return { data, error }\n  },\n\n  // Get seller's orders\n  async getSellerOrders(sellerWallet: string) {\n    const { data, error } = await supabase\n      .from('orders')\n      .select('*')\n      .eq('user_wallet', sellerWallet)\n      .order('created_at', { ascending: false })\n\n    return { data, error }\n  },\n\n  // Get proofs for seller's orders\n  async getProofsForSeller(sellerWallet: string) {\n    console.log('Getting proofs for seller:', sellerWallet)\n\n    // Get proofs with order details in one query\n    const { data, error } = await supabase\n      .from('proofs')\n      .select(`\n        *,\n        orders(\n          id,\n          user_wallet,\n          type,\n          amount,\n          rate,\n          status\n        )\n      `)\n      .eq('orders.user_wallet', sellerWallet)\n      .order('created_at', { ascending: false })\n\n    console.log('Proofs for seller result:', { data, error, sellerWallet })\n\n    if (error) {\n      console.error('Error getting proofs for seller:', error)\n      return { data: null, error }\n    }\n\n    // Filter out proofs where orders is null (shouldn't happen but just in case)\n    const validProofs = data?.filter(proof => proof.orders) || []\n    console.log('Valid proofs found:', validProofs.length)\n\n    return { data: validProofs, error: null }\n  },\n\n  // Confirm payment by seller - REWRITTEN\n  async confirmPaymentBySeller(proofId: string) {\n    try {\n      // Simple direct update without complex checks\n      const { error } = await supabase\n        .from('proofs')\n        .update({ confirmed_by_seller: true })\n        .eq('id', proofId)\n\n      if (error) {\n        console.error('Supabase update error:', error)\n        return { success: false, error: error.message }\n      }\n\n      return { success: true, error: null }\n    } catch (err) {\n      console.error('Confirmation error:', err)\n      return { success: false, error: 'Failed to confirm payment' }\n    }\n  },\n\n  // Debug function to list all proofs\n  async debugListAllProofs() {\n    const { data, error } = await supabase\n      .from('proofs')\n      .select('*')\n      .order('created_at', { ascending: false })\n\n    console.log('All proofs in database:', { data, error })\n    return { data, error }\n  },\n\n  // Upload payment proof\n  async uploadProof(file: File, orderId: string, buyerWallet: string) {\n    const fileExt = file.name.split('.').pop()\n    const fileName = `${orderId}_${Date.now()}.${fileExt}`\n\n    // Upload file to storage\n    const { data: uploadData, error: uploadError } = await supabase.storage\n      .from('payment_proofs')\n      .upload(fileName, file)\n\n    if (uploadError) return { data: null, error: uploadError }\n\n    // Get public URL\n    const { data: { publicUrl } } = supabase.storage\n      .from('payment_proofs')\n      .getPublicUrl(fileName)\n\n    // Save proof record to database\n    const { data, error } = await supabase\n      .from('proofs')\n      .insert({\n        order_id: orderId,\n        buyer_wallet: buyerWallet,\n        proof_url: publicUrl\n      })\n      .select()\n      .single()\n\n    return { data, error }\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;AAEA,MAAM;AACN,MAAM;AAEC,MAAM,WAAW,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AAG3C,MAAM,aAAa;IACxB,SAAS;IACT,MAAM;IACN,UAAU;IACV,QAAQ;IACR,eAAe;AACjB;AAGO,MAAM,wBAAwB;AA6B9B,MAAM,YAAY;IACvB,6BAA6B;IAC7B,MAAM,SAAQ,MAAc;QAC1B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,UAAU,QACb,MAAM;QAET,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,wBAAwB;IACxB,MAAM,YAAW,IAA8B;QAC7C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,SACL,MAAM,CAAC,MACP,MAAM,GACN,MAAM;QAET,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,mCAAmC;IACnC,MAAM;QACJ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,UACL,MAAM,CAAC,CAAC;;;MAGT,CAAC,EACA,EAAE,CAAC,QAAQ,QACX,EAAE,CAAC,UAAU,WACb,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,mBAAmB;IACnB,MAAM,aAAY,KAAuC;QACvD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,UACL,MAAM,CAAC,OACP,MAAM,GACN,MAAM;QAET,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,6BAA6B;IAC7B,MAAM;QACJ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,UACL,MAAM,CAAC,CAAC;;;MAGT,CAAC,EACA,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,sBAAsB;IACtB,MAAM,mBAAkB,OAAe,EAAE,MAA6C;QACpF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,UACL,MAAM,CAAC;YAAE;QAAO,GAChB,EAAE,CAAC,MAAM,SACT,MAAM,GACN,MAAM;QAET,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,6BAA6B;IAC7B,MAAM;QACJ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,UACL,MAAM,CAAC,CAAC;;;;;;MAMT,CAAC,EACA,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,QAAQ,GAAG,CAAC,wBAAwB;YAAE;YAAM;QAAM;QAClD,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,4BAA4B;IAC5B,MAAM;QACJ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,SACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,sBAAsB;IACtB,MAAM,iBAAgB,YAAoB;QACxC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,UACL,MAAM,CAAC,KACP,EAAE,CAAC,eAAe,cAClB,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,iCAAiC;IACjC,MAAM,oBAAmB,YAAoB;QAC3C,QAAQ,GAAG,CAAC,8BAA8B;QAE1C,6CAA6C;QAC7C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,UACL,MAAM,CAAC,CAAC;;;;;;;;;;MAUT,CAAC,EACA,EAAE,CAAC,sBAAsB,cACzB,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,QAAQ,GAAG,CAAC,6BAA6B;YAAE;YAAM;YAAO;QAAa;QAErE,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO;gBAAE,MAAM;gBAAM;YAAM;QAC7B;QAEA,6EAA6E;QAC7E,MAAM,cAAc,MAAM,OAAO,CAAA,QAAS,MAAM,MAAM,KAAK,EAAE;QAC7D,QAAQ,GAAG,CAAC,uBAAuB,YAAY,MAAM;QAErD,OAAO;YAAE,MAAM;YAAa,OAAO;QAAK;IAC1C;IAEA,wCAAwC;IACxC,MAAM,wBAAuB,OAAe;QAC1C,IAAI;YACF,8CAA8C;YAC9C,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,UACL,MAAM,CAAC;gBAAE,qBAAqB;YAAK,GACnC,EAAE,CAAC,MAAM;YAEZ,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,0BAA0B;gBACxC,OAAO;oBAAE,SAAS;oBAAO,OAAO,MAAM,OAAO;gBAAC;YAChD;YAEA,OAAO;gBAAE,SAAS;gBAAM,OAAO;YAAK;QACtC,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,uBAAuB;YACrC,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAA4B;QAC9D;IACF;IAEA,oCAAoC;IACpC,MAAM;QACJ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,UACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,QAAQ,GAAG,CAAC,2BAA2B;YAAE;YAAM;QAAM;QACrD,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,uBAAuB;IACvB,MAAM,aAAY,IAAU,EAAE,OAAe,EAAE,WAAmB;QAChE,MAAM,UAAU,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG;QACxC,MAAM,WAAW,GAAG,QAAQ,CAAC,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,SAAS;QAEtD,yBAAyB;QACzB,MAAM,EAAE,MAAM,UAAU,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAAS,OAAO,CACpE,IAAI,CAAC,kBACL,MAAM,CAAC,UAAU;QAEpB,IAAI,aAAa,OAAO;YAAE,MAAM;YAAM,OAAO;QAAY;QAEzD,iBAAiB;QACjB,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,EAAE,GAAG,SAAS,OAAO,CAC7C,IAAI,CAAC,kBACL,YAAY,CAAC;QAEhB,gCAAgC;QAChC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,UACL,MAAM,CAAC;YACN,UAAU;YACV,cAAc;YACd,WAAW;QACb,GACC,MAAM,GACN,MAAM;QAET,OAAO;YAAE;YAAM;QAAM;IACvB;AACF", "debugId": null}}, {"offset": {"line": 346, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/src/components/UserDetailsForm.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { dbHelpers } from '@/lib/supabase'\n\ninterface UserDetailsFormProps {\n  walletAddress: string\n  onUserCreated: (user: any) => void\n}\n\nexport default function UserDetailsForm({ walletAddress, onUserCreated }: UserDetailsFormProps) {\n  const [formData, setFormData] = useState({\n    upi_id: '',\n    bank_details: ''\n  })\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n\n    if (!formData.upi_id && !formData.bank_details) {\n      setError('Please provide either UPI ID or Bank Details')\n      return\n    }\n\n    setLoading(true)\n    setError('')\n\n    try {\n      console.log('Saving user data:', {\n        wallet: walletAddress,\n        upi_id: formData.upi_id || null,\n        bank_details: formData.bank_details || null\n      })\n\n      const userData = {\n        wallet: walletAddress,\n        upi_id: formData.upi_id || null,\n        bank_details: formData.bank_details || null\n      }\n\n      const { data, error } = await dbHelpers.upsertUser(userData)\n\n      console.log('Supabase response:', { data, error })\n\n      if (error) {\n        console.error('Supabase error details:', error)\n        setError(`Failed to save user details: ${error.message}`)\n        return\n      }\n\n      console.log('User created successfully:', data)\n      onUserCreated(data)\n    } catch (error) {\n      console.error('Unexpected error saving user:', error)\n      setError('An unexpected error occurred. Please try again.')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n    const { name, value } = e.target\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }))\n  }\n\n  return (\n    <div className=\"max-w-md mx-auto\">\n      <div className=\"mb-6 p-4 bg-blue-900/20 border border-blue-500/30 rounded-lg\">\n        <h2 className=\"text-2xl font-bold mb-2 text-center text-blue-400\">Complete Your Profile</h2>\n        <p className=\"text-gray-300 text-center text-sm\">\n          Wallet: {walletAddress?.slice(0, 8)}...{walletAddress?.slice(-6)}\n        </p>\n      </div>\n      <p className=\"text-gray-400 mb-6 text-center\">\n        Please provide your payment details to start trading\n      </p>\n\n      <form onSubmit={handleSubmit} className=\"space-y-6\">\n        <div>\n          <label htmlFor=\"upi_id\" className=\"block text-sm font-medium mb-2\">\n            UPI ID (Optional)\n          </label>\n          <input\n            type=\"text\"\n            id=\"upi_id\"\n            name=\"upi_id\"\n            value={formData.upi_id}\n            onChange={handleInputChange}\n            placeholder=\"your-upi@paytm\"\n            className=\"w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg focus:outline-none focus:border-blue-500\"\n          />\n        </div>\n\n        <div>\n          <label htmlFor=\"bank_details\" className=\"block text-sm font-medium mb-2\">\n            Bank Details (Optional)\n          </label>\n          <textarea\n            id=\"bank_details\"\n            name=\"bank_details\"\n            value={formData.bank_details}\n            onChange={handleInputChange}\n            placeholder=\"Bank Name: XYZ Bank&#10;Account Number: **********&#10;IFSC: ABCD0123456&#10;Account Holder: Your Name\"\n            rows={4}\n            className=\"w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg focus:outline-none focus:border-blue-500 resize-none\"\n          />\n        </div>\n\n        {error && (\n          <div className=\"text-red-500 text-sm text-center\">\n            {error}\n          </div>\n        )}\n\n        <button\n          type=\"submit\"\n          disabled={loading}\n          className=\"btn-primary w-full disabled:opacity-50 disabled:cursor-not-allowed\"\n        >\n          {loading ? 'Saving...' : 'Save Details'}\n        </button>\n      </form>\n\n      <p className=\"text-xs text-gray-500 mt-4 text-center\">\n        You can provide either UPI ID or Bank Details (or both). This information will be used for INR payments.\n      </p>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAUe,SAAS,gBAAgB,EAAE,aAAa,EAAE,aAAa,EAAwB;IAC5F,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,QAAQ;QACR,cAAc;IAChB;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,SAAS,MAAM,IAAI,CAAC,SAAS,YAAY,EAAE;YAC9C,SAAS;YACT;QACF;QAEA,WAAW;QACX,SAAS;QAET,IAAI;YACF,QAAQ,GAAG,CAAC,qBAAqB;gBAC/B,QAAQ;gBACR,QAAQ,SAAS,MAAM,IAAI;gBAC3B,cAAc,SAAS,YAAY,IAAI;YACzC;YAEA,MAAM,WAAW;gBACf,QAAQ;gBACR,QAAQ,SAAS,MAAM,IAAI;gBAC3B,cAAc,SAAS,YAAY,IAAI;YACzC;YAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,YAAS,CAAC,UAAU,CAAC;YAEnD,QAAQ,GAAG,CAAC,sBAAsB;gBAAE;gBAAM;YAAM;YAEhD,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,2BAA2B;gBACzC,SAAS,CAAC,6BAA6B,EAAE,MAAM,OAAO,EAAE;gBACxD;YACF;YAEA,QAAQ,GAAG,CAAC,8BAA8B;YAC1C,cAAc;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;IACH;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAoD;;;;;;kCAClE,8OAAC;wBAAE,WAAU;;4BAAoC;4BACtC,eAAe,MAAM,GAAG;4BAAG;4BAAI,eAAe,MAAM,CAAC;;;;;;;;;;;;;0BAGlE,8OAAC;gBAAE,WAAU;0BAAiC;;;;;;0BAI9C,8OAAC;gBAAK,UAAU;gBAAc,WAAU;;kCACtC,8OAAC;;0CACC,8OAAC;gCAAM,SAAQ;gCAAS,WAAU;0CAAiC;;;;;;0CAGnE,8OAAC;gCACC,MAAK;gCACL,IAAG;gCACH,MAAK;gCACL,OAAO,SAAS,MAAM;gCACtB,UAAU;gCACV,aAAY;gCACZ,WAAU;;;;;;;;;;;;kCAId,8OAAC;;0CACC,8OAAC;gCAAM,SAAQ;gCAAe,WAAU;0CAAiC;;;;;;0CAGzE,8OAAC;gCACC,IAAG;gCACH,MAAK;gCACL,OAAO,SAAS,YAAY;gCAC5B,UAAU;gCACV,aAAY;gCACZ,MAAM;gCACN,WAAU;;;;;;;;;;;;oBAIb,uBACC,8OAAC;wBAAI,WAAU;kCACZ;;;;;;kCAIL,8OAAC;wBACC,MAAK;wBACL,UAAU;wBACV,WAAU;kCAET,UAAU,cAAc;;;;;;;;;;;;0BAI7B,8OAAC;gBAAE,WAAU;0BAAyC;;;;;;;;;;;;AAK5D", "debugId": null}}, {"offset": {"line": 558, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/src/components/WalletConnect.tsx"], "sourcesContent": ["'use client'\n\nimport { useAccount, useConnect, useDisconnect } from 'wagmi'\nimport { useEffect, useState } from 'react'\nimport { dbHelpers } from '@/lib/supabase'\nimport UserDetailsForm from './UserDetailsForm'\n\nexport default function WalletConnect() {\n  const { address, isConnected } = useAccount()\n  const { connect, connectors, error, isPending } = useConnect()\n  const { disconnect } = useDisconnect()\n  const [user, setUser] = useState<any>(null)\n  const [showUserForm, setShowUserForm] = useState(false)\n  const [loading, setLoading] = useState(false)\n  const [showWalletOptions, setShowWalletOptions] = useState(false)\n  const [mounted, setMounted] = useState(false)\n  const [connectionError, setConnectionError] = useState<string | null>(null)\n\n  // Fix hydration mismatch\n  useEffect(() => {\n    setMounted(true)\n    console.log('WalletConnect mounted')\n    console.log('Available connectors:', connectors)\n  }, [])\n\n  // Debug connection state changes\n  useEffect(() => {\n    console.log('Connection state changed:', { isConnected, address })\n  }, [isConnected, address])\n\n  // Debug errors\n  useEffect(() => {\n    if (error) {\n      console.error('Wagmi connection error:', error)\n    }\n  }, [error])\n\n  // Check if user exists when wallet connects\n  useEffect(() => {\n    if (isConnected && address) {\n      checkUserExists()\n    }\n  }, [isConnected, address])\n\n  const checkUserExists = async () => {\n    if (!address) return\n    \n    setLoading(true)\n    try {\n      const { data, error } = await dbHelpers.getUser(address)\n      \n      if (error && error.code !== 'PGRST116') {\n        console.error('Error checking user:', error)\n        return\n      }\n      \n      if (data) {\n        // User exists\n        setUser(data)\n        setShowUserForm(false)\n      } else {\n        // User doesn't exist, show form\n        setShowUserForm(true)\n      }\n    } catch (error) {\n      console.error('Error:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleUserCreated = (userData: any) => {\n    setUser(userData)\n    setShowUserForm(false)\n  }\n\n  const handleDisconnect = () => {\n    disconnect()\n    setUser(null)\n    setShowUserForm(false)\n    setShowWalletOptions(false)\n  }\n\n  const handleConnectClick = () => {\n    setShowWalletOptions(true)\n  }\n\n\n\n  const handleWalletSelect = async (walletType: string) => {\n    try {\n      setConnectionError(null)\n      console.log('Attempting to connect with:', walletType)\n\n      if (walletType === 'MetaMask') {\n        await connectMetaMask()\n      } else if (walletType === 'TrustWallet') {\n        await connectTrustWallet()\n      } else {\n        // Fallback to wagmi connector\n        const connector = connectors.find(c => c.name.toLowerCase().includes(walletType.toLowerCase()))\n        if (connector) {\n          await connect({ connector })\n        }\n      }\n\n      setShowWalletOptions(false)\n    } catch (err) {\n      console.error('Connection error:', err)\n      setConnectionError(`Failed to connect: ${err instanceof Error ? err.message : 'Unknown error'}`)\n    }\n  }\n\n  const connectMetaMask = async () => {\n    if (typeof window !== 'undefined' && window.ethereum) {\n      try {\n        // Request account access\n        const accounts = await window.ethereum.request({\n          method: 'eth_requestAccounts'\n        })\n\n        // Switch to BSC network\n        try {\n          await window.ethereum.request({\n            method: 'wallet_switchEthereumChain',\n            params: [{ chainId: '0x38' }], // BSC Mainnet\n          })\n        } catch (switchError: any) {\n          // If BSC is not added, add it\n          if (switchError.code === 4902) {\n            await window.ethereum.request({\n              method: 'wallet_addEthereumChain',\n              params: [{\n                chainId: '0x38',\n                chainName: 'BSC Mainnet',\n                nativeCurrency: {\n                  name: 'BNB',\n                  symbol: 'BNB',\n                  decimals: 18,\n                },\n                rpcUrls: ['https://bsc-dataseed1.binance.org/'],\n                blockExplorerUrls: ['https://bscscan.com/'],\n              }],\n            })\n          }\n        }\n\n        console.log('MetaMask connected:', accounts[0])\n        // Force wagmi to recognize the connection\n        window.location.reload()\n      } catch (error) {\n        throw new Error('Failed to connect to MetaMask')\n      }\n    } else {\n      throw new Error('MetaMask is not installed. Please install MetaMask extension.')\n    }\n  }\n\n  const connectTrustWallet = async () => {\n    if (typeof window !== 'undefined' && window.ethereum) {\n      // Trust Wallet also uses window.ethereum\n      await connectMetaMask()\n    } else {\n      throw new Error('Trust Wallet is not detected. Please open this page in Trust Wallet browser.')\n    }\n  }\n\n  // Prevent hydration mismatch\n  if (!mounted) {\n    return (\n      <div className=\"flex items-center justify-center\">\n        <div className=\"text-lg\">Loading...</div>\n      </div>\n    )\n  }\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center\">\n        <div className=\"text-lg\">Loading...</div>\n      </div>\n    )\n  }\n\n  if (!isConnected) {\n    return (\n      <div className=\"text-center\">\n        <h2 className=\"text-2xl font-bold mb-6\">Connect Your Wallet</h2>\n        <p className=\"text-gray-400 mb-8\">\n          Connect your wallet to start trading USDT\n        </p>\n\n        {!showWalletOptions ? (\n          <button\n            onClick={handleConnectClick}\n            className=\"btn-primary text-lg px-8 py-4\"\n          >\n            Connect Wallet\n          </button>\n        ) : (\n          <div className=\"space-y-4 max-w-sm mx-auto\">\n            <h3 className=\"text-lg font-semibold mb-4\">Choose Your Wallet</h3>\n\n            {/* MetaMask */}\n            <button\n              onClick={() => handleWalletSelect('MetaMask')}\n              className=\"btn-primary w-full py-3 px-6 flex items-center justify-center gap-3 disabled:opacity-50\"\n              disabled={isPending}\n            >\n              <span>🦊</span>\n              {isPending ? 'Connecting...' : 'Connect MetaMask'}\n            </button>\n\n            {/* Trust Wallet */}\n            <button\n              onClick={() => handleWalletSelect('TrustWallet')}\n              className=\"btn-primary w-full py-3 px-6 flex items-center justify-center gap-3 disabled:opacity-50\"\n              disabled={isPending}\n            >\n              <span>🛡️</span>\n              {isPending ? 'Connecting...' : 'Connect Trust Wallet'}\n            </button>\n\n            {/* Other Injected Wallets */}\n            <button\n              onClick={() => handleWalletSelect('Injected')}\n              className=\"btn-primary w-full py-3 px-6 flex items-center justify-center gap-3 disabled:opacity-50\"\n              disabled={isPending}\n            >\n              <span>💼</span>\n              {isPending ? 'Connecting...' : 'Connect Other Wallet'}\n            </button>\n\n            {/* Error display */}\n            {(error || connectionError) && (\n              <div className=\"text-red-500 text-sm mt-2 p-2 bg-red-900/20 rounded\">\n                {connectionError || error?.message}\n              </div>\n            )}\n\n            <button\n              onClick={() => setShowWalletOptions(false)}\n              className=\"text-gray-400 hover:text-white mt-4 w-full text-center\"\n            >\n              ← Back\n            </button>\n          </div>\n        )}\n      </div>\n    )\n  }\n\n  if (showUserForm) {\n    return (\n      <UserDetailsForm\n        walletAddress={address!}\n        onUserCreated={handleUserCreated}\n      />\n    )\n  }\n\n  return (\n    <div className=\"text-center\">\n      <div className=\"mb-6 p-4 bg-green-900/20 border border-green-500/30 rounded-lg\">\n        <h2 className=\"text-2xl font-bold mb-2 text-green-400\">✅ Wallet Connected</h2>\n        <p className=\"text-gray-300 text-sm font-mono\">\n          {address?.slice(0, 8)}...{address?.slice(-6)}\n        </p>\n        <p className=\"text-xs text-gray-500 mt-1\">BSC Mainnet</p>\n      </div>\n\n      {user && (\n        <div className=\"mb-6 p-4 bg-blue-900/20 border border-blue-500/30 rounded-lg\">\n          <h3 className=\"text-lg font-semibold mb-2 text-blue-400\">Your Payment Details</h3>\n          {user.upi_id && (\n            <p className=\"text-sm text-gray-300\">UPI: {user.upi_id}</p>\n          )}\n          {user.bank_details && (\n            <p className=\"text-sm text-gray-300\">Bank: {user.bank_details}</p>\n          )}\n        </div>\n      )}\n\n      <div className=\"space-y-3\">\n        <button\n          onClick={handleDisconnect}\n          className=\"btn-primary bg-red-600 hover:bg-red-700\"\n        >\n          Disconnect Wallet\n        </button>\n\n        {user && (\n          <p className=\"text-xs text-gray-500\">\n            Ready to trade! Use the Buy/Sell buttons below.\n          </p>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,2JAAA,CAAA,aAAU,AAAD;IAC1C,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,2JAAA,CAAA,aAAU,AAAD;IAC3D,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,gBAAa,AAAD;IACnC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IACtC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEtE,yBAAyB;IACzB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;QACX,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,yBAAyB;IACvC,GAAG,EAAE;IAEL,iCAAiC;IACjC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ,GAAG,CAAC,6BAA6B;YAAE;YAAa;QAAQ;IAClE,GAAG;QAAC;QAAa;KAAQ;IAEzB,eAAe;IACf,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF,GAAG;QAAC;KAAM;IAEV,4CAA4C;IAC5C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,eAAe,SAAS;YAC1B;QACF;IACF,GAAG;QAAC;QAAa;KAAQ;IAEzB,MAAM,kBAAkB;QACtB,IAAI,CAAC,SAAS;QAEd,WAAW;QACX,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,YAAS,CAAC,OAAO,CAAC;YAEhD,IAAI,SAAS,MAAM,IAAI,KAAK,YAAY;gBACtC,QAAQ,KAAK,CAAC,wBAAwB;gBACtC;YACF;YAEA,IAAI,MAAM;gBACR,cAAc;gBACd,QAAQ;gBACR,gBAAgB;YAClB,OAAO;gBACL,gCAAgC;gBAChC,gBAAgB;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,UAAU;QAC1B,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,QAAQ;QACR,gBAAgB;IAClB;IAEA,MAAM,mBAAmB;QACvB;QACA,QAAQ;QACR,gBAAgB;QAChB,qBAAqB;IACvB;IAEA,MAAM,qBAAqB;QACzB,qBAAqB;IACvB;IAIA,MAAM,qBAAqB,OAAO;QAChC,IAAI;YACF,mBAAmB;YACnB,QAAQ,GAAG,CAAC,+BAA+B;YAE3C,IAAI,eAAe,YAAY;gBAC7B,MAAM;YACR,OAAO,IAAI,eAAe,eAAe;gBACvC,MAAM;YACR,OAAO;gBACL,8BAA8B;gBAC9B,MAAM,YAAY,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;gBAC3F,IAAI,WAAW;oBACb,MAAM,QAAQ;wBAAE;oBAAU;gBAC5B;YACF;YAEA,qBAAqB;QACvB,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,qBAAqB;YACnC,mBAAmB,CAAC,mBAAmB,EAAE,eAAe,QAAQ,IAAI,OAAO,GAAG,iBAAiB;QACjG;IACF;IAEA,MAAM,kBAAkB;QACtB,uCAAsD;;QAuCtD,OAAO;YACL,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,MAAM,qBAAqB;QACzB,uCAAsD;;QAGtD,OAAO;YACL,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,6BAA6B;IAC7B,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BAAU;;;;;;;;;;;IAG/B;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BAAU;;;;;;;;;;;IAG/B;IAEA,IAAI,CAAC,aAAa;QAChB,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAA0B;;;;;;8BACxC,8OAAC;oBAAE,WAAU;8BAAqB;;;;;;gBAIjC,CAAC,kCACA,8OAAC;oBACC,SAAS;oBACT,WAAU;8BACX;;;;;yCAID,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA6B;;;;;;sCAG3C,8OAAC;4BACC,SAAS,IAAM,mBAAmB;4BAClC,WAAU;4BACV,UAAU;;8CAEV,8OAAC;8CAAK;;;;;;gCACL,YAAY,kBAAkB;;;;;;;sCAIjC,8OAAC;4BACC,SAAS,IAAM,mBAAmB;4BAClC,WAAU;4BACV,UAAU;;8CAEV,8OAAC;8CAAK;;;;;;gCACL,YAAY,kBAAkB;;;;;;;sCAIjC,8OAAC;4BACC,SAAS,IAAM,mBAAmB;4BAClC,WAAU;4BACV,UAAU;;8CAEV,8OAAC;8CAAK;;;;;;gCACL,YAAY,kBAAkB;;;;;;;wBAIhC,CAAC,SAAS,eAAe,mBACxB,8OAAC;4BAAI,WAAU;sCACZ,mBAAmB,OAAO;;;;;;sCAI/B,8OAAC;4BACC,SAAS,IAAM,qBAAqB;4BACpC,WAAU;sCACX;;;;;;;;;;;;;;;;;;IAOX;IAEA,IAAI,cAAc;QAChB,qBACE,8OAAC,qIAAA,CAAA,UAAe;YACd,eAAe;YACf,eAAe;;;;;;IAGrB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,8OAAC;wBAAE,WAAU;;4BACV,SAAS,MAAM,GAAG;4BAAG;4BAAI,SAAS,MAAM,CAAC;;;;;;;kCAE5C,8OAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;YAG3C,sBACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA2C;;;;;;oBACxD,KAAK,MAAM,kBACV,8OAAC;wBAAE,WAAU;;4BAAwB;4BAAM,KAAK,MAAM;;;;;;;oBAEvD,KAAK,YAAY,kBAChB,8OAAC;wBAAE,WAAU;;4BAAwB;4BAAO,KAAK,YAAY;;;;;;;;;;;;;0BAKnE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;oBAIA,sBACC,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;;AAO/C", "debugId": null}}, {"offset": {"line": 979, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/src/components/AdminVerificationDashboard.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useAccount } from 'wagmi'\nimport { dbHelpers } from '@/lib/supabase'\n\ninterface Order {\n  id: string\n  type: 'buy' | 'sell'\n  amount: number\n  rate: number\n  status: 'pending' | 'completed' | 'cancelled'\n  user_wallet: string\n  created_at: string\n  users: {\n    wallet: string\n    upi_id?: string\n    bank_details?: string\n  }\n}\n\ninterface Proof {\n  id: string\n  order_id: string\n  buyer_wallet: string\n  proof_url: string\n  created_at: string\n  confirmed_by_seller?: boolean\n  orders: Order\n}\n\nexport default function AdminVerificationDashboard() {\n  const { address } = useAccount()\n  const [orders, setOrders] = useState<Order[]>([])\n  const [proofs, setProofs] = useState<Proof[]>([])\n  const [users, setUsers] = useState<any[]>([])\n  const [loading, setLoading] = useState(true)\n  const [error, setError] = useState('')\n  const [activeTab, setActiveTab] = useState<'proofs' | 'orders' | 'users'>('proofs')\n  const [processingRelease, setProcessingRelease] = useState<string | null>(null)\n  const [confirmedProofs, setConfirmedProofs] = useState<Set<string>>(new Set())\n\n  useEffect(() => {\n    loadAllData()\n  }, [])\n\n  const loadAllData = async () => {\n    try {\n      setLoading(true)\n      setError('')\n\n      // Load all data in parallel\n      const [ordersResult, proofsResult, usersResult] = await Promise.all([\n        dbHelpers.getAllOrders(),\n        dbHelpers.getAllProofs(),\n        dbHelpers.getAllUsers()\n      ])\n\n      if (ordersResult.error) {\n        console.error('Orders error:', ordersResult.error)\n        setError('Failed to load orders: ' + ordersResult.error.message)\n        return\n      }\n\n      if (proofsResult.error) {\n        console.error('Proofs error:', proofsResult.error)\n        setError('Failed to load proofs: ' + proofsResult.error.message)\n        return\n      }\n\n      if (usersResult.error) {\n        console.error('Users error:', usersResult.error)\n        setError('Failed to load users: ' + usersResult.error.message)\n        return\n      }\n\n      console.log('Loaded data:', {\n        orders: ordersResult.data?.length || 0,\n        proofs: proofsResult.data?.length || 0,\n        users: usersResult.data?.length || 0\n      })\n\n      setOrders(ordersResult.data || [])\n      setProofs(proofsResult.data || [])\n      setUsers(usersResult.data || [])\n\n    } catch (err) {\n      setError('Failed to load data')\n      console.error('Load data error:', err)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const confirmWithSeller = (proofId: string) => {\n    setConfirmedProofs(prev => new Set([...prev, proofId]))\n  }\n\n  const releaseUSDT = async (proof: Proof) => {\n    if (!address) {\n      setError('Admin wallet not connected')\n      return\n    }\n\n    if (!confirmedProofs.has(proof.id)) {\n      setError('Please confirm with seller first before releasing USDT')\n      return\n    }\n\n    if (!proof.confirmed_by_seller) {\n      setError('Seller has not confirmed payment receipt yet. Please wait for seller confirmation.')\n      return\n    }\n\n    setProcessingRelease(proof.id)\n    setError('')\n\n    try {\n      // Transfer USDT from admin wallet to buyer\n      await transferUSDTToBuyer(proof.buyer_wallet, proof.orders.amount)\n\n      // Update order status to completed\n      const { error } = await dbHelpers.updateOrderStatus(proof.order_id, 'completed')\n\n      if (error) {\n        setError('Failed to update order status: ' + error.message)\n        return\n      }\n\n      // Remove from confirmed proofs\n      setConfirmedProofs(prev => {\n        const newSet = new Set(prev)\n        newSet.delete(proof.id)\n        return newSet\n      })\n\n      // Reload data to reflect changes\n      await loadAllData()\n\n    } catch (err) {\n      console.error('Release USDT error:', err)\n      setError(err instanceof Error ? err.message : 'Failed to release USDT')\n    } finally {\n      setProcessingRelease(null)\n    }\n  }\n\n  const transferUSDTToBuyer = async (buyerWallet: string, amount: number) => {\n    if (typeof window === 'undefined' || !window.ethereum) {\n      throw new Error('MetaMask not found')\n    }\n\n    // USDT contract address on BSC\n    const usdtContract = '******************************************'\n    \n    // Convert amount to wei (USDT has 18 decimals)\n    const amountBigInt = BigInt(Math.floor(amount * 1e18))\n    const amountHex = amountBigInt.toString(16).padStart(64, '0')\n    \n    // Clean buyer wallet address (remove 0x and pad to 64 chars)\n    const buyerAddressHex = buyerWallet.slice(2).toLowerCase().padStart(64, '0')\n    \n    // ERC20 transfer function signature: transfer(address,uint256)\n    const transferData = `0xa9059cbb${buyerAddressHex}${amountHex}`\n\n    const txParams = {\n      from: address,\n      to: usdtContract,\n      data: transferData,\n      gas: '0x15F90', // 90000 gas for ERC20 transfer\n    }\n\n    console.log('Releasing USDT:', {\n      amount,\n      amountWei: amountBigInt.toString(),\n      buyerWallet,\n      transferData\n    })\n\n    const txHash = await window.ethereum.request({\n      method: 'eth_sendTransaction',\n      params: [txParams],\n    })\n\n    console.log('USDT release transaction:', txHash)\n    return txHash\n  }\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleString()\n  }\n\n  const formatWallet = (wallet: string) => {\n    return `${wallet.slice(0, 6)}...${wallet.slice(-4)}`\n  }\n\n  if (loading) {\n    return (\n      <div className=\"text-center py-8\">\n        <div className=\"animate-spin w-8 h-8 border-4 border-blue-400 border-t-transparent rounded-full mx-auto mb-4\"></div>\n        <p>Loading admin data...</p>\n      </div>\n    )\n  }\n\n  const pendingProofs = proofs.filter(proof => proof.orders?.status === 'pending')\n  const pendingOrders = orders.filter(order => order.status === 'pending')\n\n  console.log('Dashboard data:', {\n    totalProofs: proofs.length,\n    pendingProofs: pendingProofs.length,\n    totalOrders: orders.length,\n    pendingOrders: pendingOrders.length,\n    proofsData: proofs.map(p => ({ id: p.id, orderStatus: p.orders?.status }))\n  })\n\n  return (\n    <div className=\"max-w-7xl mx-auto\">\n      {/* Stats Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\">\n        <div className=\"p-6 bg-blue-900/20 border border-blue-500/30 rounded-lg\">\n          <h3 className=\"text-lg font-semibold text-blue-400\">Pending Proofs</h3>\n          <p className=\"text-3xl font-bold\">{pendingProofs.length}</p>\n        </div>\n        <div className=\"p-6 bg-yellow-900/20 border border-yellow-500/30 rounded-lg\">\n          <h3 className=\"text-lg font-semibold text-yellow-400\">Pending Orders</h3>\n          <p className=\"text-3xl font-bold\">{pendingOrders.length}</p>\n        </div>\n        <div className=\"p-6 bg-green-900/20 border border-green-500/30 rounded-lg\">\n          <h3 className=\"text-lg font-semibold text-green-400\">Total Orders</h3>\n          <p className=\"text-3xl font-bold\">{orders.length}</p>\n        </div>\n        <div className=\"p-6 bg-purple-900/20 border border-purple-500/30 rounded-lg\">\n          <h3 className=\"text-lg font-semibold text-purple-400\">Total Users</h3>\n          <p className=\"text-3xl font-bold\">{users.length}</p>\n        </div>\n      </div>\n\n      {error && (\n        <div className=\"mb-6 p-4 bg-red-900/20 border border-red-500/30 rounded-lg text-red-400\">\n          {error}\n        </div>\n      )}\n\n      {/* Tabs */}\n      <div className=\"mb-6\">\n        <div className=\"flex space-x-1 bg-gray-800 p-1 rounded-lg\">\n          <button\n            onClick={() => setActiveTab('proofs')}\n            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${\n              activeTab === 'proofs'\n                ? 'bg-blue-600 text-white'\n                : 'text-gray-400 hover:text-white'\n            }`}\n          >\n            Payment Proofs ({pendingProofs.length})\n          </button>\n          <button\n            onClick={() => setActiveTab('orders')}\n            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${\n              activeTab === 'orders'\n                ? 'bg-blue-600 text-white'\n                : 'text-gray-400 hover:text-white'\n            }`}\n          >\n            All Orders ({orders.length})\n          </button>\n          <button\n            onClick={() => setActiveTab('users')}\n            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${\n              activeTab === 'users'\n                ? 'bg-blue-600 text-white'\n                : 'text-gray-400 hover:text-white'\n            }`}\n          >\n            Users ({users.length})\n          </button>\n        </div>\n      </div>\n\n      {/* Content */}\n      {activeTab === 'proofs' && (\n        <div className=\"space-y-6\">\n          <h2 className=\"text-2xl font-bold text-blue-400\">Payment Proofs Verification</h2>\n\n          {/* Debug Info */}\n          <div className=\"p-4 bg-gray-800 border border-gray-700 rounded-lg text-sm\">\n            <h3 className=\"font-semibold mb-2\">Debug Info:</h3>\n            <div className=\"grid grid-cols-2 gap-4 text-xs\">\n              <div>Total Proofs: {proofs.length}</div>\n              <div>Pending Proofs: {pendingProofs.length}</div>\n              <div>Total Orders: {orders.length}</div>\n              <div>Pending Orders: {pendingOrders.length}</div>\n            </div>\n            {proofs.length > 0 && (\n              <div className=\"mt-2\">\n                <p className=\"text-xs text-gray-400\">Recent proofs:</p>\n                {proofs.slice(0, 3).map(p => (\n                  <div key={p.id} className=\"text-xs\">\n                    Proof {p.id.slice(0, 8)} - Order Status: {p.orders?.status || 'No order data'}\n                  </div>\n                ))}\n              </div>\n            )}\n          </div>\n\n          {pendingProofs.length === 0 ? (\n            <div className=\"text-center py-8\">\n              <div className=\"text-gray-500 mb-4\">No pending payment proofs to verify</div>\n              {proofs.length > 0 && (\n                <div className=\"text-sm text-gray-600\">\n                  Total proofs in system: {proofs.length} (all completed/cancelled)\n                </div>\n              )}\n            </div>\n          ) : (\n            <div className=\"space-y-4\">\n              {pendingProofs.map((proof) => (\n                <div key={proof.id} className=\"p-6 bg-gray-800 border border-gray-700 rounded-lg\">\n                  <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n                    {/* Order Details */}\n                    <div>\n                      <h3 className=\"text-lg font-semibold mb-4 text-blue-400\">Order Details</h3>\n                      <div className=\"space-y-2 text-sm\">\n                        <div className=\"flex justify-between\">\n                          <span className=\"text-gray-400\">Order ID:</span>\n                          <span className=\"font-mono\">{proof.order_id.slice(0, 8)}...</span>\n                        </div>\n                        <div className=\"flex justify-between\">\n                          <span className=\"text-gray-400\">Amount:</span>\n                          <span className=\"font-semibold\">{proof.orders.amount} USDT</span>\n                        </div>\n                        <div className=\"flex justify-between\">\n                          <span className=\"text-gray-400\">Rate:</span>\n                          <span>₹{proof.orders.rate}</span>\n                        </div>\n                        <div className=\"flex justify-between\">\n                          <span className=\"text-gray-400\">Total:</span>\n                          <span className=\"font-semibold text-green-400\">₹{(proof.orders.amount * proof.orders.rate).toFixed(2)}</span>\n                        </div>\n                        <div className=\"flex justify-between\">\n                          <span className=\"text-gray-400\">Seller:</span>\n                          <span className=\"font-mono\">{formatWallet(proof.orders.user_wallet)}</span>\n                        </div>\n                        <div className=\"flex justify-between\">\n                          <span className=\"text-gray-400\">Buyer:</span>\n                          <span className=\"font-mono\">{formatWallet(proof.buyer_wallet)}</span>\n                        </div>\n                        <div className=\"flex justify-between\">\n                          <span className=\"text-gray-400\">Submitted:</span>\n                          <span>{formatDate(proof.created_at)}</span>\n                        </div>\n                        <div className=\"flex justify-between\">\n                          <span className=\"text-gray-400\">Seller Confirmation:</span>\n                          <span className={proof.confirmed_by_seller ? 'text-green-400' : 'text-yellow-400'}>\n                            {proof.confirmed_by_seller ? '✅ Confirmed' : '⏳ Pending'}\n                          </span>\n                        </div>\n                      </div>\n                    </div>\n\n                    {/* Payment Proof */}\n                    <div>\n                      <h3 className=\"text-lg font-semibold mb-4 text-purple-400\">Payment Proof</h3>\n                      <div className=\"mb-4\">\n                        <img \n                          src={proof.proof_url} \n                          alt=\"Payment Proof\"\n                          className=\"w-full max-w-sm rounded-lg border border-gray-600\"\n                          onError={(e) => {\n                            e.currentTarget.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjMzMzIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlIG5vdCBmb3VuZDwvdGV4dD48L3N2Zz4='\n                          }}\n                        />\n                      </div>\n                      <a \n                        href={proof.proof_url} \n                        target=\"_blank\" \n                        rel=\"noopener noreferrer\"\n                        className=\"text-blue-400 hover:text-blue-300 text-sm underline\"\n                      >\n                        View Full Size →\n                      </a>\n                    </div>\n                  </div>\n\n                  {/* Actions */}\n                  <div className=\"mt-6 pt-4 border-t border-gray-700\">\n                    <div className=\"space-y-4\">\n                      {/* Step 1: Confirm with Seller */}\n                      <div className=\"flex items-center gap-4\">\n                        <button\n                          onClick={() => confirmWithSeller(proof.id)}\n                          disabled={confirmedProofs.has(proof.id)}\n                          className={`px-4 py-2 rounded-lg font-medium ${\n                            confirmedProofs.has(proof.id)\n                              ? 'bg-green-600 text-white cursor-not-allowed'\n                              : 'bg-yellow-600 hover:bg-yellow-700 text-white'\n                          }`}\n                        >\n                          {confirmedProofs.has(proof.id) ? '✅ Confirmed with Seller' : 'Confirm with Seller'}\n                        </button>\n                        {confirmedProofs.has(proof.id) && (\n                          <span className=\"text-sm text-green-400\">Payment verified externally</span>\n                        )}\n                      </div>\n\n                      {/* Step 2: Release USDT */}\n                      <div className=\"flex gap-4\">\n                        <button\n                          onClick={() => releaseUSDT(proof)}\n                          disabled={\n                            processingRelease === proof.id ||\n                            !confirmedProofs.has(proof.id) ||\n                            !proof.confirmed_by_seller\n                          }\n                          className=\"btn-primary bg-green-600 hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed\"\n                        >\n                          {processingRelease === proof.id ? 'Releasing...' : 'Release USDT'}\n                        </button>\n                        <button\n                          onClick={() => dbHelpers.updateOrderStatus(proof.order_id, 'cancelled')}\n                          className=\"px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg\"\n                        >\n                          Reject Order\n                        </button>\n                      </div>\n\n                      {/* Status Messages */}\n                      {!confirmedProofs.has(proof.id) && (\n                        <p className=\"text-xs text-yellow-400\">\n                          ⚠️ Step 1: Confirm payment with seller externally first\n                        </p>\n                      )}\n                      {confirmedProofs.has(proof.id) && !proof.confirmed_by_seller && (\n                        <p className=\"text-xs text-yellow-400\">\n                          ⚠️ Step 2: Waiting for seller to confirm payment receipt\n                        </p>\n                      )}\n                      {confirmedProofs.has(proof.id) && proof.confirmed_by_seller && (\n                        <p className=\"text-xs text-green-400\">\n                          ✅ Ready to release USDT to buyer\n                        </p>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n      )}\n\n      {activeTab === 'orders' && (\n        <div className=\"space-y-6\">\n          <h2 className=\"text-2xl font-bold text-yellow-400\">All Orders</h2>\n          {/* Orders content will be similar to existing AdminDashboard */}\n          <div className=\"text-center py-8 text-gray-500\">\n            Orders management interface (existing AdminDashboard component can be integrated here)\n          </div>\n        </div>\n      )}\n\n      {activeTab === 'users' && (\n        <div className=\"space-y-6\">\n          <h2 className=\"text-2xl font-bold text-purple-400\">Users</h2>\n          <div className=\"grid gap-4\">\n            {users.map((user) => (\n              <div key={user.wallet} className=\"p-4 bg-gray-800 border border-gray-700 rounded-lg\">\n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                  <div>\n                    <p className=\"text-sm text-gray-400\">Wallet</p>\n                    <p className=\"font-mono\">{user.wallet}</p>\n                  </div>\n                  <div>\n                    <p className=\"text-sm text-gray-400\">UPI ID</p>\n                    <p>{user.upi_id || 'Not provided'}</p>\n                  </div>\n                  <div>\n                    <p className=\"text-sm text-gray-400\">Bank Details</p>\n                    <p className=\"text-sm\">{user.bank_details || 'Not provided'}</p>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* Refresh Button */}\n      <div className=\"mt-8 text-center space-x-4\">\n        <button\n          onClick={loadAllData}\n          className=\"btn-primary px-6 py-2\"\n        >\n          Refresh Data\n        </button>\n\n        {/* Debug: Show raw data */}\n        <button\n          onClick={() => {\n            console.log('Raw admin data:', { orders, proofs, users })\n            alert(`Data loaded: ${orders.length} orders, ${proofs.length} proofs, ${users.length} users`)\n          }}\n          className=\"px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg text-sm\"\n        >\n          Debug Data\n        </button>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AA+Be,SAAS;IACtB,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,2JAAA,CAAA,aAAU,AAAD;IAC7B,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAChD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAChD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAC5C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiC;IAC1E,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC1E,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IAExE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,cAAc;QAClB,IAAI;YACF,WAAW;YACX,SAAS;YAET,4BAA4B;YAC5B,MAAM,CAAC,cAAc,cAAc,YAAY,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAClE,sHAAA,CAAA,YAAS,CAAC,YAAY;gBACtB,sHAAA,CAAA,YAAS,CAAC,YAAY;gBACtB,sHAAA,CAAA,YAAS,CAAC,WAAW;aACtB;YAED,IAAI,aAAa,KAAK,EAAE;gBACtB,QAAQ,KAAK,CAAC,iBAAiB,aAAa,KAAK;gBACjD,SAAS,4BAA4B,aAAa,KAAK,CAAC,OAAO;gBAC/D;YACF;YAEA,IAAI,aAAa,KAAK,EAAE;gBACtB,QAAQ,KAAK,CAAC,iBAAiB,aAAa,KAAK;gBACjD,SAAS,4BAA4B,aAAa,KAAK,CAAC,OAAO;gBAC/D;YACF;YAEA,IAAI,YAAY,KAAK,EAAE;gBACrB,QAAQ,KAAK,CAAC,gBAAgB,YAAY,KAAK;gBAC/C,SAAS,2BAA2B,YAAY,KAAK,CAAC,OAAO;gBAC7D;YACF;YAEA,QAAQ,GAAG,CAAC,gBAAgB;gBAC1B,QAAQ,aAAa,IAAI,EAAE,UAAU;gBACrC,QAAQ,aAAa,IAAI,EAAE,UAAU;gBACrC,OAAO,YAAY,IAAI,EAAE,UAAU;YACrC;YAEA,UAAU,aAAa,IAAI,IAAI,EAAE;YACjC,UAAU,aAAa,IAAI,IAAI,EAAE;YACjC,SAAS,YAAY,IAAI,IAAI,EAAE;QAEjC,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,oBAAoB;QACpC,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,mBAAmB,CAAA,OAAQ,IAAI,IAAI;mBAAI;gBAAM;aAAQ;IACvD;IAEA,MAAM,cAAc,OAAO;QACzB,IAAI,CAAC,SAAS;YACZ,SAAS;YACT;QACF;QAEA,IAAI,CAAC,gBAAgB,GAAG,CAAC,MAAM,EAAE,GAAG;YAClC,SAAS;YACT;QACF;QAEA,IAAI,CAAC,MAAM,mBAAmB,EAAE;YAC9B,SAAS;YACT;QACF;QAEA,qBAAqB,MAAM,EAAE;QAC7B,SAAS;QAET,IAAI;YACF,2CAA2C;YAC3C,MAAM,oBAAoB,MAAM,YAAY,EAAE,MAAM,MAAM,CAAC,MAAM;YAEjE,mCAAmC;YACnC,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,YAAS,CAAC,iBAAiB,CAAC,MAAM,QAAQ,EAAE;YAEpE,IAAI,OAAO;gBACT,SAAS,oCAAoC,MAAM,OAAO;gBAC1D;YACF;YAEA,+BAA+B;YAC/B,mBAAmB,CAAA;gBACjB,MAAM,SAAS,IAAI,IAAI;gBACvB,OAAO,MAAM,CAAC,MAAM,EAAE;gBACtB,OAAO;YACT;YAEA,iCAAiC;YACjC,MAAM;QAER,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,uBAAuB;YACrC,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,qBAAqB;QACvB;IACF;IAEA,MAAM,sBAAsB,OAAO,aAAqB;QACtD,wCAAuD;YACrD,MAAM,IAAI,MAAM;QAClB;QAEA,+BAA+B;QAC/B,MAAM,eAAe;QAErB,+CAA+C;QAC/C,MAAM,eAAe,OAAO,KAAK,KAAK,CAAC,SAAS;QAChD,MAAM,YAAY,aAAa,QAAQ,CAAC,IAAI,QAAQ,CAAC,IAAI;QAEzD,6DAA6D;QAC7D,MAAM,kBAAkB,YAAY,KAAK,CAAC,GAAG,WAAW,GAAG,QAAQ,CAAC,IAAI;QAExE,+DAA+D;QAC/D,MAAM,eAAe,CAAC,UAAU,EAAE,kBAAkB,WAAW;QAE/D,MAAM,WAAW;YACf,MAAM;YACN,IAAI;YACJ,MAAM;YACN,KAAK;QACP;QAEA,QAAQ,GAAG,CAAC,mBAAmB;YAC7B;YACA,WAAW,aAAa,QAAQ;YAChC;YACA;QACF;QAEA,MAAM,SAAS,MAAM,OAAO,QAAQ,CAAC,OAAO,CAAC;YAC3C,QAAQ;YACR,QAAQ;gBAAC;aAAS;QACpB;QAEA,QAAQ,GAAG,CAAC,6BAA6B;QACzC,OAAO;IACT;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,cAAc;IAC5C;IAEA,MAAM,eAAe,CAAC;QACpB,OAAO,GAAG,OAAO,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,OAAO,KAAK,CAAC,CAAC,IAAI;IACtD;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;;;;;8BACf,8OAAC;8BAAE;;;;;;;;;;;;IAGT;IAEA,MAAM,gBAAgB,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,EAAE,WAAW;IACtE,MAAM,gBAAgB,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK;IAE9D,QAAQ,GAAG,CAAC,mBAAmB;QAC7B,aAAa,OAAO,MAAM;QAC1B,eAAe,cAAc,MAAM;QACnC,aAAa,OAAO,MAAM;QAC1B,eAAe,cAAc,MAAM;QACnC,YAAY,OAAO,GAAG,CAAC,CAAA,IAAK,CAAC;gBAAE,IAAI,EAAE,EAAE;gBAAE,aAAa,EAAE,MAAM,EAAE;YAAO,CAAC;IAC1E;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;0CACpD,8OAAC;gCAAE,WAAU;0CAAsB,cAAc,MAAM;;;;;;;;;;;;kCAEzD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CACtD,8OAAC;gCAAE,WAAU;0CAAsB,cAAc,MAAM;;;;;;;;;;;;kCAEzD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAuC;;;;;;0CACrD,8OAAC;gCAAE,WAAU;0CAAsB,OAAO,MAAM;;;;;;;;;;;;kCAElD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CACtD,8OAAC;gCAAE,WAAU;0CAAsB,MAAM,MAAM;;;;;;;;;;;;;;;;;;YAIlD,uBACC,8OAAC;gBAAI,WAAU;0BACZ;;;;;;0BAKL,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS,IAAM,aAAa;4BAC5B,WAAW,CAAC,kEAAkE,EAC5E,cAAc,WACV,2BACA,kCACJ;;gCACH;gCACkB,cAAc,MAAM;gCAAC;;;;;;;sCAExC,8OAAC;4BACC,SAAS,IAAM,aAAa;4BAC5B,WAAW,CAAC,kEAAkE,EAC5E,cAAc,WACV,2BACA,kCACJ;;gCACH;gCACc,OAAO,MAAM;gCAAC;;;;;;;sCAE7B,8OAAC;4BACC,SAAS,IAAM,aAAa;4BAC5B,WAAW,CAAC,kEAAkE,EAC5E,cAAc,UACV,2BACA,kCACJ;;gCACH;gCACS,MAAM,MAAM;gCAAC;;;;;;;;;;;;;;;;;;YAM1B,cAAc,0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAmC;;;;;;kCAGjD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAqB;;;;;;0CACnC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;4CAAI;4CAAe,OAAO,MAAM;;;;;;;kDACjC,8OAAC;;4CAAI;4CAAiB,cAAc,MAAM;;;;;;;kDAC1C,8OAAC;;4CAAI;4CAAe,OAAO,MAAM;;;;;;;kDACjC,8OAAC;;4CAAI;4CAAiB,cAAc,MAAM;;;;;;;;;;;;;4BAE3C,OAAO,MAAM,GAAG,mBACf,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;oCACpC,OAAO,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,kBACtB,8OAAC;4CAAe,WAAU;;gDAAU;gDAC3B,EAAE,EAAE,CAAC,KAAK,CAAC,GAAG;gDAAG;gDAAkB,EAAE,MAAM,EAAE,UAAU;;2CADtD,EAAE,EAAE;;;;;;;;;;;;;;;;;oBAQrB,cAAc,MAAM,KAAK,kBACxB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAqB;;;;;;4BACnC,OAAO,MAAM,GAAG,mBACf,8OAAC;gCAAI,WAAU;;oCAAwB;oCACZ,OAAO,MAAM;oCAAC;;;;;;;;;;;;6CAK7C,8OAAC;wBAAI,WAAU;kCACZ,cAAc,GAAG,CAAC,CAAC,sBAClB,8OAAC;gCAAmB,WAAU;;kDAC5B,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA2C;;;;;;kEACzD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,8OAAC;wEAAK,WAAU;;4EAAa,MAAM,QAAQ,CAAC,KAAK,CAAC,GAAG;4EAAG;;;;;;;;;;;;;0EAE1D,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,8OAAC;wEAAK,WAAU;;4EAAiB,MAAM,MAAM,CAAC,MAAM;4EAAC;;;;;;;;;;;;;0EAEvD,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,8OAAC;;4EAAK;4EAAE,MAAM,MAAM,CAAC,IAAI;;;;;;;;;;;;;0EAE3B,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,8OAAC;wEAAK,WAAU;;4EAA+B;4EAAE,CAAC,MAAM,MAAM,CAAC,MAAM,GAAG,MAAM,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC;;;;;;;;;;;;;0EAErG,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,8OAAC;wEAAK,WAAU;kFAAa,aAAa,MAAM,MAAM,CAAC,WAAW;;;;;;;;;;;;0EAEpE,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,8OAAC;wEAAK,WAAU;kFAAa,aAAa,MAAM,YAAY;;;;;;;;;;;;0EAE9D,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,8OAAC;kFAAM,WAAW,MAAM,UAAU;;;;;;;;;;;;0EAEpC,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,8OAAC;wEAAK,WAAW,MAAM,mBAAmB,GAAG,mBAAmB;kFAC7D,MAAM,mBAAmB,GAAG,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;0DAOrD,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DACC,KAAK,MAAM,SAAS;4DACpB,KAAI;4DACJ,WAAU;4DACV,SAAS,CAAC;gEACR,EAAE,aAAa,CAAC,GAAG,GAAG;4DACxB;;;;;;;;;;;kEAGJ,8OAAC;wDACC,MAAM,MAAM,SAAS;wDACrB,QAAO;wDACP,KAAI;wDACJ,WAAU;kEACX;;;;;;;;;;;;;;;;;;kDAOL,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DAEb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,SAAS,IAAM,kBAAkB,MAAM,EAAE;4DACzC,UAAU,gBAAgB,GAAG,CAAC,MAAM,EAAE;4DACtC,WAAW,CAAC,iCAAiC,EAC3C,gBAAgB,GAAG,CAAC,MAAM,EAAE,IACxB,+CACA,gDACJ;sEAED,gBAAgB,GAAG,CAAC,MAAM,EAAE,IAAI,4BAA4B;;;;;;wDAE9D,gBAAgB,GAAG,CAAC,MAAM,EAAE,mBAC3B,8OAAC;4DAAK,WAAU;sEAAyB;;;;;;;;;;;;8DAK7C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,SAAS,IAAM,YAAY;4DAC3B,UACE,sBAAsB,MAAM,EAAE,IAC9B,CAAC,gBAAgB,GAAG,CAAC,MAAM,EAAE,KAC7B,CAAC,MAAM,mBAAmB;4DAE5B,WAAU;sEAET,sBAAsB,MAAM,EAAE,GAAG,iBAAiB;;;;;;sEAErD,8OAAC;4DACC,SAAS,IAAM,sHAAA,CAAA,YAAS,CAAC,iBAAiB,CAAC,MAAM,QAAQ,EAAE;4DAC3D,WAAU;sEACX;;;;;;;;;;;;gDAMF,CAAC,gBAAgB,GAAG,CAAC,MAAM,EAAE,mBAC5B,8OAAC;oDAAE,WAAU;8DAA0B;;;;;;gDAIxC,gBAAgB,GAAG,CAAC,MAAM,EAAE,KAAK,CAAC,MAAM,mBAAmB,kBAC1D,8OAAC;oDAAE,WAAU;8DAA0B;;;;;;gDAIxC,gBAAgB,GAAG,CAAC,MAAM,EAAE,KAAK,MAAM,mBAAmB,kBACzD,8OAAC;oDAAE,WAAU;8DAAyB;;;;;;;;;;;;;;;;;;+BAzHpC,MAAM,EAAE;;;;;;;;;;;;;;;;YAsI3B,cAAc,0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAqC;;;;;;kCAEnD,8OAAC;wBAAI,WAAU;kCAAiC;;;;;;;;;;;;YAMnD,cAAc,yBACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAqC;;;;;;kCACnD,8OAAC;wBAAI,WAAU;kCACZ,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC;gCAAsB,WAAU;0CAC/B,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;8DACrC,8OAAC;oDAAE,WAAU;8DAAa,KAAK,MAAM;;;;;;;;;;;;sDAEvC,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;8DACrC,8OAAC;8DAAG,KAAK,MAAM,IAAI;;;;;;;;;;;;sDAErB,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;8DACrC,8OAAC;oDAAE,WAAU;8DAAW,KAAK,YAAY,IAAI;;;;;;;;;;;;;;;;;;+BAZzC,KAAK,MAAM;;;;;;;;;;;;;;;;0BAsB7B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;kCAKD,8OAAC;wBACC,SAAS;4BACP,QAAQ,GAAG,CAAC,mBAAmB;gCAAE;gCAAQ;gCAAQ;4BAAM;4BACvD,MAAM,CAAC,aAAa,EAAE,OAAO,MAAM,CAAC,SAAS,EAAE,OAAO,MAAM,CAAC,SAAS,EAAE,MAAM,MAAM,CAAC,MAAM,CAAC;wBAC9F;wBACA,WAAU;kCACX;;;;;;;;;;;;;;;;;;AAMT", "debugId": null}}, {"offset": {"line": 2066, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/src/app/admin/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useAccount } from 'wagmi'\nimport { useEffect, useState } from 'react'\nimport WalletConnect from '@/components/WalletConnect'\nimport AdminVerificationDashboard from '@/components/AdminVerificationDashboard'\n\nexport default function AdminPage() {\n  const { address, isConnected } = useAccount()\n  const [mounted, setMounted] = useState(false)\n  const [isAdmin, setIsAdmin] = useState(false)\n\n  useEffect(() => {\n    setMounted(true)\n  }, [])\n\n  useEffect(() => {\n    if (isConnected && address) {\n      const adminWallet = process.env.NEXT_PUBLIC_ADMIN_WALLET_ADDRESS?.toLowerCase()\n      const userWallet = address.toLowerCase()\n      setIsAdmin(adminWallet === userWallet)\n    } else {\n      setIsAdmin(false)\n    }\n  }, [isConnected, address])\n\n  if (!mounted) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-lg\">Loading...</div>\n      </div>\n    )\n  }\n\n  if (!isConnected) {\n    return (\n      <div className=\"min-h-screen flex flex-col items-center justify-center p-8\">\n        <div className=\"text-center max-w-md mx-auto\">\n          <h1 className=\"text-4xl font-bold mb-4\">\n            <span className=\"text-blue-400\">Admin</span> Dashboard\n          </h1>\n          <p className=\"text-gray-400 mb-8\">\n            Connect your admin wallet to access the dashboard\n          </p>\n          <WalletConnect />\n        </div>\n      </div>\n    )\n  }\n\n  if (!isAdmin) {\n    return (\n      <div className=\"min-h-screen flex flex-col items-center justify-center p-8\">\n        <div className=\"text-center max-w-md mx-auto\">\n          <div className=\"mb-6 p-6 bg-red-900/20 border border-red-500/30 rounded-lg\">\n            <h1 className=\"text-3xl font-bold mb-4 text-red-400\">❌ Access Denied</h1>\n            <p className=\"text-gray-300 mb-4\">\n              You are not authorized to access the admin dashboard.\n            </p>\n            <div className=\"text-sm text-gray-500\">\n              <p>Connected wallet: {address?.slice(0, 8)}...{address?.slice(-6)}</p>\n              <p>Admin wallet required</p>\n            </div>\n          </div>\n          \n          <div className=\"space-y-4\">\n            <a \n              href=\"/\"\n              className=\"btn-primary inline-block px-6 py-3\"\n            >\n              ← Back to Home\n            </a>\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-900\">\n      {/* Admin Header */}\n      <div className=\"bg-blue-900/20 border-b border-blue-500/30 p-4\">\n        <div className=\"max-w-7xl mx-auto flex justify-between items-center\">\n          <div>\n            <h1 className=\"text-2xl font-bold text-blue-400\">Admin Dashboard</h1>\n            <p className=\"text-sm text-gray-400\">\n              Wallet: {address?.slice(0, 8)}...{address?.slice(-6)}\n            </p>\n          </div>\n          <div className=\"flex gap-4\">\n            <a \n              href=\"/\"\n              className=\"px-4 py-2 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-800\"\n            >\n              ← Home\n            </a>\n          </div>\n        </div>\n      </div>\n\n      {/* Admin Content */}\n      <div className=\"p-6\">\n        <AdminVerificationDashboard />\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,2JAAA,CAAA,aAAU,AAAD;IAC1C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;IACb,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,eAAe,SAAS;YAC1B,MAAM,8FAA4D;YAClE,MAAM,aAAa,QAAQ,WAAW;YACtC,WAAW,gBAAgB;QAC7B,OAAO;YACL,WAAW;QACb;IACF,GAAG;QAAC;QAAa;KAAQ;IAEzB,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BAAU;;;;;;;;;;;IAG/B;IAEA,IAAI,CAAC,aAAa;QAChB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC;gCAAK,WAAU;0CAAgB;;;;;;4BAAY;;;;;;;kCAE9C,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAGlC,8OAAC,mIAAA,CAAA,UAAa;;;;;;;;;;;;;;;;IAItB;IAEA,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAuC;;;;;;0CACrD,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAGlC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;4CAAE;4CAAmB,SAAS,MAAM,GAAG;4CAAG;4CAAI,SAAS,MAAM,CAAC;;;;;;;kDAC/D,8OAAC;kDAAE;;;;;;;;;;;;;;;;;;kCAIP,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,MAAK;4BACL,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;IAOX;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,8OAAC;oCAAE,WAAU;;wCAAwB;wCAC1B,SAAS,MAAM,GAAG;wCAAG;wCAAI,SAAS,MAAM,CAAC;;;;;;;;;;;;;sCAGtD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;0BAQP,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,gJAAA,CAAA,UAA0B;;;;;;;;;;;;;;;;AAInC", "debugId": null}}]}