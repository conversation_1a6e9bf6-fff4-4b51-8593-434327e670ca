(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/@metamask/sdk/dist/browser/es/metamask-sdk.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_41d702de._.js",
  "static/chunks/node_modules_@metamask_sdk_dist_browser_es_metamask-sdk_54d6eb22.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@metamask/sdk/dist/browser/es/metamask-sdk.js [app-client] (ecmascript)");
    });
});
}}),
}]);