import { createConfig, http } from 'wagmi'
import { bsc } from 'wagmi/chains'
import { metaMask, injected } from 'wagmi/connectors'

// BSC Mainnet configuration
export const config = createConfig({
  chains: [bsc],
  connectors: [
    metaMask(),
    injected(),
  ],
  transports: {
    [bsc.id]: http('https://bsc-dataseed1.binance.org/'),
  },
})

// USDT Contract on BSC Mainnet
export const USDT_CONTRACT = {
  address: '0x55d398326f99059fF775485246999027B3197955' as const,
  abi: [
    // ERC20 standard functions we need
    {
      name: 'transfer',
      type: 'function',
      stateMutability: 'nonpayable',
      inputs: [
        { name: 'to', type: 'address' },
        { name: 'amount', type: 'uint256' }
      ],
      outputs: [{ name: '', type: 'bool' }]
    },
    {
      name: 'balanceOf',
      type: 'function',
      stateMutability: 'view',
      inputs: [{ name: 'account', type: 'address' }],
      outputs: [{ name: '', type: 'uint256' }]
    },
    {
      name: 'allowance',
      type: 'function',
      stateMutability: 'view',
      inputs: [
        { name: 'owner', type: 'address' },
        { name: 'spender', type: 'address' }
      ],
      outputs: [{ name: '', type: 'uint256' }]
    },
    {
      name: 'approve',
      type: 'function',
      stateMutability: 'nonpayable',
      inputs: [
        { name: 'spender', type: 'address' },
        { name: 'amount', type: 'uint256' }
      ],
      outputs: [{ name: '', type: 'bool' }]
    }
  ] as const
}
