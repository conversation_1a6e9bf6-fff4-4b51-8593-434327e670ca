{"version": 3, "file": "request.js", "sourceRoot": "", "sources": ["../../errors/request.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,uBAAuB,CAAA;AAEjD,OAAO,EAAE,SAAS,EAAE,MAAM,WAAW,CAAA;AACrC,OAAO,EAAE,MAAM,EAAE,MAAM,YAAY,CAAA;AAKnC,MAAM,OAAO,gBAAiB,SAAQ,SAAS;IAM7C,YAAY,EACV,IAAI,EACJ,KAAK,EACL,OAAO,EACP,OAAO,EACP,MAAM,EACN,GAAG,GAQJ;QACC,KAAK,CAAC,sBAAsB,EAAE;YAC5B,KAAK;YACL,OAAO;YACP,YAAY,EAAE;gBACZ,MAAM,IAAI,WAAW,MAAM,EAAE;gBAC7B,QAAQ,MAAM,CAAC,GAAG,CAAC,EAAE;gBACrB,IAAI,IAAI,iBAAiB,SAAS,CAAC,IAAI,CAAC,EAAE;aAC3C,CAAC,MAAM,CAAC,OAAO,CAAa;YAC7B,IAAI,EAAE,kBAAkB;SACzB,CAAC,CAAA;QA7BJ;;;;;WAAwE;QACxE;;;;;WAA6B;QAC7B;;;;;WAA2B;QAC3B;;;;;WAAW;QA2BT,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAChB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAA;QACtB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;QACpB,IAAI,CAAC,GAAG,GAAG,GAAG,CAAA;IAChB,CAAC;CACF;AAKD,MAAM,OAAO,qBAAsB,SAAQ,SAAS;IAClD,YAAY,EACV,IAAI,EACJ,KAAK,EACL,OAAO,EACP,GAAG,GAMJ;QACC,KAAK,CAAC,2BAA2B,EAAE;YACjC,KAAK;YACL,OAAO;YACP,YAAY,EAAE;gBACZ,QAAQ,MAAM,CAAC,GAAG,CAAC,EAAE;gBACrB,IAAI,IAAI,iBAAiB,SAAS,CAAC,IAAI,CAAC,EAAE;aAC3C,CAAC,MAAM,CAAC,OAAO,CAAa;YAC7B,IAAI,EAAE,uBAAuB;SAC9B,CAAC,CAAA;IACJ,CAAC;CACF;AAKD,MAAM,OAAO,eAAgB,SAAQ,SAAS;IAI5C,YAAY,EACV,IAAI,EACJ,KAAK,EACL,GAAG,GAKJ;QACC,KAAK,CAAC,qBAAqB,EAAE;YAC3B,KAAK,EAAE,KAAY;YACnB,OAAO,EAAE,KAAK,CAAC,OAAO;YACtB,YAAY,EAAE,CAAC,QAAQ,MAAM,CAAC,GAAG,CAAC,EAAE,EAAE,iBAAiB,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC;YACzE,IAAI,EAAE,iBAAiB;SACxB,CAAC,CAAA;QAjBJ;;;;;WAAY;QACZ;;;;;WAAc;QAiBZ,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAA;QACtB,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAA;IACxB,CAAC;CACF;AAKD,MAAM,OAAO,iBAAkB,SAAQ,SAAS;IAC9C,YAAY,EACV,GAAG,MAGD,EAAE;QACJ,KAAK,CAAC,6BAA6B,EAAE;YACnC,YAAY,EAAE,CAAC,GAAG,IAAI,QAAQ,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAa;YACxE,IAAI,EAAE,mBAAmB;SAC1B,CAAC,CAAA;IACJ,CAAC;CACF;AAKD,MAAM,OAAO,YAAa,SAAQ,SAAS;IACzC,YAAY,EACV,IAAI,EACJ,GAAG,GAIJ;QACC,KAAK,CAAC,uCAAuC,EAAE;YAC7C,OAAO,EAAE,wBAAwB;YACjC,YAAY,EAAE,CAAC,QAAQ,MAAM,CAAC,GAAG,CAAC,EAAE,EAAE,iBAAiB,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC;YACzE,IAAI,EAAE,cAAc;SACrB,CAAC,CAAA;IACJ,CAAC;CACF"}