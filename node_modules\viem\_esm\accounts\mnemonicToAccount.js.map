{"version": 3, "file": "mnemonicToAccount.js", "sourceRoot": "", "sources": ["../../accounts/mnemonicToAccount.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,KAAK,EAAE,MAAM,cAAc,CAAA;AACpC,OAAO,EAAE,kBAAkB,EAAE,MAAM,cAAc,CAAA;AAGjD,OAAO,EAGL,cAAc,GACf,MAAM,qBAAqB,CAAA;AAO5B;;;;GAIG;AACH,MAAM,UAAU,iBAAiB,CAC/B,QAAgB,EAChB,OAAiC,EAAE;IAEnC,MAAM,IAAI,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAA;IACzC,OAAO,cAAc,CAAC,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAA;AACzD,CAAC"}