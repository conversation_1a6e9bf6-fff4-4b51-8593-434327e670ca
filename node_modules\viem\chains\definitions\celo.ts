import { chainConfig } from '../../celo/chainConfig.js'
import { define<PERSON>hain } from '../../utils/chain/defineChain.js'

export const celo = /*#__PURE__*/ defineChain({
  ...chainConfig,
  id: 42_220,
  name: '<PERSON><PERSON>',
  nativeCurrency: {
    decimals: 18,
    name: '<PERSON><PERSON><PERSON>',
    symbol: '<PERSON><PERSON><PERSON>',
  },
  rpcUrls: {
    default: { http: ['https://forno.celo.org'] },
  },
  blockExplorers: {
    default: {
      name: 'Celo Explorer',
      url: 'https://celoscan.io',
      apiUrl: 'https://api.celoscan.io/api',
    },
  },
  contracts: {
    multicall3: {
      address: '0xcA11bde05977b3631167028862bE2a173976CA11',
      blockCreated: 13112599,
    },
  },
  testnet: false,
})
