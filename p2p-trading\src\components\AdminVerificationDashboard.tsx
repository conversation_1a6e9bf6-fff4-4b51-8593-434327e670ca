'use client'

import { useState, useEffect } from 'react'
import { useAccount } from 'wagmi'
import { dbHelpers } from '@/lib/supabase'

interface Order {
  id: string
  type: 'buy' | 'sell'
  amount: number
  rate: number
  status: 'pending' | 'completed' | 'cancelled'
  user_wallet: string
  created_at: string
  users: {
    wallet: string
    upi_id?: string
    bank_details?: string
  }
}

interface Proof {
  id: string
  order_id: string
  buyer_wallet: string
  proof_url: string
  created_at: string
  orders: Order
}

export default function AdminVerificationDashboard() {
  const { address } = useAccount()
  const [orders, setOrders] = useState<Order[]>([])
  const [proofs, setProofs] = useState<Proof[]>([])
  const [users, setUsers] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [activeTab, setActiveTab] = useState<'proofs' | 'orders' | 'users'>('proofs')
  const [processingRelease, setProcessingRelease] = useState<string | null>(null)

  useEffect(() => {
    loadAllData()
  }, [])

  const loadAllData = async () => {
    try {
      setLoading(true)
      setError('')

      // Load all data in parallel
      const [ordersResult, proofsResult, usersResult] = await Promise.all([
        dbHelpers.getAllOrders(),
        dbHelpers.getAllProofs(),
        dbHelpers.getAllUsers()
      ])

      if (ordersResult.error) {
        setError('Failed to load orders: ' + ordersResult.error.message)
        return
      }

      if (proofsResult.error) {
        setError('Failed to load proofs: ' + proofsResult.error.message)
        return
      }

      if (usersResult.error) {
        setError('Failed to load users: ' + usersResult.error.message)
        return
      }

      setOrders(ordersResult.data || [])
      setProofs(proofsResult.data || [])
      setUsers(usersResult.data || [])

    } catch (err) {
      setError('Failed to load data')
      console.error('Load data error:', err)
    } finally {
      setLoading(false)
    }
  }

  const releaseUSDT = async (proof: Proof) => {
    if (!address) {
      setError('Admin wallet not connected')
      return
    }

    setProcessingRelease(proof.id)
    setError('')

    try {
      // Transfer USDT from admin wallet to buyer
      await transferUSDTToBuyer(proof.buyer_wallet, proof.orders.amount)

      // Update order status to completed
      const { error } = await dbHelpers.updateOrderStatus(proof.order_id, 'completed')
      
      if (error) {
        setError('Failed to update order status: ' + error.message)
        return
      }

      // Reload data to reflect changes
      await loadAllData()

    } catch (err) {
      console.error('Release USDT error:', err)
      setError(err instanceof Error ? err.message : 'Failed to release USDT')
    } finally {
      setProcessingRelease(null)
    }
  }

  const transferUSDTToBuyer = async (buyerWallet: string, amount: number) => {
    if (typeof window === 'undefined' || !window.ethereum) {
      throw new Error('MetaMask not found')
    }

    // USDT contract address on BSC
    const usdtContract = '******************************************'
    
    // Convert amount to wei (USDT has 18 decimals)
    const amountBigInt = BigInt(Math.floor(amount * 1e18))
    const amountHex = amountBigInt.toString(16).padStart(64, '0')
    
    // Clean buyer wallet address (remove 0x and pad to 64 chars)
    const buyerAddressHex = buyerWallet.slice(2).toLowerCase().padStart(64, '0')
    
    // ERC20 transfer function signature: transfer(address,uint256)
    const transferData = `0xa9059cbb${buyerAddressHex}${amountHex}`

    const txParams = {
      from: address,
      to: usdtContract,
      data: transferData,
      gas: '0x15F90', // 90000 gas for ERC20 transfer
    }

    console.log('Releasing USDT:', {
      amount,
      amountWei: amountBigInt.toString(),
      buyerWallet,
      transferData
    })

    const txHash = await window.ethereum.request({
      method: 'eth_sendTransaction',
      params: [txParams],
    })

    console.log('USDT release transaction:', txHash)
    return txHash
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString()
  }

  const formatWallet = (wallet: string) => {
    return `${wallet.slice(0, 6)}...${wallet.slice(-4)}`
  }

  if (loading) {
    return (
      <div className="text-center py-8">
        <div className="animate-spin w-8 h-8 border-4 border-blue-400 border-t-transparent rounded-full mx-auto mb-4"></div>
        <p>Loading admin data...</p>
      </div>
    )
  }

  const pendingProofs = proofs.filter(proof => proof.orders.status === 'pending')
  const pendingOrders = orders.filter(order => order.status === 'pending')

  return (
    <div className="max-w-7xl mx-auto">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div className="p-6 bg-blue-900/20 border border-blue-500/30 rounded-lg">
          <h3 className="text-lg font-semibold text-blue-400">Pending Proofs</h3>
          <p className="text-3xl font-bold">{pendingProofs.length}</p>
        </div>
        <div className="p-6 bg-yellow-900/20 border border-yellow-500/30 rounded-lg">
          <h3 className="text-lg font-semibold text-yellow-400">Pending Orders</h3>
          <p className="text-3xl font-bold">{pendingOrders.length}</p>
        </div>
        <div className="p-6 bg-green-900/20 border border-green-500/30 rounded-lg">
          <h3 className="text-lg font-semibold text-green-400">Total Orders</h3>
          <p className="text-3xl font-bold">{orders.length}</p>
        </div>
        <div className="p-6 bg-purple-900/20 border border-purple-500/30 rounded-lg">
          <h3 className="text-lg font-semibold text-purple-400">Total Users</h3>
          <p className="text-3xl font-bold">{users.length}</p>
        </div>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-900/20 border border-red-500/30 rounded-lg text-red-400">
          {error}
        </div>
      )}

      {/* Tabs */}
      <div className="mb-6">
        <div className="flex space-x-1 bg-gray-800 p-1 rounded-lg">
          <button
            onClick={() => setActiveTab('proofs')}
            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'proofs'
                ? 'bg-blue-600 text-white'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            Payment Proofs ({pendingProofs.length})
          </button>
          <button
            onClick={() => setActiveTab('orders')}
            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'orders'
                ? 'bg-blue-600 text-white'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            All Orders ({orders.length})
          </button>
          <button
            onClick={() => setActiveTab('users')}
            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'users'
                ? 'bg-blue-600 text-white'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            Users ({users.length})
          </button>
        </div>
      </div>

      {/* Content */}
      {activeTab === 'proofs' && (
        <div className="space-y-6">
          <h2 className="text-2xl font-bold text-blue-400">Payment Proofs Verification</h2>
          
          {pendingProofs.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              No pending payment proofs to verify
            </div>
          ) : (
            <div className="space-y-4">
              {pendingProofs.map((proof) => (
                <div key={proof.id} className="p-6 bg-gray-800 border border-gray-700 rounded-lg">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Order Details */}
                    <div>
                      <h3 className="text-lg font-semibold mb-4 text-blue-400">Order Details</h3>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-400">Order ID:</span>
                          <span className="font-mono">{proof.order_id.slice(0, 8)}...</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-400">Amount:</span>
                          <span className="font-semibold">{proof.orders.amount} USDT</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-400">Rate:</span>
                          <span>₹{proof.orders.rate}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-400">Total:</span>
                          <span className="font-semibold text-green-400">₹{(proof.orders.amount * proof.orders.rate).toFixed(2)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-400">Seller:</span>
                          <span className="font-mono">{formatWallet(proof.orders.user_wallet)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-400">Buyer:</span>
                          <span className="font-mono">{formatWallet(proof.buyer_wallet)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-400">Submitted:</span>
                          <span>{formatDate(proof.created_at)}</span>
                        </div>
                      </div>
                    </div>

                    {/* Payment Proof */}
                    <div>
                      <h3 className="text-lg font-semibold mb-4 text-purple-400">Payment Proof</h3>
                      <div className="mb-4">
                        <img 
                          src={proof.proof_url} 
                          alt="Payment Proof"
                          className="w-full max-w-sm rounded-lg border border-gray-600"
                          onError={(e) => {
                            e.currentTarget.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjMzMzIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlIG5vdCBmb3VuZDwvdGV4dD48L3N2Zz4='
                          }}
                        />
                      </div>
                      <a 
                        href={proof.proof_url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-blue-400 hover:text-blue-300 text-sm underline"
                      >
                        View Full Size →
                      </a>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="mt-6 pt-4 border-t border-gray-700">
                    <div className="flex gap-4">
                      <button
                        onClick={() => releaseUSDT(proof)}
                        disabled={processingRelease === proof.id}
                        className="btn-primary bg-green-600 hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {processingRelease === proof.id ? 'Releasing...' : 'Release USDT'}
                      </button>
                      <button
                        onClick={() => dbHelpers.updateOrderStatus(proof.order_id, 'cancelled')}
                        className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg"
                      >
                        Reject
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {activeTab === 'orders' && (
        <div className="space-y-6">
          <h2 className="text-2xl font-bold text-yellow-400">All Orders</h2>
          {/* Orders content will be similar to existing AdminDashboard */}
          <div className="text-center py-8 text-gray-500">
            Orders management interface (existing AdminDashboard component can be integrated here)
          </div>
        </div>
      )}

      {activeTab === 'users' && (
        <div className="space-y-6">
          <h2 className="text-2xl font-bold text-purple-400">Users</h2>
          <div className="grid gap-4">
            {users.map((user) => (
              <div key={user.wallet} className="p-4 bg-gray-800 border border-gray-700 rounded-lg">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <p className="text-sm text-gray-400">Wallet</p>
                    <p className="font-mono">{user.wallet}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-400">UPI ID</p>
                    <p>{user.upi_id || 'Not provided'}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-400">Bank Details</p>
                    <p className="text-sm">{user.bank_details || 'Not provided'}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Refresh Button */}
      <div className="mt-8 text-center">
        <button
          onClick={loadAllData}
          className="btn-primary px-6 py-2"
        >
          Refresh Data
        </button>
      </div>
    </div>
  )
}
