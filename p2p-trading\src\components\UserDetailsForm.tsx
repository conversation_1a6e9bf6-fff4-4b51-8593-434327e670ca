'use client'

import { useState } from 'react'
import { dbHelpers } from '@/lib/supabase'

interface UserDetailsFormProps {
  walletAddress: string
  onUserCreated: (user: any) => void
}

export default function UserDetailsForm({ walletAddress, onUserCreated }: UserDetailsFormProps) {
  const [formData, setFormData] = useState({
    upi_id: '',
    bank_details: ''
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.upi_id && !formData.bank_details) {
      setError('Please provide either UPI ID or Bank Details')
      return
    }

    setLoading(true)
    setError('')

    try {
      console.log('Saving user data:', {
        wallet: walletAddress,
        upi_id: formData.upi_id || null,
        bank_details: formData.bank_details || null
      })

      const userData = {
        wallet: walletAddress,
        upi_id: formData.upi_id || null,
        bank_details: formData.bank_details || null
      }

      const { data, error } = await dbHelpers.upsertUser(userData)

      console.log('Supabase response:', { data, error })

      if (error) {
        console.error('Supabase error details:', error)
        setError(`Failed to save user details: ${error.message}`)
        return
      }

      console.log('User created successfully:', data)
      onUserCreated(data)
    } catch (error) {
      console.error('Unexpected error saving user:', error)
      setError('An unexpected error occurred. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  return (
    <div className="max-w-md mx-auto">
      <div className="mb-6 p-4 bg-blue-900/20 border border-blue-500/30 rounded-lg">
        <h2 className="text-2xl font-bold mb-2 text-center text-blue-400">Complete Your Profile</h2>
        <p className="text-gray-300 text-center text-sm">
          Wallet: {walletAddress?.slice(0, 8)}...{walletAddress?.slice(-6)}
        </p>
      </div>
      <p className="text-gray-400 mb-6 text-center">
        Please provide your payment details to start trading
      </p>

      <form onSubmit={handleSubmit} className="space-y-6">
        <div>
          <label htmlFor="upi_id" className="block text-sm font-medium mb-2">
            UPI ID (Optional)
          </label>
          <input
            type="text"
            id="upi_id"
            name="upi_id"
            value={formData.upi_id}
            onChange={handleInputChange}
            placeholder="your-upi@paytm"
            className="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg focus:outline-none focus:border-blue-500"
          />
        </div>

        <div>
          <label htmlFor="bank_details" className="block text-sm font-medium mb-2">
            Bank Details (Optional)
          </label>
          <textarea
            id="bank_details"
            name="bank_details"
            value={formData.bank_details}
            onChange={handleInputChange}
            placeholder="Bank Name: XYZ Bank&#10;Account Number: **********&#10;IFSC: ABCD0123456&#10;Account Holder: Your Name"
            rows={4}
            className="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg focus:outline-none focus:border-blue-500 resize-none"
          />
        </div>

        {error && (
          <div className="text-red-500 text-sm text-center">
            {error}
          </div>
        )}

        <button
          type="submit"
          disabled={loading}
          className="btn-primary w-full disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {loading ? 'Saving...' : 'Save Details'}
        </button>
      </form>

      <p className="text-xs text-gray-500 mt-4 text-center">
        You can provide either UPI ID or Bank Details (or both). This information will be used for INR payments.
      </p>
    </div>
  )
}
