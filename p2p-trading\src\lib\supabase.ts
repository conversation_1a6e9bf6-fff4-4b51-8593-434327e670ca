import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// BSC Mainnet Configuration
export const BSC_CONFIG = {
  chainId: 56,
  name: 'BSC Mainnet',
  currency: 'BNB',
  rpcUrl: 'https://bsc-dataseed1.binance.org/',
  blockExplorer: 'https://bscscan.com'
}

// USDT Contract Address on BSC Mainnet
export const USDT_CONTRACT_ADDRESS = '******************************************'

// Database types
export interface User {
  wallet: string
  upi_id?: string
  bank_details?: string
  created_at: string
}

export interface Order {
  id: string
  type: 'buy' | 'sell'
  amount: number
  rate: number
  status: 'pending' | 'completed' | 'cancelled'
  user_wallet: string
  created_at: string
}

export interface Proof {
  id: string
  order_id: string
  buyer_wallet: string
  proof_url: string
  created_at: string
}

// Database helper functions
export const dbHelpers = {
  // Get user by wallet address
  async getUser(wallet: string) {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('wallet', wallet)
      .single()

    return { data, error }
  },

  // Create or update user
  async upsertUser(user: Omit<User, 'created_at'>) {
    const { data, error } = await supabase
      .from('users')
      .upsert(user)
      .select()
      .single()

    return { data, error }
  },

  // Get all sell orders (for buyers)
  async getSellOrders() {
    const { data, error } = await supabase
      .from('orders')
      .select(`
        *,
        users!orders_user_wallet_fkey(wallet, upi_id, bank_details)
      `)
      .eq('type', 'sell')
      .eq('status', 'pending')
      .order('created_at', { ascending: false })

    return { data, error }
  },

  // Create new order
  async createOrder(order: Omit<Order, 'id' | 'created_at'>) {
    const { data, error } = await supabase
      .from('orders')
      .insert(order)
      .select()
      .single()

    return { data, error }
  },

  // Get all orders (for admin)
  async getAllOrders() {
    const { data, error } = await supabase
      .from('orders')
      .select(`
        *,
        users!orders_user_wallet_fkey(wallet, upi_id, bank_details)
      `)
      .order('created_at', { ascending: false })

    return { data, error }
  },

  // Update order status
  async updateOrderStatus(orderId: string, status: 'pending' | 'completed' | 'cancelled') {
    const { data, error } = await supabase
      .from('orders')
      .update({ status })
      .eq('id', orderId)
      .select()
      .single()

    return { data, error }
  },

  // Get all proofs (for admin)
  async getAllProofs() {
    const { data, error } = await supabase
      .from('proofs')
      .select(`
        *,
        orders(
          *,
          users(wallet, upi_id, bank_details)
        )
      `)
      .order('created_at', { ascending: false })

    console.log('getAllProofs result:', { data, error })
    return { data, error }
  },

  // Get all users (for admin)
  async getAllUsers() {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .order('created_at', { ascending: false })

    return { data, error }
  },

  // Get seller's orders
  async getSellerOrders(sellerWallet: string) {
    const { data, error } = await supabase
      .from('orders')
      .select('*')
      .eq('user_wallet', sellerWallet)
      .order('created_at', { ascending: false })

    return { data, error }
  },

  // Get proofs for seller's orders
  async getProofsForSeller(sellerWallet: string) {
    const { data, error } = await supabase
      .from('proofs')
      .select(`
        *,
        orders!proofs_order_id_fkey(user_wallet)
      `)
      .eq('orders.user_wallet', sellerWallet)
      .order('created_at', { ascending: false })

    return { data, error }
  },

  // Confirm payment by seller
  async confirmPaymentBySeller(proofId: string) {
    const { data, error } = await supabase
      .from('proofs')
      .update({ confirmed_by_seller: true })
      .eq('id', proofId)
      .select()
      .single()

    return { data, error }
  },

  // Upload payment proof
  async uploadProof(file: File, orderId: string, buyerWallet: string) {
    const fileExt = file.name.split('.').pop()
    const fileName = `${orderId}_${Date.now()}.${fileExt}`

    // Upload file to storage
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('payment_proofs')
      .upload(fileName, file)

    if (uploadError) return { data: null, error: uploadError }

    // Get public URL
    const { data: { publicUrl } } = supabase.storage
      .from('payment_proofs')
      .getPublicUrl(fileName)

    // Save proof record to database
    const { data, error } = await supabase
      .from('proofs')
      .insert({
        order_id: orderId,
        buyer_wallet: buyerWallet,
        proof_url: publicUrl
      })
      .select()
      .single()

    return { data, error }
  }
}
