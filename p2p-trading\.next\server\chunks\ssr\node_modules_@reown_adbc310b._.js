module.exports = {

"[project]/node_modules/@reown/appkit/node_modules/@noble/curves/esm/secp256k1.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/7e465_@noble_5c9ecd64._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit/node_modules/@noble/curves/esm/secp256k1.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-controllers/node_modules/@noble/curves/esm/secp256k1.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/104f9_@noble_7e61c1e1._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-controllers/node_modules/@noble/curves/esm/secp256k1.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-scaffold-ui/dist/esm/exports/basic.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_ac68c5f9._.js",
  "server/chunks/ssr/node_modules_98dbd00b._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-scaffold-ui/dist/esm/exports/basic.js [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/node_modules/@reown/appkit-scaffold-ui/dist/esm/exports/w3m-modal.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@reown_appkit-ui_dist_esm_src_assets_svg_7bb9c0ee._.js",
  "server/chunks/ssr/node_modules_dd3fd160._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@reown/appkit-scaffold-ui/dist/esm/exports/w3m-modal.js [app-ssr] (ecmascript)");
    });
});
}}),

};