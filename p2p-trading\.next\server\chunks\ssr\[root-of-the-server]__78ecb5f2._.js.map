{"version": 3, "sources": [], "sections": [{"offset": {"line": 119, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n\n// BSC Mainnet Configuration\nexport const BSC_CONFIG = {\n  chainId: 56,\n  name: 'BSC Mainnet',\n  currency: 'BNB',\n  rpcUrl: 'https://bsc-dataseed1.binance.org/',\n  blockExplorer: 'https://bscscan.com'\n}\n\n// USDT Contract Address on BSC Mainnet\nexport const USDT_CONTRACT_ADDRESS = '******************************************'\n\n// Database types\nexport interface User {\n  wallet: string\n  upi_id?: string\n  bank_details?: string\n  created_at: string\n}\n\nexport interface Order {\n  id: string\n  type: 'buy' | 'sell'\n  amount: number\n  rate: number\n  status: 'pending' | 'completed' | 'cancelled'\n  user_wallet: string\n  created_at: string\n}\n\nexport interface Proof {\n  id: string\n  order_id: string\n  buyer_wallet: string\n  proof_url: string\n  created_at: string\n}\n\n// Database helper functions\nexport const dbHelpers = {\n  // Get user by wallet address\n  async getUser(wallet: string) {\n    const { data, error } = await supabase\n      .from('users')\n      .select('*')\n      .eq('wallet', wallet)\n      .single()\n\n    return { data, error }\n  },\n\n  // Create or update user\n  async upsertUser(user: Omit<User, 'created_at'>) {\n    const { data, error } = await supabase\n      .from('users')\n      .upsert(user)\n      .select()\n      .single()\n\n    return { data, error }\n  },\n\n  // Get all sell orders\n  async getSellOrders() {\n    const { data, error } = await supabase\n      .from('orders')\n      .select(`\n        *,\n        users!orders_user_wallet_fkey(wallet, upi_id, bank_details)\n      `)\n      .eq('type', 'sell')\n      .eq('status', 'pending')\n      .order('created_at', { ascending: false })\n\n    return { data, error }\n  },\n\n  // Create new order\n  async createOrder(order: Omit<Order, 'id' | 'created_at'>) {\n    const { data, error } = await supabase\n      .from('orders')\n      .insert(order)\n      .select()\n      .single()\n\n    return { data, error }\n  },\n\n  // Upload payment proof\n  async uploadProof(file: File, orderId: string, buyerWallet: string) {\n    const fileExt = file.name.split('.').pop()\n    const fileName = `${orderId}_${Date.now()}.${fileExt}`\n\n    // Upload file to storage\n    const { data: uploadData, error: uploadError } = await supabase.storage\n      .from('payment_proofs')\n      .upload(fileName, file)\n\n    if (uploadError) return { data: null, error: uploadError }\n\n    // Get public URL\n    const { data: { publicUrl } } = supabase.storage\n      .from('payment_proofs')\n      .getPublicUrl(fileName)\n\n    // Save proof record to database\n    const { data, error } = await supabase\n      .from('proofs')\n      .insert({\n        order_id: orderId,\n        buyer_wallet: buyerWallet,\n        proof_url: publicUrl\n      })\n      .select()\n      .single()\n\n    return { data, error }\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;AAEA,MAAM;AACN,MAAM;AAEC,MAAM,WAAW,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AAG3C,MAAM,aAAa;IACxB,SAAS;IACT,MAAM;IACN,UAAU;IACV,QAAQ;IACR,eAAe;AACjB;AAGO,MAAM,wBAAwB;AA6B9B,MAAM,YAAY;IACvB,6BAA6B;IAC7B,MAAM,SAAQ,MAAc;QAC1B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,UAAU,QACb,MAAM;QAET,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,wBAAwB;IACxB,MAAM,YAAW,IAA8B;QAC7C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,SACL,MAAM,CAAC,MACP,MAAM,GACN,MAAM;QAET,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,sBAAsB;IACtB,MAAM;QACJ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,UACL,MAAM,CAAC,CAAC;;;MAGT,CAAC,EACA,EAAE,CAAC,QAAQ,QACX,EAAE,CAAC,UAAU,WACb,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,mBAAmB;IACnB,MAAM,aAAY,KAAuC;QACvD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,UACL,MAAM,CAAC,OACP,MAAM,GACN,MAAM;QAET,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,uBAAuB;IACvB,MAAM,aAAY,IAAU,EAAE,OAAe,EAAE,WAAmB;QAChE,MAAM,UAAU,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG;QACxC,MAAM,WAAW,GAAG,QAAQ,CAAC,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,SAAS;QAEtD,yBAAyB;QACzB,MAAM,EAAE,MAAM,UAAU,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAAS,OAAO,CACpE,IAAI,CAAC,kBACL,MAAM,CAAC,UAAU;QAEpB,IAAI,aAAa,OAAO;YAAE,MAAM;YAAM,OAAO;QAAY;QAEzD,iBAAiB;QACjB,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,EAAE,GAAG,SAAS,OAAO,CAC7C,IAAI,CAAC,kBACL,YAAY,CAAC;QAEhB,gCAAgC;QAChC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,UACL,MAAM,CAAC;YACN,UAAU;YACV,cAAc;YACd,WAAW;QACb,GACC,MAAM,GACN,MAAM;QAET,OAAO;YAAE;YAAM;QAAM;IACvB;AACF", "debugId": null}}, {"offset": {"line": 206, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/src/components/UserDetailsForm.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { dbHelpers } from '@/lib/supabase'\n\ninterface UserDetailsFormProps {\n  walletAddress: string\n  onUserCreated: (user: any) => void\n}\n\nexport default function UserDetailsForm({ walletAddress, onUserCreated }: UserDetailsFormProps) {\n  const [formData, setFormData] = useState({\n    upi_id: '',\n    bank_details: ''\n  })\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    \n    if (!formData.upi_id && !formData.bank_details) {\n      setError('Please provide either UPI ID or Bank Details')\n      return\n    }\n\n    setLoading(true)\n    setError('')\n\n    try {\n      const userData = {\n        wallet: walletAddress,\n        upi_id: formData.upi_id || null,\n        bank_details: formData.bank_details || null\n      }\n\n      const { data, error } = await dbHelpers.upsertUser(userData)\n      \n      if (error) {\n        setError('Failed to save user details: ' + error.message)\n        return\n      }\n\n      onUserCreated(data)\n    } catch (error) {\n      setError('An unexpected error occurred')\n      console.error('Error saving user:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n    const { name, value } = e.target\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }))\n  }\n\n  return (\n    <div className=\"max-w-md mx-auto\">\n      <h2 className=\"text-2xl font-bold mb-6 text-center\">Complete Your Profile</h2>\n      <p className=\"text-gray-400 mb-6 text-center\">\n        Please provide your payment details to start trading\n      </p>\n\n      <form onSubmit={handleSubmit} className=\"space-y-6\">\n        <div>\n          <label htmlFor=\"upi_id\" className=\"block text-sm font-medium mb-2\">\n            UPI ID (Optional)\n          </label>\n          <input\n            type=\"text\"\n            id=\"upi_id\"\n            name=\"upi_id\"\n            value={formData.upi_id}\n            onChange={handleInputChange}\n            placeholder=\"your-upi@paytm\"\n            className=\"w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg focus:outline-none focus:border-blue-500\"\n          />\n        </div>\n\n        <div>\n          <label htmlFor=\"bank_details\" className=\"block text-sm font-medium mb-2\">\n            Bank Details (Optional)\n          </label>\n          <textarea\n            id=\"bank_details\"\n            name=\"bank_details\"\n            value={formData.bank_details}\n            onChange={handleInputChange}\n            placeholder=\"Bank Name: XYZ Bank&#10;Account Number: **********&#10;IFSC: ABCD0123456&#10;Account Holder: Your Name\"\n            rows={4}\n            className=\"w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg focus:outline-none focus:border-blue-500 resize-none\"\n          />\n        </div>\n\n        {error && (\n          <div className=\"text-red-500 text-sm text-center\">\n            {error}\n          </div>\n        )}\n\n        <button\n          type=\"submit\"\n          disabled={loading}\n          className=\"btn-primary w-full disabled:opacity-50 disabled:cursor-not-allowed\"\n        >\n          {loading ? 'Saving...' : 'Save Details'}\n        </button>\n      </form>\n\n      <p className=\"text-xs text-gray-500 mt-4 text-center\">\n        You can provide either UPI ID or Bank Details (or both). This information will be used for INR payments.\n      </p>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAUe,SAAS,gBAAgB,EAAE,aAAa,EAAE,aAAa,EAAwB;IAC5F,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,QAAQ;QACR,cAAc;IAChB;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,SAAS,MAAM,IAAI,CAAC,SAAS,YAAY,EAAE;YAC9C,SAAS;YACT;QACF;QAEA,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,WAAW;gBACf,QAAQ;gBACR,QAAQ,SAAS,MAAM,IAAI;gBAC3B,cAAc,SAAS,YAAY,IAAI;YACzC;YAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,YAAS,CAAC,UAAU,CAAC;YAEnD,IAAI,OAAO;gBACT,SAAS,kCAAkC,MAAM,OAAO;gBACxD;YACF;YAEA,cAAc;QAChB,EAAE,OAAO,OAAO;YACd,SAAS;YACT,QAAQ,KAAK,CAAC,sBAAsB;QACtC,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;IACH;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAAsC;;;;;;0BACpD,8OAAC;gBAAE,WAAU;0BAAiC;;;;;;0BAI9C,8OAAC;gBAAK,UAAU;gBAAc,WAAU;;kCACtC,8OAAC;;0CACC,8OAAC;gCAAM,SAAQ;gCAAS,WAAU;0CAAiC;;;;;;0CAGnE,8OAAC;gCACC,MAAK;gCACL,IAAG;gCACH,MAAK;gCACL,OAAO,SAAS,MAAM;gCACtB,UAAU;gCACV,aAAY;gCACZ,WAAU;;;;;;;;;;;;kCAId,8OAAC;;0CACC,8OAAC;gCAAM,SAAQ;gCAAe,WAAU;0CAAiC;;;;;;0CAGzE,8OAAC;gCACC,IAAG;gCACH,MAAK;gCACL,OAAO,SAAS,YAAY;gCAC5B,UAAU;gCACV,aAAY;gCACZ,MAAM;gCACN,WAAU;;;;;;;;;;;;oBAIb,uBACC,8OAAC;wBAAI,WAAU;kCACZ;;;;;;kCAIL,8OAAC;wBACC,MAAK;wBACL,UAAU;wBACV,WAAU;kCAET,UAAU,cAAc;;;;;;;;;;;;0BAI7B,8OAAC;gBAAE,WAAU;0BAAyC;;;;;;;;;;;;AAK5D", "debugId": null}}, {"offset": {"line": 385, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/src/components/WalletConnect.tsx"], "sourcesContent": ["'use client'\n\nimport { useAccount, useConnect, useDisconnect } from 'wagmi'\nimport { useEffect, useState } from 'react'\nimport { dbHelpers } from '@/lib/supabase'\nimport UserDetailsForm from './UserDetailsForm'\n\nexport default function WalletConnect() {\n  const { address, isConnected } = useAccount()\n  const { connect, connectors } = useConnect()\n  const { disconnect } = useDisconnect()\n  const [user, setUser] = useState<any>(null)\n  const [showUserForm, setShowUserForm] = useState(false)\n  const [loading, setLoading] = useState(false)\n  const [showWalletOptions, setShowWalletOptions] = useState(false)\n  const [mounted, setMounted] = useState(false)\n\n  // Fix hydration mismatch\n  useEffect(() => {\n    setMounted(true)\n  }, [])\n\n  // Check if user exists when wallet connects\n  useEffect(() => {\n    if (isConnected && address) {\n      checkUserExists()\n    }\n  }, [isConnected, address])\n\n  const checkUserExists = async () => {\n    if (!address) return\n    \n    setLoading(true)\n    try {\n      const { data, error } = await dbHelpers.getUser(address)\n      \n      if (error && error.code !== 'PGRST116') {\n        console.error('Error checking user:', error)\n        return\n      }\n      \n      if (data) {\n        // User exists\n        setUser(data)\n        setShowUserForm(false)\n      } else {\n        // User doesn't exist, show form\n        setShowUserForm(true)\n      }\n    } catch (error) {\n      console.error('Error:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleUserCreated = (userData: any) => {\n    setUser(userData)\n    setShowUserForm(false)\n  }\n\n  const handleDisconnect = () => {\n    disconnect()\n    setUser(null)\n    setShowUserForm(false)\n    setShowWalletOptions(false)\n  }\n\n  const handleConnectClick = () => {\n    setShowWalletOptions(true)\n  }\n\n  const handleWalletSelect = (connector: any) => {\n    connect({ connector })\n    setShowWalletOptions(false)\n  }\n\n  // Prevent hydration mismatch\n  if (!mounted) {\n    return (\n      <div className=\"flex items-center justify-center\">\n        <div className=\"text-lg\">Loading...</div>\n      </div>\n    )\n  }\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center\">\n        <div className=\"text-lg\">Loading...</div>\n      </div>\n    )\n  }\n\n  if (!isConnected) {\n    return (\n      <div className=\"text-center\">\n        <h2 className=\"text-2xl font-bold mb-6\">Connect Your Wallet</h2>\n        <p className=\"text-gray-400 mb-8\">\n          Connect your wallet to start trading USDT\n        </p>\n\n        {!showWalletOptions ? (\n          <button\n            onClick={handleConnectClick}\n            className=\"btn-primary text-lg px-8 py-4\"\n          >\n            Connect Wallet\n          </button>\n        ) : (\n          <div className=\"space-y-4 max-w-sm mx-auto\">\n            <h3 className=\"text-lg font-semibold mb-4\">Choose Your Wallet</h3>\n            {connectors.map((connector) => (\n              <button\n                key={connector.uid}\n                onClick={() => handleWalletSelect(connector)}\n                className=\"btn-primary w-full py-3 px-6 flex items-center justify-center gap-3\"\n                disabled={!connector.ready}\n              >\n                <span>🦊</span>\n                Connect {connector.name}\n              </button>\n            ))}\n            <button\n              onClick={() => setShowWalletOptions(false)}\n              className=\"text-gray-400 hover:text-white mt-4 w-full text-center\"\n            >\n              ← Back\n            </button>\n          </div>\n        )}\n      </div>\n    )\n  }\n\n  if (showUserForm) {\n    return (\n      <UserDetailsForm \n        walletAddress={address!}\n        onUserCreated={handleUserCreated}\n      />\n    )\n  }\n\n  return (\n    <div className=\"text-center\">\n      <div className=\"mb-6\">\n        <h2 className=\"text-2xl font-bold mb-2\">Wallet Connected</h2>\n        <p className=\"text-gray-400 text-sm\">\n          {address?.slice(0, 6)}...{address?.slice(-4)}\n        </p>\n      </div>\n      \n      {user && (\n        <div className=\"mb-6 p-4 bg-gray-800 rounded-lg\">\n          <h3 className=\"text-lg font-semibold mb-2\">Your Details</h3>\n          {user.upi_id && (\n            <p className=\"text-sm text-gray-300\">UPI: {user.upi_id}</p>\n          )}\n          {user.bank_details && (\n            <p className=\"text-sm text-gray-300\">Bank: {user.bank_details}</p>\n          )}\n        </div>\n      )}\n      \n      <button\n        onClick={handleDisconnect}\n        className=\"btn-primary bg-red-600 hover:bg-red-700\"\n      >\n        Disconnect Wallet\n      </button>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,2JAAA,CAAA,aAAU,AAAD;IAC1C,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,2JAAA,CAAA,aAAU,AAAD;IACzC,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,gBAAa,AAAD;IACnC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IACtC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,yBAAyB;IACzB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;IACb,GAAG,EAAE;IAEL,4CAA4C;IAC5C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,eAAe,SAAS;YAC1B;QACF;IACF,GAAG;QAAC;QAAa;KAAQ;IAEzB,MAAM,kBAAkB;QACtB,IAAI,CAAC,SAAS;QAEd,WAAW;QACX,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,YAAS,CAAC,OAAO,CAAC;YAEhD,IAAI,SAAS,MAAM,IAAI,KAAK,YAAY;gBACtC,QAAQ,KAAK,CAAC,wBAAwB;gBACtC;YACF;YAEA,IAAI,MAAM;gBACR,cAAc;gBACd,QAAQ;gBACR,gBAAgB;YAClB,OAAO;gBACL,gCAAgC;gBAChC,gBAAgB;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,UAAU;QAC1B,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,QAAQ;QACR,gBAAgB;IAClB;IAEA,MAAM,mBAAmB;QACvB;QACA,QAAQ;QACR,gBAAgB;QAChB,qBAAqB;IACvB;IAEA,MAAM,qBAAqB;QACzB,qBAAqB;IACvB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,QAAQ;YAAE;QAAU;QACpB,qBAAqB;IACvB;IAEA,6BAA6B;IAC7B,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BAAU;;;;;;;;;;;IAG/B;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BAAU;;;;;;;;;;;IAG/B;IAEA,IAAI,CAAC,aAAa;QAChB,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAA0B;;;;;;8BACxC,8OAAC;oBAAE,WAAU;8BAAqB;;;;;;gBAIjC,CAAC,kCACA,8OAAC;oBACC,SAAS;oBACT,WAAU;8BACX;;;;;yCAID,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA6B;;;;;;wBAC1C,WAAW,GAAG,CAAC,CAAC,0BACf,8OAAC;gCAEC,SAAS,IAAM,mBAAmB;gCAClC,WAAU;gCACV,UAAU,CAAC,UAAU,KAAK;;kDAE1B,8OAAC;kDAAK;;;;;;oCAAS;oCACN,UAAU,IAAI;;+BANlB,UAAU,GAAG;;;;;sCAStB,8OAAC;4BACC,SAAS,IAAM,qBAAqB;4BACpC,WAAU;sCACX;;;;;;;;;;;;;;;;;;IAOX;IAEA,IAAI,cAAc;QAChB,qBACE,8OAAC,qIAAA,CAAA,UAAe;YACd,eAAe;YACf,eAAe;;;;;;IAGrB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA0B;;;;;;kCACxC,8OAAC;wBAAE,WAAU;;4BACV,SAAS,MAAM,GAAG;4BAAG;4BAAI,SAAS,MAAM,CAAC;;;;;;;;;;;;;YAI7C,sBACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA6B;;;;;;oBAC1C,KAAK,MAAM,kBACV,8OAAC;wBAAE,WAAU;;4BAAwB;4BAAM,KAAK,MAAM;;;;;;;oBAEvD,KAAK,YAAY,kBAChB,8OAAC;wBAAE,WAAU;;4BAAwB;4BAAO,KAAK,YAAY;;;;;;;;;;;;;0BAKnE,8OAAC;gBACC,SAAS;gBACT,WAAU;0BACX;;;;;;;;;;;;AAKP", "debugId": null}}, {"offset": {"line": 684, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useAccount } from 'wagmi'\nimport WalletConnect from '@/components/WalletConnect'\nimport { useEffect, useState } from 'react'\n\nexport default function Home() {\n  const { isConnected } = useAccount()\n  const [mounted, setMounted] = useState(false)\n\n  useEffect(() => {\n    setMounted(true)\n  }, [])\n\n  if (!mounted) {\n    return (\n      <div className=\"min-h-screen flex flex-col items-center justify-center p-8\">\n        <div className=\"text-center max-w-4xl mx-auto\">\n          <h1 className=\"text-6xl font-bold mb-4\">\n            <span className=\"accent-primary\">Srd</span>\n            <span className=\"accent-secondary\">.Exchange</span>\n          </h1>\n          <p className=\"text-xl mb-8 text-gray-300\">\n            Secure P2P USDT Trading Platform\n          </p>\n          <p className=\"text-lg mb-12 text-gray-400\">\n            Buy and sell USDT with INR through our secure, admin-verified platform\n          </p>\n          <div className=\"text-lg\">Loading...</div>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen flex flex-col items-center justify-center p-8\">\n      {/* Hero Section */}\n      <div className=\"text-center max-w-4xl mx-auto\">\n        <h1 className=\"text-6xl font-bold mb-4\">\n          <span className=\"accent-primary\">Srd</span>\n          <span className=\"accent-secondary\">.Exchange</span>\n        </h1>\n        <p className=\"text-xl mb-8 text-gray-300\">\n          Secure P2P USDT Trading Platform\n        </p>\n        <p className=\"text-lg mb-12 text-gray-400\">\n          Buy and sell USDT with INR through our secure, admin-verified platform\n        </p>\n\n        {/* Wallet Connection Section */}\n        <div className=\"mb-12\">\n          <WalletConnect />\n        </div>\n\n        {/* Trading Section - Only show when wallet is connected */}\n        {isConnected && (\n          <div className=\"flex justify-center gap-4 mb-8\">\n            <button className=\"btn-primary px-6 py-3\">\n              Buy USDT\n            </button>\n            <button className=\"btn-primary px-6 py-3\">\n              Sell USDT\n            </button>\n          </div>\n        )}\n\n        {/* Features */}\n        <div className=\"grid md:grid-cols-3 gap-8 mt-16\">\n          <div className=\"p-6 border border-gray-700 rounded-lg\">\n            <h3 className=\"text-xl font-semibold mb-3 accent-primary\">Secure Trading</h3>\n            <p className=\"text-gray-400\">All transactions are verified by our admin team</p>\n          </div>\n          <div className=\"p-6 border border-gray-700 rounded-lg\">\n            <h3 className=\"text-xl font-semibold mb-3 accent-secondary\">Fast Processing</h3>\n            <p className=\"text-gray-400\">Quick verification and USDT release</p>\n          </div>\n          <div className=\"p-6 border border-gray-700 rounded-lg\">\n            <h3 className=\"text-xl font-semibold mb-3 accent-primary\">Wallet Integration</h3>\n            <p className=\"text-gray-400\">Connect with Metamask or Trust Wallet</p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,2JAAA,CAAA,aAAU,AAAD;IACjC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;IACb,GAAG,EAAE;IAEL,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC;gCAAK,WAAU;0CAAiB;;;;;;0CACjC,8OAAC;gCAAK,WAAU;0CAAmB;;;;;;;;;;;;kCAErC,8OAAC;wBAAE,WAAU;kCAA6B;;;;;;kCAG1C,8OAAC;wBAAE,WAAU;kCAA8B;;;;;;kCAG3C,8OAAC;wBAAI,WAAU;kCAAU;;;;;;;;;;;;;;;;;IAIjC;IAEA,qBACE,8OAAC;QAAI,WAAU;kBAEb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;;sCACZ,8OAAC;4BAAK,WAAU;sCAAiB;;;;;;sCACjC,8OAAC;4BAAK,WAAU;sCAAmB;;;;;;;;;;;;8BAErC,8OAAC;oBAAE,WAAU;8BAA6B;;;;;;8BAG1C,8OAAC;oBAAE,WAAU;8BAA8B;;;;;;8BAK3C,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,mIAAA,CAAA,UAAa;;;;;;;;;;gBAIf,6BACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAO,WAAU;sCAAwB;;;;;;sCAG1C,8OAAC;4BAAO,WAAU;sCAAwB;;;;;;;;;;;;8BAO9C,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA4C;;;;;;8CAC1D,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;sCAE/B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA8C;;;;;;8CAC5D,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;sCAE/B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA4C;;;;;;8CAC1D,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMzC", "debugId": null}}]}