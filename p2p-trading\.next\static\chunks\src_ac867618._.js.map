{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n\n// BSC Mainnet Configuration\nexport const BSC_CONFIG = {\n  chainId: 56,\n  name: 'BSC Mainnet',\n  currency: 'BNB',\n  rpcUrl: 'https://bsc-dataseed1.binance.org/',\n  blockExplorer: 'https://bscscan.com'\n}\n\n// USDT Contract Address on BSC Mainnet\nexport const USDT_CONTRACT_ADDRESS = '******************************************'\n\n// Database types\nexport interface User {\n  wallet: string\n  upi_id?: string\n  bank_details?: string\n  created_at: string\n}\n\nexport interface Order {\n  id: string\n  type: 'buy' | 'sell'\n  amount: number\n  rate: number\n  status: 'pending' | 'completed' | 'cancelled'\n  user_wallet: string\n  created_at: string\n}\n\nexport interface Proof {\n  id: string\n  order_id: string\n  buyer_wallet: string\n  proof_url: string\n  created_at: string\n}\n\n// Database helper functions\nexport const dbHelpers = {\n  // Get user by wallet address\n  async getUser(wallet: string) {\n    const { data, error } = await supabase\n      .from('users')\n      .select('*')\n      .eq('wallet', wallet)\n      .single()\n\n    return { data, error }\n  },\n\n  // Create or update user\n  async upsertUser(user: Omit<User, 'created_at'>) {\n    const { data, error } = await supabase\n      .from('users')\n      .upsert(user)\n      .select()\n      .single()\n\n    return { data, error }\n  },\n\n  // Get all sell orders (for buyers)\n  async getSellOrders() {\n    const { data, error } = await supabase\n      .from('orders')\n      .select(`\n        *,\n        users!orders_user_wallet_fkey(wallet, upi_id, bank_details)\n      `)\n      .eq('type', 'sell')\n      .eq('status', 'pending')\n      .order('created_at', { ascending: false })\n\n    return { data, error }\n  },\n\n  // Create new order\n  async createOrder(order: Omit<Order, 'id' | 'created_at'>) {\n    const { data, error } = await supabase\n      .from('orders')\n      .insert(order)\n      .select()\n      .single()\n\n    return { data, error }\n  },\n\n  // Get all orders (for admin)\n  async getAllOrders() {\n    const { data, error } = await supabase\n      .from('orders')\n      .select(`\n        *,\n        users!orders_user_wallet_fkey(wallet, upi_id, bank_details)\n      `)\n      .order('created_at', { ascending: false })\n\n    return { data, error }\n  },\n\n  // Update order status\n  async updateOrderStatus(orderId: string, status: 'pending' | 'completed' | 'cancelled') {\n    const { data, error } = await supabase\n      .from('orders')\n      .update({ status })\n      .eq('id', orderId)\n      .select()\n      .single()\n\n    return { data, error }\n  },\n\n  // Upload payment proof\n  async uploadProof(file: File, orderId: string, buyerWallet: string) {\n    const fileExt = file.name.split('.').pop()\n    const fileName = `${orderId}_${Date.now()}.${fileExt}`\n\n    // Upload file to storage\n    const { data: uploadData, error: uploadError } = await supabase.storage\n      .from('payment_proofs')\n      .upload(fileName, file)\n\n    if (uploadError) return { data: null, error: uploadError }\n\n    // Get public URL\n    const { data: { publicUrl } } = supabase.storage\n      .from('payment_proofs')\n      .getPublicUrl(fileName)\n\n    // Save proof record to database\n    const { data, error } = await supabase\n      .from('proofs')\n      .insert({\n        order_id: orderId,\n        buyer_wallet: buyerWallet,\n        proof_url: publicUrl\n      })\n      .select()\n      .single()\n\n    return { data, error }\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAEoB;AAFpB;;AAEA,MAAM;AACN,MAAM;AAEC,MAAM,WAAW,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AAG3C,MAAM,aAAa;IACxB,SAAS;IACT,MAAM;IACN,UAAU;IACV,QAAQ;IACR,eAAe;AACjB;AAGO,MAAM,wBAAwB;AA6B9B,MAAM,YAAY;IACvB,6BAA6B;IAC7B,MAAM,SAAQ,MAAc;QAC1B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,UAAU,QACb,MAAM;QAET,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,wBAAwB;IACxB,MAAM,YAAW,IAA8B;QAC7C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,SACL,MAAM,CAAC,MACP,MAAM,GACN,MAAM;QAET,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,mCAAmC;IACnC,MAAM;QACJ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,UACL,MAAM,CAAC,CAAC;;;MAGT,CAAC,EACA,EAAE,CAAC,QAAQ,QACX,EAAE,CAAC,UAAU,WACb,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,mBAAmB;IACnB,MAAM,aAAY,KAAuC;QACvD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,UACL,MAAM,CAAC,OACP,MAAM,GACN,MAAM;QAET,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,6BAA6B;IAC7B,MAAM;QACJ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,UACL,MAAM,CAAC,CAAC;;;MAGT,CAAC,EACA,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,sBAAsB;IACtB,MAAM,mBAAkB,OAAe,EAAE,MAA6C;QACpF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,UACL,MAAM,CAAC;YAAE;QAAO,GAChB,EAAE,CAAC,MAAM,SACT,MAAM,GACN,MAAM;QAET,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,uBAAuB;IACvB,MAAM,aAAY,IAAU,EAAE,OAAe,EAAE,WAAmB;QAChE,MAAM,UAAU,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG;QACxC,MAAM,WAAW,GAAG,QAAQ,CAAC,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,SAAS;QAEtD,yBAAyB;QACzB,MAAM,EAAE,MAAM,UAAU,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAAS,OAAO,CACpE,IAAI,CAAC,kBACL,MAAM,CAAC,UAAU;QAEpB,IAAI,aAAa,OAAO;YAAE,MAAM;YAAM,OAAO;QAAY;QAEzD,iBAAiB;QACjB,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,EAAE,GAAG,SAAS,OAAO,CAC7C,IAAI,CAAC,kBACL,YAAY,CAAC;QAEhB,gCAAgC;QAChC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,UACL,MAAM,CAAC;YACN,UAAU;YACV,cAAc;YACd,WAAW;QACb,GACC,MAAM,GACN,MAAM;QAET,OAAO;YAAE;YAAM;QAAM;IACvB;AACF", "debugId": null}}, {"offset": {"line": 121, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/src/components/UserDetailsForm.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { dbHelpers } from '@/lib/supabase'\n\ninterface UserDetailsFormProps {\n  walletAddress: string\n  onUserCreated: (user: any) => void\n}\n\nexport default function UserDetailsForm({ walletAddress, onUserCreated }: UserDetailsFormProps) {\n  const [formData, setFormData] = useState({\n    upi_id: '',\n    bank_details: ''\n  })\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n\n    if (!formData.upi_id && !formData.bank_details) {\n      setError('Please provide either UPI ID or Bank Details')\n      return\n    }\n\n    setLoading(true)\n    setError('')\n\n    try {\n      console.log('Saving user data:', {\n        wallet: walletAddress,\n        upi_id: formData.upi_id || null,\n        bank_details: formData.bank_details || null\n      })\n\n      const userData = {\n        wallet: walletAddress,\n        upi_id: formData.upi_id || null,\n        bank_details: formData.bank_details || null\n      }\n\n      const { data, error } = await dbHelpers.upsertUser(userData)\n\n      console.log('Supabase response:', { data, error })\n\n      if (error) {\n        console.error('Supabase error details:', error)\n        setError(`Failed to save user details: ${error.message}`)\n        return\n      }\n\n      console.log('User created successfully:', data)\n      onUserCreated(data)\n    } catch (error) {\n      console.error('Unexpected error saving user:', error)\n      setError('An unexpected error occurred. Please try again.')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n    const { name, value } = e.target\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }))\n  }\n\n  return (\n    <div className=\"max-w-md mx-auto\">\n      <div className=\"mb-6 p-4 bg-blue-900/20 border border-blue-500/30 rounded-lg\">\n        <h2 className=\"text-2xl font-bold mb-2 text-center text-blue-400\">Complete Your Profile</h2>\n        <p className=\"text-gray-300 text-center text-sm\">\n          Wallet: {walletAddress?.slice(0, 8)}...{walletAddress?.slice(-6)}\n        </p>\n      </div>\n      <p className=\"text-gray-400 mb-6 text-center\">\n        Please provide your payment details to start trading\n      </p>\n\n      <form onSubmit={handleSubmit} className=\"space-y-6\">\n        <div>\n          <label htmlFor=\"upi_id\" className=\"block text-sm font-medium mb-2\">\n            UPI ID (Optional)\n          </label>\n          <input\n            type=\"text\"\n            id=\"upi_id\"\n            name=\"upi_id\"\n            value={formData.upi_id}\n            onChange={handleInputChange}\n            placeholder=\"your-upi@paytm\"\n            className=\"w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg focus:outline-none focus:border-blue-500\"\n          />\n        </div>\n\n        <div>\n          <label htmlFor=\"bank_details\" className=\"block text-sm font-medium mb-2\">\n            Bank Details (Optional)\n          </label>\n          <textarea\n            id=\"bank_details\"\n            name=\"bank_details\"\n            value={formData.bank_details}\n            onChange={handleInputChange}\n            placeholder=\"Bank Name: XYZ Bank&#10;Account Number: **********&#10;IFSC: ABCD0123456&#10;Account Holder: Your Name\"\n            rows={4}\n            className=\"w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg focus:outline-none focus:border-blue-500 resize-none\"\n          />\n        </div>\n\n        {error && (\n          <div className=\"text-red-500 text-sm text-center\">\n            {error}\n          </div>\n        )}\n\n        <button\n          type=\"submit\"\n          disabled={loading}\n          className=\"btn-primary w-full disabled:opacity-50 disabled:cursor-not-allowed\"\n        >\n          {loading ? 'Saving...' : 'Save Details'}\n        </button>\n      </form>\n\n      <p className=\"text-xs text-gray-500 mt-4 text-center\">\n        You can provide either UPI ID or Bank Details (or both). This information will be used for INR payments.\n      </p>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAUe,SAAS,gBAAgB,EAAE,aAAa,EAAE,aAAa,EAAwB;;IAC5F,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,QAAQ;QACR,cAAc;IAChB;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,SAAS,MAAM,IAAI,CAAC,SAAS,YAAY,EAAE;YAC9C,SAAS;YACT;QACF;QAEA,WAAW;QACX,SAAS;QAET,IAAI;YACF,QAAQ,GAAG,CAAC,qBAAqB;gBAC/B,QAAQ;gBACR,QAAQ,SAAS,MAAM,IAAI;gBAC3B,cAAc,SAAS,YAAY,IAAI;YACzC;YAEA,MAAM,WAAW;gBACf,QAAQ;gBACR,QAAQ,SAAS,MAAM,IAAI;gBAC3B,cAAc,SAAS,YAAY,IAAI;YACzC;YAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,YAAS,CAAC,UAAU,CAAC;YAEnD,QAAQ,GAAG,CAAC,sBAAsB;gBAAE;gBAAM;YAAM;YAEhD,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,2BAA2B;gBACzC,SAAS,CAAC,6BAA6B,EAAE,MAAM,OAAO,EAAE;gBACxD;YACF;YAEA,QAAQ,GAAG,CAAC,8BAA8B;YAC1C,cAAc;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;IACH;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAoD;;;;;;kCAClE,6LAAC;wBAAE,WAAU;;4BAAoC;4BACtC,eAAe,MAAM,GAAG;4BAAG;4BAAI,eAAe,MAAM,CAAC;;;;;;;;;;;;;0BAGlE,6LAAC;gBAAE,WAAU;0BAAiC;;;;;;0BAI9C,6LAAC;gBAAK,UAAU;gBAAc,WAAU;;kCACtC,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAS,WAAU;0CAAiC;;;;;;0CAGnE,6LAAC;gCACC,MAAK;gCACL,IAAG;gCACH,MAAK;gCACL,OAAO,SAAS,MAAM;gCACtB,UAAU;gCACV,aAAY;gCACZ,WAAU;;;;;;;;;;;;kCAId,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAe,WAAU;0CAAiC;;;;;;0CAGzE,6LAAC;gCACC,IAAG;gCACH,MAAK;gCACL,OAAO,SAAS,YAAY;gCAC5B,UAAU;gCACV,aAAY;gCACZ,MAAM;gCACN,WAAU;;;;;;;;;;;;oBAIb,uBACC,6LAAC;wBAAI,WAAU;kCACZ;;;;;;kCAIL,6LAAC;wBACC,MAAK;wBACL,UAAU;wBACV,WAAU;kCAET,UAAU,cAAc;;;;;;;;;;;;0BAI7B,6LAAC;gBAAE,WAAU;0BAAyC;;;;;;;;;;;;AAK5D;GA3HwB;KAAA", "debugId": null}}, {"offset": {"line": 342, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/src/components/WalletConnect.tsx"], "sourcesContent": ["'use client'\n\nimport { useAccount, useConnect, useDisconnect } from 'wagmi'\nimport { useEffect, useState } from 'react'\nimport { dbHelpers } from '@/lib/supabase'\nimport UserDetailsForm from './UserDetailsForm'\n\nexport default function WalletConnect() {\n  const { address, isConnected } = useAccount()\n  const { connect, connectors, error, isPending } = useConnect()\n  const { disconnect } = useDisconnect()\n  const [user, setUser] = useState<any>(null)\n  const [showUserForm, setShowUserForm] = useState(false)\n  const [loading, setLoading] = useState(false)\n  const [showWalletOptions, setShowWalletOptions] = useState(false)\n  const [mounted, setMounted] = useState(false)\n  const [connectionError, setConnectionError] = useState<string | null>(null)\n\n  // Fix hydration mismatch\n  useEffect(() => {\n    setMounted(true)\n    console.log('WalletConnect mounted')\n    console.log('Available connectors:', connectors)\n  }, [])\n\n  // Debug connection state changes\n  useEffect(() => {\n    console.log('Connection state changed:', { isConnected, address })\n  }, [isConnected, address])\n\n  // Debug errors\n  useEffect(() => {\n    if (error) {\n      console.error('Wagmi connection error:', error)\n    }\n  }, [error])\n\n  // Check if user exists when wallet connects\n  useEffect(() => {\n    if (isConnected && address) {\n      checkUserExists()\n    }\n  }, [isConnected, address])\n\n  const checkUserExists = async () => {\n    if (!address) return\n    \n    setLoading(true)\n    try {\n      const { data, error } = await dbHelpers.getUser(address)\n      \n      if (error && error.code !== 'PGRST116') {\n        console.error('Error checking user:', error)\n        return\n      }\n      \n      if (data) {\n        // User exists\n        setUser(data)\n        setShowUserForm(false)\n      } else {\n        // User doesn't exist, show form\n        setShowUserForm(true)\n      }\n    } catch (error) {\n      console.error('Error:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleUserCreated = (userData: any) => {\n    setUser(userData)\n    setShowUserForm(false)\n  }\n\n  const handleDisconnect = () => {\n    disconnect()\n    setUser(null)\n    setShowUserForm(false)\n    setShowWalletOptions(false)\n  }\n\n  const handleConnectClick = () => {\n    setShowWalletOptions(true)\n  }\n\n\n\n  const handleWalletSelect = async (walletType: string) => {\n    try {\n      setConnectionError(null)\n      console.log('Attempting to connect with:', walletType)\n\n      if (walletType === 'MetaMask') {\n        await connectMetaMask()\n      } else if (walletType === 'TrustWallet') {\n        await connectTrustWallet()\n      } else {\n        // Fallback to wagmi connector\n        const connector = connectors.find(c => c.name.toLowerCase().includes(walletType.toLowerCase()))\n        if (connector) {\n          await connect({ connector })\n        }\n      }\n\n      setShowWalletOptions(false)\n    } catch (err) {\n      console.error('Connection error:', err)\n      setConnectionError(`Failed to connect: ${err instanceof Error ? err.message : 'Unknown error'}`)\n    }\n  }\n\n  const connectMetaMask = async () => {\n    if (typeof window !== 'undefined' && window.ethereum) {\n      try {\n        // Request account access\n        const accounts = await window.ethereum.request({\n          method: 'eth_requestAccounts'\n        })\n\n        // Switch to BSC network\n        try {\n          await window.ethereum.request({\n            method: 'wallet_switchEthereumChain',\n            params: [{ chainId: '0x38' }], // BSC Mainnet\n          })\n        } catch (switchError: any) {\n          // If BSC is not added, add it\n          if (switchError.code === 4902) {\n            await window.ethereum.request({\n              method: 'wallet_addEthereumChain',\n              params: [{\n                chainId: '0x38',\n                chainName: 'BSC Mainnet',\n                nativeCurrency: {\n                  name: 'BNB',\n                  symbol: 'BNB',\n                  decimals: 18,\n                },\n                rpcUrls: ['https://bsc-dataseed1.binance.org/'],\n                blockExplorerUrls: ['https://bscscan.com/'],\n              }],\n            })\n          }\n        }\n\n        console.log('MetaMask connected:', accounts[0])\n        // Force wagmi to recognize the connection\n        window.location.reload()\n      } catch (error) {\n        throw new Error('Failed to connect to MetaMask')\n      }\n    } else {\n      throw new Error('MetaMask is not installed. Please install MetaMask extension.')\n    }\n  }\n\n  const connectTrustWallet = async () => {\n    if (typeof window !== 'undefined' && window.ethereum) {\n      // Trust Wallet also uses window.ethereum\n      await connectMetaMask()\n    } else {\n      throw new Error('Trust Wallet is not detected. Please open this page in Trust Wallet browser.')\n    }\n  }\n\n  // Prevent hydration mismatch\n  if (!mounted) {\n    return (\n      <div className=\"flex items-center justify-center\">\n        <div className=\"text-lg\">Loading...</div>\n      </div>\n    )\n  }\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center\">\n        <div className=\"text-lg\">Loading...</div>\n      </div>\n    )\n  }\n\n  if (!isConnected) {\n    return (\n      <div className=\"text-center\">\n        <h2 className=\"text-2xl font-bold mb-6\">Connect Your Wallet</h2>\n        <p className=\"text-gray-400 mb-8\">\n          Connect your wallet to start trading USDT\n        </p>\n\n        {!showWalletOptions ? (\n          <button\n            onClick={handleConnectClick}\n            className=\"btn-primary text-lg px-8 py-4\"\n          >\n            Connect Wallet\n          </button>\n        ) : (\n          <div className=\"space-y-4 max-w-sm mx-auto\">\n            <h3 className=\"text-lg font-semibold mb-4\">Choose Your Wallet</h3>\n\n            {/* MetaMask */}\n            <button\n              onClick={() => handleWalletSelect('MetaMask')}\n              className=\"btn-primary w-full py-3 px-6 flex items-center justify-center gap-3 disabled:opacity-50\"\n              disabled={isPending}\n            >\n              <span>🦊</span>\n              {isPending ? 'Connecting...' : 'Connect MetaMask'}\n            </button>\n\n            {/* Trust Wallet */}\n            <button\n              onClick={() => handleWalletSelect('TrustWallet')}\n              className=\"btn-primary w-full py-3 px-6 flex items-center justify-center gap-3 disabled:opacity-50\"\n              disabled={isPending}\n            >\n              <span>🛡️</span>\n              {isPending ? 'Connecting...' : 'Connect Trust Wallet'}\n            </button>\n\n            {/* Other Injected Wallets */}\n            <button\n              onClick={() => handleWalletSelect('Injected')}\n              className=\"btn-primary w-full py-3 px-6 flex items-center justify-center gap-3 disabled:opacity-50\"\n              disabled={isPending}\n            >\n              <span>💼</span>\n              {isPending ? 'Connecting...' : 'Connect Other Wallet'}\n            </button>\n\n            {/* Error display */}\n            {(error || connectionError) && (\n              <div className=\"text-red-500 text-sm mt-2 p-2 bg-red-900/20 rounded\">\n                {connectionError || error?.message}\n              </div>\n            )}\n\n            <button\n              onClick={() => setShowWalletOptions(false)}\n              className=\"text-gray-400 hover:text-white mt-4 w-full text-center\"\n            >\n              ← Back\n            </button>\n          </div>\n        )}\n      </div>\n    )\n  }\n\n  if (showUserForm) {\n    return (\n      <UserDetailsForm\n        walletAddress={address!}\n        onUserCreated={handleUserCreated}\n      />\n    )\n  }\n\n  return (\n    <div className=\"text-center\">\n      <div className=\"mb-6 p-4 bg-green-900/20 border border-green-500/30 rounded-lg\">\n        <h2 className=\"text-2xl font-bold mb-2 text-green-400\">✅ Wallet Connected</h2>\n        <p className=\"text-gray-300 text-sm font-mono\">\n          {address?.slice(0, 8)}...{address?.slice(-6)}\n        </p>\n        <p className=\"text-xs text-gray-500 mt-1\">BSC Mainnet</p>\n      </div>\n\n      {user && (\n        <div className=\"mb-6 p-4 bg-blue-900/20 border border-blue-500/30 rounded-lg\">\n          <h3 className=\"text-lg font-semibold mb-2 text-blue-400\">Your Payment Details</h3>\n          {user.upi_id && (\n            <p className=\"text-sm text-gray-300\">UPI: {user.upi_id}</p>\n          )}\n          {user.bank_details && (\n            <p className=\"text-sm text-gray-300\">Bank: {user.bank_details}</p>\n          )}\n        </div>\n      )}\n\n      <div className=\"space-y-3\">\n        <button\n          onClick={handleDisconnect}\n          className=\"btn-primary bg-red-600 hover:bg-red-700\"\n        >\n          Disconnect Wallet\n        </button>\n\n        {user && (\n          <p className=\"text-xs text-gray-500\">\n            Ready to trade! Use the Buy/Sell buttons below.\n          </p>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AACA;AACA;AACA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,aAAU,AAAD;IAC1C,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,aAAU,AAAD;IAC3D,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,gBAAa,AAAD;IACnC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IACtC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAEtE,yBAAyB;IACzB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,WAAW;YACX,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,yBAAyB;QACvC;kCAAG,EAAE;IAEL,iCAAiC;IACjC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,QAAQ,GAAG,CAAC,6BAA6B;gBAAE;gBAAa;YAAQ;QAClE;kCAAG;QAAC;QAAa;KAAQ;IAEzB,eAAe;IACf,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,2BAA2B;YAC3C;QACF;kCAAG;QAAC;KAAM;IAEV,4CAA4C;IAC5C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,eAAe,SAAS;gBAC1B;YACF;QACF;kCAAG;QAAC;QAAa;KAAQ;IAEzB,MAAM,kBAAkB;QACtB,IAAI,CAAC,SAAS;QAEd,WAAW;QACX,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,YAAS,CAAC,OAAO,CAAC;YAEhD,IAAI,SAAS,MAAM,IAAI,KAAK,YAAY;gBACtC,QAAQ,KAAK,CAAC,wBAAwB;gBACtC;YACF;YAEA,IAAI,MAAM;gBACR,cAAc;gBACd,QAAQ;gBACR,gBAAgB;YAClB,OAAO;gBACL,gCAAgC;gBAChC,gBAAgB;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,UAAU;QAC1B,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,QAAQ;QACR,gBAAgB;IAClB;IAEA,MAAM,mBAAmB;QACvB;QACA,QAAQ;QACR,gBAAgB;QAChB,qBAAqB;IACvB;IAEA,MAAM,qBAAqB;QACzB,qBAAqB;IACvB;IAIA,MAAM,qBAAqB,OAAO;QAChC,IAAI;YACF,mBAAmB;YACnB,QAAQ,GAAG,CAAC,+BAA+B;YAE3C,IAAI,eAAe,YAAY;gBAC7B,MAAM;YACR,OAAO,IAAI,eAAe,eAAe;gBACvC,MAAM;YACR,OAAO;gBACL,8BAA8B;gBAC9B,MAAM,YAAY,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;gBAC3F,IAAI,WAAW;oBACb,MAAM,QAAQ;wBAAE;oBAAU;gBAC5B;YACF;YAEA,qBAAqB;QACvB,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,qBAAqB;YACnC,mBAAmB,CAAC,mBAAmB,EAAE,eAAe,QAAQ,IAAI,OAAO,GAAG,iBAAiB;QACjG;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI,aAAkB,eAAe,OAAO,QAAQ,EAAE;YACpD,IAAI;gBACF,yBAAyB;gBACzB,MAAM,WAAW,MAAM,OAAO,QAAQ,CAAC,OAAO,CAAC;oBAC7C,QAAQ;gBACV;gBAEA,wBAAwB;gBACxB,IAAI;oBACF,MAAM,OAAO,QAAQ,CAAC,OAAO,CAAC;wBAC5B,QAAQ;wBACR,QAAQ;4BAAC;gCAAE,SAAS;4BAAO;yBAAE;oBAC/B;gBACF,EAAE,OAAO,aAAkB;oBACzB,8BAA8B;oBAC9B,IAAI,YAAY,IAAI,KAAK,MAAM;wBAC7B,MAAM,OAAO,QAAQ,CAAC,OAAO,CAAC;4BAC5B,QAAQ;4BACR,QAAQ;gCAAC;oCACP,SAAS;oCACT,WAAW;oCACX,gBAAgB;wCACd,MAAM;wCACN,QAAQ;wCACR,UAAU;oCACZ;oCACA,SAAS;wCAAC;qCAAqC;oCAC/C,mBAAmB;wCAAC;qCAAuB;gCAC7C;6BAAE;wBACJ;oBACF;gBACF;gBAEA,QAAQ,GAAG,CAAC,uBAAuB,QAAQ,CAAC,EAAE;gBAC9C,0CAA0C;gBAC1C,OAAO,QAAQ,CAAC,MAAM;YACxB,EAAE,OAAO,OAAO;gBACd,MAAM,IAAI,MAAM;YAClB;QACF,OAAO;YACL,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI,aAAkB,eAAe,OAAO,QAAQ,EAAE;YACpD,yCAAyC;YACzC,MAAM;QACR,OAAO;YACL,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,6BAA6B;IAC7B,IAAI,CAAC,SAAS;QACZ,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BAAU;;;;;;;;;;;IAG/B;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BAAU;;;;;;;;;;;IAG/B;IAEA,IAAI,CAAC,aAAa;QAChB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAA0B;;;;;;8BACxC,6LAAC;oBAAE,WAAU;8BAAqB;;;;;;gBAIjC,CAAC,kCACA,6LAAC;oBACC,SAAS;oBACT,WAAU;8BACX;;;;;yCAID,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA6B;;;;;;sCAG3C,6LAAC;4BACC,SAAS,IAAM,mBAAmB;4BAClC,WAAU;4BACV,UAAU;;8CAEV,6LAAC;8CAAK;;;;;;gCACL,YAAY,kBAAkB;;;;;;;sCAIjC,6LAAC;4BACC,SAAS,IAAM,mBAAmB;4BAClC,WAAU;4BACV,UAAU;;8CAEV,6LAAC;8CAAK;;;;;;gCACL,YAAY,kBAAkB;;;;;;;sCAIjC,6LAAC;4BACC,SAAS,IAAM,mBAAmB;4BAClC,WAAU;4BACV,UAAU;;8CAEV,6LAAC;8CAAK;;;;;;gCACL,YAAY,kBAAkB;;;;;;;wBAIhC,CAAC,SAAS,eAAe,mBACxB,6LAAC;4BAAI,WAAU;sCACZ,mBAAmB,OAAO;;;;;;sCAI/B,6LAAC;4BACC,SAAS,IAAM,qBAAqB;4BACpC,WAAU;sCACX;;;;;;;;;;;;;;;;;;IAOX;IAEA,IAAI,cAAc;QAChB,qBACE,6LAAC,wIAAA,CAAA,UAAe;YACd,eAAe;YACf,eAAe;;;;;;IAGrB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAE,WAAU;;4BACV,SAAS,MAAM,GAAG;4BAAG;4BAAI,SAAS,MAAM,CAAC;;;;;;;kCAE5C,6LAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;YAG3C,sBACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;oBACxD,KAAK,MAAM,kBACV,6LAAC;wBAAE,WAAU;;4BAAwB;4BAAM,KAAK,MAAM;;;;;;;oBAEvD,KAAK,YAAY,kBAChB,6LAAC;wBAAE,WAAU;;4BAAwB;4BAAO,KAAK,YAAY;;;;;;;;;;;;;0BAKnE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;oBAIA,sBACC,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;;AAO/C;GApSwB;;QACW,8JAAA,CAAA,aAAU;QACO,8JAAA,CAAA,aAAU;QACrC,iKAAA,CAAA,gBAAa;;;KAHd", "debugId": null}}, {"offset": {"line": 832, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/src/components/SellUSDTForm.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useAccount } from 'wagmi'\nimport { dbHelpers } from '@/lib/supabase'\n\ninterface SellUSDTFormProps {\n  onClose: () => void\n  onSuccess: () => void\n}\n\nexport default function SellUSDTForm({ onClose, onSuccess }: SellUSDTFormProps) {\n  const { address } = useAccount()\n  const [formData, setFormData] = useState({\n    amount: '',\n    rate: '84.50' // Default INR rate per USDT\n  })\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n  const [step, setStep] = useState<'form' | 'transfer' | 'success'>('form')\n\n  const minAmount = 11\n  const totalINR = parseFloat(formData.amount || '0') * parseFloat(formData.rate)\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const { name, value } = e.target\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }))\n  }\n\n  const validateForm = () => {\n    const amount = parseFloat(formData.amount)\n    if (!amount || amount < minAmount) {\n      setError(`Minimum amount is ${minAmount} USDT`)\n      return false\n    }\n    if (!formData.rate || parseFloat(formData.rate) <= 0) {\n      setError('Please enter a valid rate')\n      return false\n    }\n    return true\n  }\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    \n    if (!validateForm()) return\n    if (!address) {\n      setError('Wallet not connected')\n      return\n    }\n\n    setLoading(true)\n    setError('')\n\n    try {\n      // Step 1: Transfer USDT to admin wallet\n      setStep('transfer')\n      await transferUSDTToAdmin()\n\n      // Step 2: Save order to database\n      await saveOrderToDatabase()\n\n      // Step 3: Success\n      setStep('success')\n      setTimeout(() => {\n        onSuccess()\n        onClose()\n      }, 3000)\n\n    } catch (err) {\n      console.error('Sell USDT error:', err)\n      setError(err instanceof Error ? err.message : 'Failed to process sell order')\n      setStep('form')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const transferUSDTToAdmin = async () => {\n    if (typeof window === 'undefined' || !window.ethereum) {\n      throw new Error('MetaMask not found')\n    }\n\n    const adminWallet = process.env.NEXT_PUBLIC_ADMIN_WALLET_ADDRESS\n    if (!adminWallet) {\n      throw new Error('Admin wallet not configured')\n    }\n\n    // USDT contract address on BSC\n    const usdtContract = '******************************************'\n    const amount = parseFloat(formData.amount)\n\n    // Convert amount to wei (USDT has 18 decimals)\n    const amountBigInt = BigInt(Math.floor(amount * 1e18))\n    const amountHex = amountBigInt.toString(16).padStart(64, '0')\n\n    // Clean admin wallet address (remove 0x and pad to 64 chars)\n    const adminAddressHex = adminWallet.slice(2).toLowerCase().padStart(64, '0')\n\n    // ERC20 transfer function signature: transfer(address,uint256)\n    const transferData = `0xa9059cbb${adminAddressHex}${amountHex}`\n\n    const txParams = {\n      from: address,\n      to: usdtContract,\n      data: transferData,\n      gas: '0x15F90', // 90000 gas for ERC20 transfer\n    }\n\n    console.log('Transfer params:', {\n      amount: formData.amount,\n      amountWei: amountBigInt.toString(),\n      adminWallet,\n      transferData\n    })\n\n    const txHash = await window.ethereum.request({\n      method: 'eth_sendTransaction',\n      params: [txParams],\n    })\n\n    console.log('USDT transfer transaction:', txHash)\n    return txHash\n  }\n\n  const saveOrderToDatabase = async () => {\n    const orderData = {\n      type: 'sell' as const,\n      amount: parseFloat(formData.amount),\n      rate: parseFloat(formData.rate),\n      status: 'pending' as const,\n      user_wallet: address!\n    }\n\n    const { data, error } = await dbHelpers.createOrder(orderData)\n    \n    if (error) {\n      throw new Error(`Failed to save order: ${error.message}`)\n    }\n\n    console.log('Order saved:', data)\n    return data\n  }\n\n  if (step === 'transfer') {\n    return (\n      <div className=\"max-w-md mx-auto text-center\">\n        <div className=\"mb-6 p-6 bg-yellow-900/20 border border-yellow-500/30 rounded-lg\">\n          <h2 className=\"text-2xl font-bold mb-4 text-yellow-400\">Processing Transfer</h2>\n          <div className=\"animate-spin w-8 h-8 border-4 border-yellow-400 border-t-transparent rounded-full mx-auto mb-4\"></div>\n          <p className=\"text-gray-300 mb-2\">Transferring {formData.amount} USDT to admin wallet...</p>\n          <p className=\"text-sm text-gray-500\">Please confirm the transaction in MetaMask</p>\n        </div>\n      </div>\n    )\n  }\n\n  if (step === 'success') {\n    return (\n      <div className=\"max-w-md mx-auto text-center\">\n        <div className=\"mb-6 p-6 bg-green-900/20 border border-green-500/30 rounded-lg\">\n          <h2 className=\"text-2xl font-bold mb-4 text-green-400\">✅ Sell Order Created!</h2>\n          <p className=\"text-gray-300 mb-2\">Your sell order for {formData.amount} USDT has been created successfully.</p>\n          <p className=\"text-sm text-gray-500\">You will receive ₹{totalINR.toFixed(2)} when a buyer completes the purchase.</p>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"max-w-md mx-auto\">\n      <div className=\"mb-6 p-4 bg-red-900/20 border border-red-500/30 rounded-lg\">\n        <h2 className=\"text-2xl font-bold mb-2 text-center text-red-400\">Sell USDT</h2>\n        <p className=\"text-gray-300 text-center text-sm\">\n          Convert your USDT to INR\n        </p>\n      </div>\n\n      <form onSubmit={handleSubmit} className=\"space-y-6\">\n        <div>\n          <label htmlFor=\"amount\" className=\"block text-sm font-medium mb-2\">\n            USDT Amount (Minimum: {minAmount} USDT)\n          </label>\n          <input\n            type=\"number\"\n            id=\"amount\"\n            name=\"amount\"\n            value={formData.amount}\n            onChange={handleInputChange}\n            placeholder=\"Enter USDT amount\"\n            min={minAmount}\n            step=\"0.01\"\n            className=\"w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg focus:outline-none focus:border-blue-500\"\n            required\n          />\n        </div>\n\n        <div>\n          <label htmlFor=\"rate\" className=\"block text-sm font-medium mb-2\">\n            Rate (INR per USDT)\n          </label>\n          <input\n            type=\"number\"\n            id=\"rate\"\n            name=\"rate\"\n            value={formData.rate}\n            onChange={handleInputChange}\n            placeholder=\"Enter rate\"\n            step=\"0.01\"\n            className=\"w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg focus:outline-none focus:border-blue-500\"\n            required\n          />\n        </div>\n\n        {formData.amount && formData.rate && (\n          <div className=\"p-4 bg-blue-900/20 border border-blue-500/30 rounded-lg\">\n            <h3 className=\"text-lg font-semibold mb-2 text-blue-400\">Order Summary</h3>\n            <div className=\"space-y-1 text-sm\">\n              <div className=\"flex justify-between\">\n                <span>USDT Amount:</span>\n                <span>{formData.amount} USDT</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span>Rate:</span>\n                <span>₹{formData.rate} per USDT</span>\n              </div>\n              <div className=\"flex justify-between font-semibold text-blue-400\">\n                <span>Total INR:</span>\n                <span>₹{totalINR.toFixed(2)}</span>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {error && (\n          <div className=\"text-red-500 text-sm p-2 bg-red-900/20 rounded\">\n            {error}\n          </div>\n        )}\n\n        <div className=\"flex gap-4\">\n          <button\n            type=\"button\"\n            onClick={onClose}\n            className=\"flex-1 px-6 py-3 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-800\"\n          >\n            Cancel\n          </button>\n          <button\n            type=\"submit\"\n            disabled={loading}\n            className=\"flex-1 btn-primary disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            {loading ? 'Processing...' : 'Create Sell Order'}\n          </button>\n        </div>\n      </form>\n\n      <p className=\"text-xs text-gray-500 mt-4 text-center\">\n        Your USDT will be transferred to our admin wallet and held securely until a buyer completes the purchase.\n      </p>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;AAsFwB;;AApFxB;AACA;AACA;;;AAJA;;;;AAWe,SAAS,aAAa,EAAE,OAAO,EAAE,SAAS,EAAqB;;IAC5E,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,aAAU,AAAD;IAC7B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,QAAQ;QACR,MAAM,QAAQ,4BAA4B;IAC5C;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmC;IAElE,MAAM,YAAY;IAClB,MAAM,WAAW,WAAW,SAAS,MAAM,IAAI,OAAO,WAAW,SAAS,IAAI;IAE9E,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;IACH;IAEA,MAAM,eAAe;QACnB,MAAM,SAAS,WAAW,SAAS,MAAM;QACzC,IAAI,CAAC,UAAU,SAAS,WAAW;YACjC,SAAS,CAAC,kBAAkB,EAAE,UAAU,KAAK,CAAC;YAC9C,OAAO;QACT;QACA,IAAI,CAAC,SAAS,IAAI,IAAI,WAAW,SAAS,IAAI,KAAK,GAAG;YACpD,SAAS;YACT,OAAO;QACT;QACA,OAAO;IACT;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;QACrB,IAAI,CAAC,SAAS;YACZ,SAAS;YACT;QACF;QAEA,WAAW;QACX,SAAS;QAET,IAAI;YACF,wCAAwC;YACxC,QAAQ;YACR,MAAM;YAEN,iCAAiC;YACjC,MAAM;YAEN,kBAAkB;YAClB,QAAQ;YACR,WAAW;gBACT;gBACA;YACF,GAAG;QAEL,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,oBAAoB;YAClC,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC9C,QAAQ;QACV,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI,aAAkB,eAAe,CAAC,OAAO,QAAQ,EAAE;YACrD,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM;QACN,uCAAkB;;QAElB;QAEA,+BAA+B;QAC/B,MAAM,eAAe;QACrB,MAAM,SAAS,WAAW,SAAS,MAAM;QAEzC,+CAA+C;QAC/C,MAAM,eAAe,OAAO,KAAK,KAAK,CAAC,SAAS;QAChD,MAAM,YAAY,aAAa,QAAQ,CAAC,IAAI,QAAQ,CAAC,IAAI;QAEzD,6DAA6D;QAC7D,MAAM,kBAAkB,YAAY,KAAK,CAAC,GAAG,WAAW,GAAG,QAAQ,CAAC,IAAI;QAExE,+DAA+D;QAC/D,MAAM,eAAe,CAAC,UAAU,EAAE,kBAAkB,WAAW;QAE/D,MAAM,WAAW;YACf,MAAM;YACN,IAAI;YACJ,MAAM;YACN,KAAK;QACP;QAEA,QAAQ,GAAG,CAAC,oBAAoB;YAC9B,QAAQ,SAAS,MAAM;YACvB,WAAW,aAAa,QAAQ;YAChC;YACA;QACF;QAEA,MAAM,SAAS,MAAM,OAAO,QAAQ,CAAC,OAAO,CAAC;YAC3C,QAAQ;YACR,QAAQ;gBAAC;aAAS;QACpB;QAEA,QAAQ,GAAG,CAAC,8BAA8B;QAC1C,OAAO;IACT;IAEA,MAAM,sBAAsB;QAC1B,MAAM,YAAY;YAChB,MAAM;YACN,QAAQ,WAAW,SAAS,MAAM;YAClC,MAAM,WAAW,SAAS,IAAI;YAC9B,QAAQ;YACR,aAAa;QACf;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,YAAS,CAAC,WAAW,CAAC;QAEpD,IAAI,OAAO;YACT,MAAM,IAAI,MAAM,CAAC,sBAAsB,EAAE,MAAM,OAAO,EAAE;QAC1D;QAEA,QAAQ,GAAG,CAAC,gBAAgB;QAC5B,OAAO;IACT;IAEA,IAAI,SAAS,YAAY;QACvB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA0C;;;;;;kCACxD,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;;4BAAqB;4BAAc,SAAS,MAAM;4BAAC;;;;;;;kCAChE,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;IAI7C;IAEA,IAAI,SAAS,WAAW;QACtB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAE,WAAU;;4BAAqB;4BAAqB,SAAS,MAAM;4BAAC;;;;;;;kCACvE,6LAAC;wBAAE,WAAU;;4BAAwB;4BAAmB,SAAS,OAAO,CAAC;4BAAG;;;;;;;;;;;;;;;;;;IAIpF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAmD;;;;;;kCACjE,6LAAC;wBAAE,WAAU;kCAAoC;;;;;;;;;;;;0BAKnD,6LAAC;gBAAK,UAAU;gBAAc,WAAU;;kCACtC,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAS,WAAU;;oCAAiC;oCAC1C;oCAAU;;;;;;;0CAEnC,6LAAC;gCACC,MAAK;gCACL,IAAG;gCACH,MAAK;gCACL,OAAO,SAAS,MAAM;gCACtB,UAAU;gCACV,aAAY;gCACZ,KAAK;gCACL,MAAK;gCACL,WAAU;gCACV,QAAQ;;;;;;;;;;;;kCAIZ,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAO,WAAU;0CAAiC;;;;;;0CAGjE,6LAAC;gCACC,MAAK;gCACL,IAAG;gCACH,MAAK;gCACL,OAAO,SAAS,IAAI;gCACpB,UAAU;gCACV,aAAY;gCACZ,MAAK;gCACL,WAAU;gCACV,QAAQ;;;;;;;;;;;;oBAIX,SAAS,MAAM,IAAI,SAAS,IAAI,kBAC/B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA2C;;;;;;0CACzD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;0DAAK;;;;;;0DACN,6LAAC;;oDAAM,SAAS,MAAM;oDAAC;;;;;;;;;;;;;kDAEzB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;0DAAK;;;;;;0DACN,6LAAC;;oDAAK;oDAAE,SAAS,IAAI;oDAAC;;;;;;;;;;;;;kDAExB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;0DAAK;;;;;;0DACN,6LAAC;;oDAAK;oDAAE,SAAS,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;oBAMhC,uBACC,6LAAC;wBAAI,WAAU;kCACZ;;;;;;kCAIL,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;0CACX;;;;;;0CAGD,6LAAC;gCACC,MAAK;gCACL,UAAU;gCACV,WAAU;0CAET,UAAU,kBAAkB;;;;;;;;;;;;;;;;;;0BAKnC,6LAAC;gBAAE,WAAU;0BAAyC;;;;;;;;;;;;AAK5D;GA/PwB;;QACF,8JAAA,CAAA,aAAU;;;KADR", "debugId": null}}, {"offset": {"line": 1344, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/src/components/BuyUSDTFlow.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useAccount } from 'wagmi'\nimport { dbHelpers } from '@/lib/supabase'\n\ninterface SellOrder {\n  id: string\n  amount: number\n  rate: number\n  user_wallet: string\n  created_at: string\n  users: {\n    wallet: string\n    upi_id?: string\n    bank_details?: string\n  }\n}\n\ninterface BuyUSDTFlowProps {\n  onClose: () => void\n  onSuccess: () => void\n}\n\nexport default function BuyUSDTFlow({ onClose, onSuccess }: BuyUSDTFlowProps) {\n  const { address } = useAccount()\n  const [sellOrders, setSellOrders] = useState<SellOrder[]>([])\n  const [selectedOrder, setSelectedOrder] = useState<SellOrder | null>(null)\n  const [loading, setLoading] = useState(true)\n  const [error, setError] = useState('')\n  const [step, setStep] = useState<'orders' | 'payment' | 'proof' | 'success'>('orders')\n  const [buyAmount, setBuyAmount] = useState('')\n  const [proofFile, setProofFile] = useState<File | null>(null)\n  const [uploading, setUploading] = useState(false)\n\n  useEffect(() => {\n    loadSellOrders()\n  }, [])\n\n  const loadSellOrders = async () => {\n    try {\n      setLoading(true)\n      const { data, error } = await dbHelpers.getSellOrders()\n      \n      if (error) {\n        setError('Failed to load sell orders: ' + error.message)\n        return\n      }\n\n      setSellOrders(data || [])\n    } catch (err) {\n      setError('Failed to load sell orders')\n      console.error('Load sell orders error:', err)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleSelectOrder = (order: SellOrder) => {\n    setSelectedOrder(order)\n    setBuyAmount(order.amount.toString())\n    setStep('payment')\n  }\n\n  const handlePaymentMade = () => {\n    setStep('proof')\n  }\n\n  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const file = e.target.files?.[0]\n    if (file) {\n      // Validate file type\n      if (!file.type.startsWith('image/')) {\n        setError('Please select an image file')\n        return\n      }\n      // Validate file size (max 5MB)\n      if (file.size > 5 * 1024 * 1024) {\n        setError('File size must be less than 5MB')\n        return\n      }\n      setProofFile(file)\n      setError('')\n    }\n  }\n\n  const handleSubmitProof = async () => {\n    if (!proofFile || !selectedOrder || !address) {\n      setError('Missing required information')\n      return\n    }\n\n    setUploading(true)\n    setError('')\n\n    try {\n      // Upload proof to Supabase\n      const { data, error } = await dbHelpers.uploadProof(\n        proofFile,\n        selectedOrder.id,\n        address\n      )\n\n      if (error) {\n        setError('Failed to upload proof: ' + error.message)\n        return\n      }\n\n      console.log('Proof uploaded successfully:', data)\n      setStep('success')\n      \n      setTimeout(() => {\n        onSuccess()\n        onClose()\n      }, 3000)\n\n    } catch (err) {\n      console.error('Upload proof error:', err)\n      setError('Failed to upload proof')\n    } finally {\n      setUploading(false)\n    }\n  }\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleString()\n  }\n\n  const formatWallet = (wallet: string) => {\n    return `${wallet.slice(0, 6)}...${wallet.slice(-4)}`\n  }\n\n  if (loading) {\n    return (\n      <div className=\"max-w-2xl mx-auto text-center\">\n        <div className=\"animate-spin w-8 h-8 border-4 border-blue-400 border-t-transparent rounded-full mx-auto mb-4\"></div>\n        <p>Loading sell orders...</p>\n      </div>\n    )\n  }\n\n  if (step === 'payment' && selectedOrder) {\n    const totalAmount = parseFloat(buyAmount) * selectedOrder.rate\n\n    return (\n      <div className=\"max-w-md mx-auto\">\n        <div className=\"mb-6 p-4 bg-green-900/20 border border-green-500/30 rounded-lg\">\n          <h2 className=\"text-2xl font-bold mb-2 text-center text-green-400\">Payment Details</h2>\n          <p className=\"text-gray-300 text-center text-sm\">\n            Make payment to the seller\n          </p>\n        </div>\n\n        {/* Order Summary */}\n        <div className=\"mb-6 p-4 bg-blue-900/20 border border-blue-500/30 rounded-lg\">\n          <h3 className=\"text-lg font-semibold mb-3 text-blue-400\">Order Summary</h3>\n          <div className=\"space-y-2 text-sm\">\n            <div className=\"flex justify-between\">\n              <span>USDT Amount:</span>\n              <span>{buyAmount} USDT</span>\n            </div>\n            <div className=\"flex justify-between\">\n              <span>Rate:</span>\n              <span>₹{selectedOrder.rate} per USDT</span>\n            </div>\n            <div className=\"flex justify-between font-semibold text-blue-400\">\n              <span>Total to Pay:</span>\n              <span>₹{totalAmount.toFixed(2)}</span>\n            </div>\n            <div className=\"flex justify-between text-xs text-gray-500\">\n              <span>Seller:</span>\n              <span>{formatWallet(selectedOrder.user_wallet)}</span>\n            </div>\n          </div>\n        </div>\n\n        {/* Payment Information */}\n        <div className=\"mb-6 p-4 bg-yellow-900/20 border border-yellow-500/30 rounded-lg\">\n          <h3 className=\"text-lg font-semibold mb-3 text-yellow-400\">Payment Information</h3>\n          \n          {selectedOrder.users.upi_id && (\n            <div className=\"mb-3\">\n              <p className=\"text-sm text-gray-400\">UPI ID:</p>\n              <p className=\"font-mono text-lg text-yellow-400\">{selectedOrder.users.upi_id}</p>\n            </div>\n          )}\n          \n          {selectedOrder.users.bank_details && (\n            <div className=\"mb-3\">\n              <p className=\"text-sm text-gray-400\">Bank Details:</p>\n              <pre className=\"text-sm text-yellow-400 whitespace-pre-wrap\">{selectedOrder.users.bank_details}</pre>\n            </div>\n          )}\n\n          <div className=\"mt-4 p-3 bg-yellow-800/30 rounded\">\n            <p className=\"text-xs text-yellow-200\">\n              💡 Send exactly ₹{totalAmount.toFixed(2)} to the above payment details. \n              After payment, click \"I Have Made Payment\" and upload your payment proof.\n            </p>\n          </div>\n        </div>\n\n        <div className=\"flex gap-4\">\n          <button\n            onClick={() => setStep('orders')}\n            className=\"flex-1 px-6 py-3 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-800\"\n          >\n            ← Back\n          </button>\n          <button\n            onClick={handlePaymentMade}\n            className=\"flex-1 btn-primary\"\n          >\n            I Have Made Payment\n          </button>\n        </div>\n      </div>\n    )\n  }\n\n  if (step === 'proof' && selectedOrder) {\n    return (\n      <div className=\"max-w-md mx-auto\">\n        <div className=\"mb-6 p-4 bg-purple-900/20 border border-purple-500/30 rounded-lg\">\n          <h2 className=\"text-2xl font-bold mb-2 text-center text-purple-400\">Upload Payment Proof</h2>\n          <p className=\"text-gray-300 text-center text-sm\">\n            Upload screenshot of your payment\n          </p>\n        </div>\n\n        <div className=\"mb-6\">\n          <label htmlFor=\"proof\" className=\"block text-sm font-medium mb-2\">\n            Payment Proof Image\n          </label>\n          <input\n            type=\"file\"\n            id=\"proof\"\n            accept=\"image/*\"\n            onChange={handleFileChange}\n            className=\"w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg focus:outline-none focus:border-blue-500\"\n            required\n          />\n          <p className=\"text-xs text-gray-500 mt-2\">\n            Supported formats: JPG, PNG, GIF (Max 5MB)\n          </p>\n        </div>\n\n        {proofFile && (\n          <div className=\"mb-6 p-4 bg-green-900/20 border border-green-500/30 rounded-lg\">\n            <p className=\"text-green-400 text-sm\">\n              ✅ File selected: {proofFile.name}\n            </p>\n          </div>\n        )}\n\n        {error && (\n          <div className=\"mb-4 text-red-500 text-sm p-2 bg-red-900/20 rounded\">\n            {error}\n          </div>\n        )}\n\n        <div className=\"flex gap-4\">\n          <button\n            onClick={() => setStep('payment')}\n            className=\"flex-1 px-6 py-3 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-800\"\n          >\n            ← Back\n          </button>\n          <button\n            onClick={handleSubmitProof}\n            disabled={!proofFile || uploading}\n            className=\"flex-1 btn-primary disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            {uploading ? 'Uploading...' : 'Submit Proof'}\n          </button>\n        </div>\n      </div>\n    )\n  }\n\n  if (step === 'success') {\n    return (\n      <div className=\"max-w-md mx-auto text-center\">\n        <div className=\"mb-6 p-6 bg-green-900/20 border border-green-500/30 rounded-lg\">\n          <h2 className=\"text-2xl font-bold mb-4 text-green-400\">✅ Proof Submitted!</h2>\n          <p className=\"text-gray-300 mb-2\">Your payment proof has been submitted successfully.</p>\n          <p className=\"text-sm text-gray-500\">Admin will verify your payment and release USDT to your wallet.</p>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"max-w-4xl mx-auto\">\n      <div className=\"mb-6 p-4 bg-green-900/20 border border-green-500/30 rounded-lg\">\n        <h2 className=\"text-2xl font-bold mb-2 text-center text-green-400\">Buy USDT</h2>\n        <p className=\"text-gray-300 text-center text-sm\">\n          Choose from available sell orders\n        </p>\n      </div>\n\n      {error && (\n        <div className=\"mb-4 p-4 bg-red-900/20 border border-red-500/30 rounded-lg text-red-400\">\n          {error}\n        </div>\n      )}\n\n      <div className=\"space-y-4\">\n        {sellOrders.length === 0 ? (\n          <div className=\"text-center py-8\">\n            <p className=\"text-gray-500\">No sell orders available at the moment.</p>\n            <button\n              onClick={loadSellOrders}\n              className=\"mt-4 btn-primary px-6 py-2\"\n            >\n              Refresh\n            </button>\n          </div>\n        ) : (\n          sellOrders.map((order) => (\n            <div key={order.id} className=\"p-4 bg-gray-800 border border-gray-700 rounded-lg hover:border-green-500/50 transition-colors\">\n              <div className=\"grid grid-cols-1 md:grid-cols-5 gap-4 items-center\">\n                <div>\n                  <p className=\"text-sm text-gray-400\">Amount</p>\n                  <p className=\"font-semibold text-lg\">{order.amount} USDT</p>\n                </div>\n                <div>\n                  <p className=\"text-sm text-gray-400\">Rate</p>\n                  <p className=\"font-semibold\">₹{order.rate}</p>\n                </div>\n                <div>\n                  <p className=\"text-sm text-gray-400\">Total</p>\n                  <p className=\"font-semibold text-green-400\">₹{(order.amount * order.rate).toFixed(2)}</p>\n                </div>\n                <div>\n                  <p className=\"text-sm text-gray-400\">Seller</p>\n                  <p className=\"font-mono text-sm\">{formatWallet(order.user_wallet)}</p>\n                  <p className=\"text-xs text-gray-500\">{formatDate(order.created_at)}</p>\n                </div>\n                <div>\n                  <button\n                    onClick={() => handleSelectOrder(order)}\n                    className=\"btn-primary w-full\"\n                    disabled={order.user_wallet === address}\n                  >\n                    {order.user_wallet === address ? 'Your Order' : 'Buy'}\n                  </button>\n                </div>\n              </div>\n            </div>\n          ))\n        )}\n      </div>\n\n      <div className=\"mt-6 text-center\">\n        <button\n          onClick={onClose}\n          className=\"px-6 py-2 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-800\"\n        >\n          Close\n        </button>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAwBe,SAAS,YAAY,EAAE,OAAO,EAAE,SAAS,EAAoB;;IAC1E,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,aAAU,AAAD;IAC7B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IAC5D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IACrE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA8C;IAC7E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IACxD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR;QACF;gCAAG,EAAE;IAEL,MAAM,iBAAiB;QACrB,IAAI;YACF,WAAW;YACX,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,YAAS,CAAC,aAAa;YAErD,IAAI,OAAO;gBACT,SAAS,iCAAiC,MAAM,OAAO;gBACvD;YACF;YAEA,cAAc,QAAQ,EAAE;QAC1B,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,2BAA2B;QAC3C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,iBAAiB;QACjB,aAAa,MAAM,MAAM,CAAC,QAAQ;QAClC,QAAQ;IACV;IAEA,MAAM,oBAAoB;QACxB,QAAQ;IACV;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QAChC,IAAI,MAAM;YACR,qBAAqB;YACrB,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;gBACnC,SAAS;gBACT;YACF;YACA,+BAA+B;YAC/B,IAAI,KAAK,IAAI,GAAG,IAAI,OAAO,MAAM;gBAC/B,SAAS;gBACT;YACF;YACA,aAAa;YACb,SAAS;QACX;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,SAAS;YAC5C,SAAS;YACT;QACF;QAEA,aAAa;QACb,SAAS;QAET,IAAI;YACF,2BAA2B;YAC3B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,YAAS,CAAC,WAAW,CACjD,WACA,cAAc,EAAE,EAChB;YAGF,IAAI,OAAO;gBACT,SAAS,6BAA6B,MAAM,OAAO;gBACnD;YACF;YAEA,QAAQ,GAAG,CAAC,gCAAgC;YAC5C,QAAQ;YAER,WAAW;gBACT;gBACA;YACF,GAAG;QAEL,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,uBAAuB;YACrC,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,cAAc;IAC5C;IAEA,MAAM,eAAe,CAAC;QACpB,OAAO,GAAG,OAAO,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,OAAO,KAAK,CAAC,CAAC,IAAI;IACtD;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;8BAAE;;;;;;;;;;;;IAGT;IAEA,IAAI,SAAS,aAAa,eAAe;QACvC,MAAM,cAAc,WAAW,aAAa,cAAc,IAAI;QAE9D,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAqD;;;;;;sCACnE,6LAAC;4BAAE,WAAU;sCAAoC;;;;;;;;;;;;8BAMnD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA2C;;;;;;sCACzD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;sDAAK;;;;;;sDACN,6LAAC;;gDAAM;gDAAU;;;;;;;;;;;;;8CAEnB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;sDAAK;;;;;;sDACN,6LAAC;;gDAAK;gDAAE,cAAc,IAAI;gDAAC;;;;;;;;;;;;;8CAE7B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;sDAAK;;;;;;sDACN,6LAAC;;gDAAK;gDAAE,YAAY,OAAO,CAAC;;;;;;;;;;;;;8CAE9B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;sDAAK;;;;;;sDACN,6LAAC;sDAAM,aAAa,cAAc,WAAW;;;;;;;;;;;;;;;;;;;;;;;;8BAMnD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA6C;;;;;;wBAE1D,cAAc,KAAK,CAAC,MAAM,kBACzB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;8CACrC,6LAAC;oCAAE,WAAU;8CAAqC,cAAc,KAAK,CAAC,MAAM;;;;;;;;;;;;wBAI/E,cAAc,KAAK,CAAC,YAAY,kBAC/B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;8CACrC,6LAAC;oCAAI,WAAU;8CAA+C,cAAc,KAAK,CAAC,YAAY;;;;;;;;;;;;sCAIlG,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;;oCAA0B;oCACnB,YAAY,OAAO,CAAC;oCAAG;;;;;;;;;;;;;;;;;;8BAM/C,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,SAAS,IAAM,QAAQ;4BACvB,WAAU;sCACX;;;;;;sCAGD,6LAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;;;;;;;IAMT;IAEA,IAAI,SAAS,WAAW,eAAe;QACrC,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAsD;;;;;;sCACpE,6LAAC;4BAAE,WAAU;sCAAoC;;;;;;;;;;;;8BAKnD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAM,SAAQ;4BAAQ,WAAU;sCAAiC;;;;;;sCAGlE,6LAAC;4BACC,MAAK;4BACL,IAAG;4BACH,QAAO;4BACP,UAAU;4BACV,WAAU;4BACV,QAAQ;;;;;;sCAEV,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;gBAK3C,2BACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;;4BAAyB;4BAClB,UAAU,IAAI;;;;;;;;;;;;gBAKrC,uBACC,6LAAC;oBAAI,WAAU;8BACZ;;;;;;8BAIL,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,SAAS,IAAM,QAAQ;4BACvB,WAAU;sCACX;;;;;;sCAGD,6LAAC;4BACC,SAAS;4BACT,UAAU,CAAC,aAAa;4BACxB,WAAU;sCAET,YAAY,iBAAiB;;;;;;;;;;;;;;;;;;IAKxC;IAEA,IAAI,SAAS,WAAW;QACtB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAClC,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;IAI7C;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAqD;;;;;;kCACnE,6LAAC;wBAAE,WAAU;kCAAoC;;;;;;;;;;;;YAKlD,uBACC,6LAAC;gBAAI,WAAU;0BACZ;;;;;;0BAIL,6LAAC;gBAAI,WAAU;0BACZ,WAAW,MAAM,KAAK,kBACrB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;sCAC7B,6LAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;2BAKH,WAAW,GAAG,CAAC,CAAC,sBACd,6LAAC;wBAAmB,WAAU;kCAC5B,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;sDACrC,6LAAC;4CAAE,WAAU;;gDAAyB,MAAM,MAAM;gDAAC;;;;;;;;;;;;;8CAErD,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;sDACrC,6LAAC;4CAAE,WAAU;;gDAAgB;gDAAE,MAAM,IAAI;;;;;;;;;;;;;8CAE3C,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;sDACrC,6LAAC;4CAAE,WAAU;;gDAA+B;gDAAE,CAAC,MAAM,MAAM,GAAG,MAAM,IAAI,EAAE,OAAO,CAAC;;;;;;;;;;;;;8CAEpF,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;sDACrC,6LAAC;4CAAE,WAAU;sDAAqB,aAAa,MAAM,WAAW;;;;;;sDAChE,6LAAC;4CAAE,WAAU;sDAAyB,WAAW,MAAM,UAAU;;;;;;;;;;;;8CAEnE,6LAAC;8CACC,cAAA,6LAAC;wCACC,SAAS,IAAM,kBAAkB;wCACjC,WAAU;wCACV,UAAU,MAAM,WAAW,KAAK;kDAE/B,MAAM,WAAW,KAAK,UAAU,eAAe;;;;;;;;;;;;;;;;;uBAzB9C,MAAM,EAAE;;;;;;;;;;0BAkCxB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,SAAS;oBACT,WAAU;8BACX;;;;;;;;;;;;;;;;;AAMT;GApVwB;;QACF,8JAAA,CAAA,aAAU;;;KADR", "debugId": null}}, {"offset": {"line": 2173, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/src/components/AdminDashboard.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { dbHelpers } from '@/lib/supabase'\n\ninterface Order {\n  id: string\n  type: 'buy' | 'sell'\n  amount: number\n  rate: number\n  status: 'pending' | 'completed' | 'cancelled'\n  user_wallet: string\n  created_at: string\n}\n\ninterface AdminDashboardProps {\n  onClose: () => void\n}\n\nexport default function AdminDashboard({ onClose }: AdminDashboardProps) {\n  const [orders, setOrders] = useState<Order[]>([])\n  const [loading, setLoading] = useState(true)\n  const [error, setError] = useState('')\n\n  useEffect(() => {\n    loadOrders()\n  }, [])\n\n  const loadOrders = async () => {\n    try {\n      setLoading(true)\n      // Get all orders (we'll create this function)\n      const { data, error } = await dbHelpers.getAllOrders()\n      \n      if (error) {\n        setError('Failed to load orders: ' + error.message)\n        return\n      }\n\n      setOrders(data || [])\n    } catch (err) {\n      setError('Failed to load orders')\n      console.error('Load orders error:', err)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const updateOrderStatus = async (orderId: string, newStatus: 'completed' | 'cancelled') => {\n    try {\n      const { error } = await dbHelpers.updateOrderStatus(orderId, newStatus)\n      \n      if (error) {\n        setError('Failed to update order: ' + error.message)\n        return\n      }\n\n      // Refresh orders\n      await loadOrders()\n    } catch (err) {\n      setError('Failed to update order')\n      console.error('Update order error:', err)\n    }\n  }\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleString()\n  }\n\n  const formatWallet = (wallet: string) => {\n    return `${wallet.slice(0, 6)}...${wallet.slice(-4)}`\n  }\n\n  if (loading) {\n    return (\n      <div className=\"max-w-4xl mx-auto\">\n        <div className=\"text-center py-8\">\n          <div className=\"animate-spin w-8 h-8 border-4 border-blue-400 border-t-transparent rounded-full mx-auto mb-4\"></div>\n          <p>Loading orders...</p>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"max-w-6xl mx-auto\">\n      <div className=\"mb-6 p-4 bg-blue-900/20 border border-blue-500/30 rounded-lg\">\n        <div className=\"flex justify-between items-center\">\n          <h2 className=\"text-2xl font-bold text-blue-400\">Admin Dashboard</h2>\n          <button\n            onClick={onClose}\n            className=\"text-gray-400 hover:text-white text-2xl\"\n          >\n            ×\n          </button>\n        </div>\n        <p className=\"text-gray-300 text-sm mt-2\">\n          Manage all buy and sell orders\n        </p>\n      </div>\n\n      {error && (\n        <div className=\"mb-4 p-4 bg-red-900/20 border border-red-500/30 rounded-lg text-red-400\">\n          {error}\n        </div>\n      )}\n\n      <div className=\"grid gap-6\">\n        {/* Sell Orders */}\n        <div>\n          <h3 className=\"text-xl font-semibold mb-4 text-red-400\">Sell Orders</h3>\n          <div className=\"space-y-4\">\n            {orders.filter(order => order.type === 'sell').map((order) => (\n              <div key={order.id} className=\"p-4 bg-gray-800 border border-gray-700 rounded-lg\">\n                <div className=\"grid grid-cols-1 md:grid-cols-6 gap-4 items-center\">\n                  <div>\n                    <p className=\"text-sm text-gray-400\">Amount</p>\n                    <p className=\"font-semibold\">{order.amount} USDT</p>\n                  </div>\n                  <div>\n                    <p className=\"text-sm text-gray-400\">Rate</p>\n                    <p className=\"font-semibold\">₹{order.rate}</p>\n                  </div>\n                  <div>\n                    <p className=\"text-sm text-gray-400\">Total</p>\n                    <p className=\"font-semibold\">₹{(order.amount * order.rate).toFixed(2)}</p>\n                  </div>\n                  <div>\n                    <p className=\"text-sm text-gray-400\">Seller</p>\n                    <p className=\"font-mono text-sm\">{formatWallet(order.user_wallet)}</p>\n                  </div>\n                  <div>\n                    <p className=\"text-sm text-gray-400\">Status</p>\n                    <span className={`px-2 py-1 rounded text-xs font-semibold ${\n                      order.status === 'pending' ? 'bg-yellow-900/50 text-yellow-400' :\n                      order.status === 'completed' ? 'bg-green-900/50 text-green-400' :\n                      'bg-red-900/50 text-red-400'\n                    }`}>\n                      {order.status}\n                    </span>\n                  </div>\n                  <div>\n                    {order.status === 'pending' && (\n                      <div className=\"flex gap-2\">\n                        <button\n                          onClick={() => updateOrderStatus(order.id, 'completed')}\n                          className=\"px-3 py-1 bg-green-600 hover:bg-green-700 text-white text-xs rounded\"\n                        >\n                          Complete\n                        </button>\n                        <button\n                          onClick={() => updateOrderStatus(order.id, 'cancelled')}\n                          className=\"px-3 py-1 bg-red-600 hover:bg-red-700 text-white text-xs rounded\"\n                        >\n                          Cancel\n                        </button>\n                      </div>\n                    )}\n                  </div>\n                </div>\n                <div className=\"mt-2 text-xs text-gray-500\">\n                  Created: {formatDate(order.created_at)}\n                </div>\n              </div>\n            ))}\n            {orders.filter(order => order.type === 'sell').length === 0 && (\n              <p className=\"text-gray-500 text-center py-8\">No sell orders found</p>\n            )}\n          </div>\n        </div>\n\n        {/* Buy Orders */}\n        <div>\n          <h3 className=\"text-xl font-semibold mb-4 text-green-400\">Buy Orders</h3>\n          <div className=\"space-y-4\">\n            {orders.filter(order => order.type === 'buy').map((order) => (\n              <div key={order.id} className=\"p-4 bg-gray-800 border border-gray-700 rounded-lg\">\n                <div className=\"grid grid-cols-1 md:grid-cols-6 gap-4 items-center\">\n                  <div>\n                    <p className=\"text-sm text-gray-400\">Amount</p>\n                    <p className=\"font-semibold\">{order.amount} USDT</p>\n                  </div>\n                  <div>\n                    <p className=\"text-sm text-gray-400\">Rate</p>\n                    <p className=\"font-semibold\">₹{order.rate}</p>\n                  </div>\n                  <div>\n                    <p className=\"text-sm text-gray-400\">Total</p>\n                    <p className=\"font-semibold\">₹{(order.amount * order.rate).toFixed(2)}</p>\n                  </div>\n                  <div>\n                    <p className=\"text-sm text-gray-400\">Buyer</p>\n                    <p className=\"font-mono text-sm\">{formatWallet(order.user_wallet)}</p>\n                  </div>\n                  <div>\n                    <p className=\"text-sm text-gray-400\">Status</p>\n                    <span className={`px-2 py-1 rounded text-xs font-semibold ${\n                      order.status === 'pending' ? 'bg-yellow-900/50 text-yellow-400' :\n                      order.status === 'completed' ? 'bg-green-900/50 text-green-400' :\n                      'bg-red-900/50 text-red-400'\n                    }`}>\n                      {order.status}\n                    </span>\n                  </div>\n                  <div>\n                    {order.status === 'pending' && (\n                      <div className=\"flex gap-2\">\n                        <button\n                          onClick={() => updateOrderStatus(order.id, 'completed')}\n                          className=\"px-3 py-1 bg-green-600 hover:bg-green-700 text-white text-xs rounded\"\n                        >\n                          Complete\n                        </button>\n                        <button\n                          onClick={() => updateOrderStatus(order.id, 'cancelled')}\n                          className=\"px-3 py-1 bg-red-600 hover:bg-red-700 text-white text-xs rounded\"\n                        >\n                          Cancel\n                        </button>\n                      </div>\n                    )}\n                  </div>\n                </div>\n                <div className=\"mt-2 text-xs text-gray-500\">\n                  Created: {formatDate(order.created_at)}\n                </div>\n              </div>\n            ))}\n            {orders.filter(order => order.type === 'buy').length === 0 && (\n              <p className=\"text-gray-500 text-center py-8\">No buy orders found</p>\n            )}\n          </div>\n        </div>\n      </div>\n\n      <div className=\"mt-6 text-center\">\n        <button\n          onClick={loadOrders}\n          className=\"btn-primary px-6 py-2\"\n        >\n          Refresh Orders\n        </button>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAmBe,SAAS,eAAe,EAAE,OAAO,EAAuB;;IACrE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAChD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR;QACF;mCAAG,EAAE;IAEL,MAAM,aAAa;QACjB,IAAI;YACF,WAAW;YACX,8CAA8C;YAC9C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,YAAS,CAAC,YAAY;YAEpD,IAAI,OAAO;gBACT,SAAS,4BAA4B,MAAM,OAAO;gBAClD;YACF;YAEA,UAAU,QAAQ,EAAE;QACtB,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,sBAAsB;QACtC,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB,OAAO,SAAiB;QAChD,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,YAAS,CAAC,iBAAiB,CAAC,SAAS;YAE7D,IAAI,OAAO;gBACT,SAAS,6BAA6B,MAAM,OAAO;gBACnD;YACF;YAEA,iBAAiB;YACjB,MAAM;QACR,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,uBAAuB;QACvC;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,cAAc;IAC5C;IAEA,MAAM,eAAe,CAAC;QACpB,OAAO,GAAG,OAAO,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,OAAO,KAAK,CAAC,CAAC,IAAI;IACtD;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;kCAAE;;;;;;;;;;;;;;;;;IAIX;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,6LAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;kCAIH,6LAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;YAK3C,uBACC,6LAAC;gBAAI,WAAU;0BACZ;;;;;;0BAIL,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAA0C;;;;;;0CACxD,6LAAC;gCAAI,WAAU;;oCACZ,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,IAAI,KAAK,QAAQ,GAAG,CAAC,CAAC,sBAClD,6LAAC;4CAAmB,WAAU;;8DAC5B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;8EACrC,6LAAC;oEAAE,WAAU;;wEAAiB,MAAM,MAAM;wEAAC;;;;;;;;;;;;;sEAE7C,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;8EACrC,6LAAC;oEAAE,WAAU;;wEAAgB;wEAAE,MAAM,IAAI;;;;;;;;;;;;;sEAE3C,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;8EACrC,6LAAC;oEAAE,WAAU;;wEAAgB;wEAAE,CAAC,MAAM,MAAM,GAAG,MAAM,IAAI,EAAE,OAAO,CAAC;;;;;;;;;;;;;sEAErE,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;8EACrC,6LAAC;oEAAE,WAAU;8EAAqB,aAAa,MAAM,WAAW;;;;;;;;;;;;sEAElE,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;8EACrC,6LAAC;oEAAK,WAAW,CAAC,wCAAwC,EACxD,MAAM,MAAM,KAAK,YAAY,qCAC7B,MAAM,MAAM,KAAK,cAAc,mCAC/B,8BACA;8EACC,MAAM,MAAM;;;;;;;;;;;;sEAGjB,6LAAC;sEACE,MAAM,MAAM,KAAK,2BAChB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEACC,SAAS,IAAM,kBAAkB,MAAM,EAAE,EAAE;wEAC3C,WAAU;kFACX;;;;;;kFAGD,6LAAC;wEACC,SAAS,IAAM,kBAAkB,MAAM,EAAE,EAAE;wEAC3C,WAAU;kFACX;;;;;;;;;;;;;;;;;;;;;;;8DAOT,6LAAC;oDAAI,WAAU;;wDAA6B;wDAChC,WAAW,MAAM,UAAU;;;;;;;;2CAhD/B,MAAM,EAAE;;;;;oCAoDnB,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,IAAI,KAAK,QAAQ,MAAM,KAAK,mBACxD,6LAAC;wCAAE,WAAU;kDAAiC;;;;;;;;;;;;;;;;;;kCAMpD,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAA4C;;;;;;0CAC1D,6LAAC;gCAAI,WAAU;;oCACZ,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,IAAI,KAAK,OAAO,GAAG,CAAC,CAAC,sBACjD,6LAAC;4CAAmB,WAAU;;8DAC5B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;8EACrC,6LAAC;oEAAE,WAAU;;wEAAiB,MAAM,MAAM;wEAAC;;;;;;;;;;;;;sEAE7C,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;8EACrC,6LAAC;oEAAE,WAAU;;wEAAgB;wEAAE,MAAM,IAAI;;;;;;;;;;;;;sEAE3C,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;8EACrC,6LAAC;oEAAE,WAAU;;wEAAgB;wEAAE,CAAC,MAAM,MAAM,GAAG,MAAM,IAAI,EAAE,OAAO,CAAC;;;;;;;;;;;;;sEAErE,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;8EACrC,6LAAC;oEAAE,WAAU;8EAAqB,aAAa,MAAM,WAAW;;;;;;;;;;;;sEAElE,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;8EACrC,6LAAC;oEAAK,WAAW,CAAC,wCAAwC,EACxD,MAAM,MAAM,KAAK,YAAY,qCAC7B,MAAM,MAAM,KAAK,cAAc,mCAC/B,8BACA;8EACC,MAAM,MAAM;;;;;;;;;;;;sEAGjB,6LAAC;sEACE,MAAM,MAAM,KAAK,2BAChB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEACC,SAAS,IAAM,kBAAkB,MAAM,EAAE,EAAE;wEAC3C,WAAU;kFACX;;;;;;kFAGD,6LAAC;wEACC,SAAS,IAAM,kBAAkB,MAAM,EAAE,EAAE;wEAC3C,WAAU;kFACX;;;;;;;;;;;;;;;;;;;;;;;8DAOT,6LAAC;oDAAI,WAAU;;wDAA6B;wDAChC,WAAW,MAAM,UAAU;;;;;;;;2CAhD/B,MAAM,EAAE;;;;;oCAoDnB,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,IAAI,KAAK,OAAO,MAAM,KAAK,mBACvD,6LAAC;wCAAE,WAAU;kDAAiC;;;;;;;;;;;;;;;;;;;;;;;;0BAMtD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,SAAS;oBACT,WAAU;8BACX;;;;;;;;;;;;;;;;;AAMT;GAlOwB;KAAA", "debugId": null}}, {"offset": {"line": 2810, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/src/components/Modal.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\n\ninterface ModalProps {\n  isOpen: boolean\n  onClose: () => void\n  children: React.ReactNode\n  title?: string\n}\n\nexport default function Modal({ isOpen, onClose, children, title }: ModalProps) {\n  useEffect(() => {\n    if (isOpen) {\n      document.body.style.overflow = 'hidden'\n    } else {\n      document.body.style.overflow = 'unset'\n    }\n\n    return () => {\n      document.body.style.overflow = 'unset'\n    }\n  }, [isOpen])\n\n  if (!isOpen) return null\n\n  return (\n    <div className=\"fixed inset-0 z-50 flex items-center justify-center\">\n      {/* Backdrop */}\n      <div \n        className=\"absolute inset-0 bg-black/70 backdrop-blur-sm\"\n        onClick={onClose}\n      />\n      \n      {/* Modal */}\n      <div className=\"relative bg-gray-900 border border-gray-700 rounded-lg shadow-xl max-w-lg w-full mx-4 max-h-[90vh] overflow-y-auto\">\n        {/* Header */}\n        {title && (\n          <div className=\"flex items-center justify-between p-4 border-b border-gray-700\">\n            <h2 className=\"text-xl font-semibold\">{title}</h2>\n            <button\n              onClick={onClose}\n              className=\"text-gray-400 hover:text-white text-2xl\"\n            >\n              ×\n            </button>\n          </div>\n        )}\n        \n        {/* Content */}\n        <div className=\"p-6\">\n          {children}\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAWe,SAAS,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAc;;IAC5E,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2BAAE;YACR,IAAI,QAAQ;gBACV,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC,OAAO;gBACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC;YAEA;mCAAO;oBACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;gBACjC;;QACF;0BAAG;QAAC;KAAO;IAEX,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBACC,WAAU;gBACV,SAAS;;;;;;0BAIX,6LAAC;gBAAI,WAAU;;oBAEZ,uBACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAyB;;;;;;0CACvC,6LAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;kCAOL,6LAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;;;AAKX;GA7CwB;KAAA", "debugId": null}}, {"offset": {"line": 2912, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useAccount } from 'wagmi'\nimport WalletConnect from '@/components/WalletConnect'\nimport SellUSDTForm from '@/components/SellUSDTForm'\nimport BuyUSDTFlow from '@/components/BuyUSDTFlow'\nimport AdminDashboard from '@/components/AdminDashboard'\nimport Modal from '@/components/Modal'\nimport { useEffect, useState } from 'react'\n\nexport default function Home() {\n  const { isConnected } = useAccount()\n  const [mounted, setMounted] = useState(false)\n  const [showSellModal, setShowSellModal] = useState(false)\n  const [showBuyModal, setShowBuyModal] = useState(false)\n  const [showAdminModal, setShowAdminModal] = useState(false)\n\n  useEffect(() => {\n    setMounted(true)\n  }, [])\n\n  const handleSellSuccess = () => {\n    // Refresh or update UI after successful sell\n    console.log('Sell order created successfully!')\n  }\n\n  const handleBuySuccess = () => {\n    // Refresh or update UI after successful buy\n    console.log('Buy order completed successfully!')\n  }\n\n  if (!mounted) {\n    return (\n      <div className=\"min-h-screen flex flex-col items-center justify-center p-8\">\n        <div className=\"text-center max-w-4xl mx-auto\">\n          <h1 className=\"text-6xl font-bold mb-4\">\n            <span className=\"accent-primary\">Srd</span>\n            <span className=\"accent-secondary\">.Exchange</span>\n          </h1>\n          <p className=\"text-xl mb-8 text-gray-300\">\n            Secure P2P USDT Trading Platform\n          </p>\n          <p className=\"text-lg mb-12 text-gray-400\">\n            Buy and sell USDT with INR through our secure, admin-verified platform\n          </p>\n          <div className=\"text-lg\">Loading...</div>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen flex flex-col items-center justify-center p-8\">\n      {/* Hero Section */}\n      <div className=\"text-center max-w-4xl mx-auto\">\n        <h1 className=\"text-6xl font-bold mb-4\">\n          <span className=\"accent-primary\">Srd</span>\n          <span className=\"accent-secondary\">.Exchange</span>\n        </h1>\n        <p className=\"text-xl mb-8 text-gray-300\">\n          Secure P2P USDT Trading Platform\n        </p>\n        <p className=\"text-lg mb-12 text-gray-400\">\n          Buy and sell USDT with INR through our secure, admin-verified platform\n        </p>\n\n        {/* Wallet Connection Section */}\n        <div className=\"mb-12\">\n          <WalletConnect />\n        </div>\n\n        {/* Trading Section - Only show when wallet is connected */}\n        {isConnected && (\n          <div className=\"space-y-4\">\n            <div className=\"flex justify-center gap-4\">\n              <button\n                onClick={() => setShowBuyModal(true)}\n                className=\"btn-primary px-6 py-3 bg-green-600 hover:bg-green-700\"\n              >\n                Buy USDT\n              </button>\n              <button\n                onClick={() => setShowSellModal(true)}\n                className=\"btn-primary px-6 py-3 bg-red-600 hover:bg-red-700\"\n              >\n                Sell USDT\n              </button>\n            </div>\n\n            {/* Admin Button (for testing) */}\n            <div className=\"flex justify-center\">\n              <button\n                onClick={() => setShowAdminModal(true)}\n                className=\"text-sm text-gray-500 hover:text-gray-300 underline\"\n              >\n                Admin Dashboard\n              </button>\n            </div>\n          </div>\n        )}\n\n        {/* Features */}\n        <div className=\"grid md:grid-cols-3 gap-8 mt-16\">\n          <div className=\"p-6 border border-gray-700 rounded-lg\">\n            <h3 className=\"text-xl font-semibold mb-3 accent-primary\">Secure Trading</h3>\n            <p className=\"text-gray-400\">All transactions are verified by our admin team</p>\n          </div>\n          <div className=\"p-6 border border-gray-700 rounded-lg\">\n            <h3 className=\"text-xl font-semibold mb-3 accent-secondary\">Fast Processing</h3>\n            <p className=\"text-gray-400\">Quick verification and USDT release</p>\n          </div>\n          <div className=\"p-6 border border-gray-700 rounded-lg\">\n            <h3 className=\"text-xl font-semibold mb-3 accent-primary\">Wallet Integration</h3>\n            <p className=\"text-gray-400\">Connect with Metamask or Trust Wallet</p>\n          </div>\n        </div>\n      </div>\n\n      {/* Buy USDT Modal */}\n      <Modal\n        isOpen={showBuyModal}\n        onClose={() => setShowBuyModal(false)}\n      >\n        <BuyUSDTFlow\n          onClose={() => setShowBuyModal(false)}\n          onSuccess={handleBuySuccess}\n        />\n      </Modal>\n\n      {/* Sell USDT Modal */}\n      <Modal\n        isOpen={showSellModal}\n        onClose={() => setShowSellModal(false)}\n      >\n        <SellUSDTForm\n          onClose={() => setShowSellModal(false)}\n          onSuccess={handleSellSuccess}\n        />\n      </Modal>\n\n      {/* Admin Dashboard Modal */}\n      <Modal\n        isOpen={showAdminModal}\n        onClose={() => setShowAdminModal(false)}\n      >\n        <AdminDashboard\n          onClose={() => setShowAdminModal(false)}\n        />\n      </Modal>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;AAUe,SAAS;;IACtB,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,aAAU,AAAD;IACjC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,WAAW;QACb;yBAAG,EAAE;IAEL,MAAM,oBAAoB;QACxB,6CAA6C;QAC7C,QAAQ,GAAG,CAAC;IACd;IAEA,MAAM,mBAAmB;QACvB,4CAA4C;QAC5C,QAAQ,GAAG,CAAC;IACd;IAEA,IAAI,CAAC,SAAS;QACZ,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC;gCAAK,WAAU;0CAAiB;;;;;;0CACjC,6LAAC;gCAAK,WAAU;0CAAmB;;;;;;;;;;;;kCAErC,6LAAC;wBAAE,WAAU;kCAA6B;;;;;;kCAG1C,6LAAC;wBAAE,WAAU;kCAA8B;;;;;;kCAG3C,6LAAC;wBAAI,WAAU;kCAAU;;;;;;;;;;;;;;;;;IAIjC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC;gCAAK,WAAU;0CAAiB;;;;;;0CACjC,6LAAC;gCAAK,WAAU;0CAAmB;;;;;;;;;;;;kCAErC,6LAAC;wBAAE,WAAU;kCAA6B;;;;;;kCAG1C,6LAAC;wBAAE,WAAU;kCAA8B;;;;;;kCAK3C,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,sIAAA,CAAA,UAAa;;;;;;;;;;oBAIf,6BACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,gBAAgB;wCAC/B,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCACC,SAAS,IAAM,iBAAiB;wCAChC,WAAU;kDACX;;;;;;;;;;;;0CAMH,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,SAAS,IAAM,kBAAkB;oCACjC,WAAU;8CACX;;;;;;;;;;;;;;;;;kCAQP,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA4C;;;;;;kDAC1D,6LAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;0CAE/B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA8C;;;;;;kDAC5D,6LAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;0CAE/B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA4C;;;;;;kDAC1D,6LAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;;0BAMnC,6LAAC,8HAAA,CAAA,UAAK;gBACJ,QAAQ;gBACR,SAAS,IAAM,gBAAgB;0BAE/B,cAAA,6LAAC,oIAAA,CAAA,UAAW;oBACV,SAAS,IAAM,gBAAgB;oBAC/B,WAAW;;;;;;;;;;;0BAKf,6LAAC,8HAAA,CAAA,UAAK;gBACJ,QAAQ;gBACR,SAAS,IAAM,iBAAiB;0BAEhC,cAAA,6LAAC,qIAAA,CAAA,UAAY;oBACX,SAAS,IAAM,iBAAiB;oBAChC,WAAW;;;;;;;;;;;0BAKf,6LAAC,8HAAA,CAAA,UAAK;gBACJ,QAAQ;gBACR,SAAS,IAAM,kBAAkB;0BAEjC,cAAA,6LAAC,uIAAA,CAAA,UAAc;oBACb,SAAS,IAAM,kBAAkB;;;;;;;;;;;;;;;;;AAK3C;GA7IwB;;QACE,8JAAA,CAAA,aAAU;;;KADZ", "debugId": null}}]}