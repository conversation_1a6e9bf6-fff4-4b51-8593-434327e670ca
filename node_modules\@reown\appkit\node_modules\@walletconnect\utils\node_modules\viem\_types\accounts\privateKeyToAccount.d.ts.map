{"version": 3, "file": "privateKeyToAccount.d.ts", "sourceRoot": "", "sources": ["../../accounts/privateKeyToAccount.ts"], "names": [], "mappings": "AAEA,OAAO,KAAK,EAAE,GAAG,EAAE,MAAM,kBAAkB,CAAA;AAC3C,OAAO,EAAE,KAAK,cAAc,EAAS,MAAM,4BAA4B,CAAA;AAEvE,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,oBAAoB,CAAA;AACnD,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,0BAA0B,CAAA;AAC5D,OAAO,EAAE,KAAK,kBAAkB,EAAa,MAAM,gBAAgB,CAAA;AACnE,OAAO,KAAK,EAAE,iBAAiB,EAAE,MAAM,YAAY,CAAA;AACnD,OAAO,EACL,KAAK,2BAA2B,EAEjC,MAAM,+BAA+B,CAAA;AACtC,OAAO,EAAE,KAAK,aAAa,EAAQ,MAAM,iBAAiB,CAAA;AAE1D,OAAO,EAAE,KAAK,oBAAoB,EAAe,MAAM,wBAAwB,CAAA;AAC/E,OAAO,EACL,KAAK,wBAAwB,EAE9B,MAAM,4BAA4B,CAAA;AACnC,OAAO,EACL,KAAK,sBAAsB,EAE5B,MAAM,0BAA0B,CAAA;AAEjC,MAAM,MAAM,0BAA0B,GAAG;IACvC,YAAY,CAAC,EAAE,YAAY,GAAG,SAAS,CAAA;CACxC,CAAA;AAED,MAAM,MAAM,4BAA4B,GACpC,kBAAkB,GAClB,cAAc,GACd,2BAA2B,GAC3B,aAAa,GACb,oBAAoB,GACpB,wBAAwB,GACxB,sBAAsB,GACtB,SAAS,CAAA;AAEb;;;;GAIG;AACH,wBAAgB,mBAAmB,CACjC,UAAU,EAAE,GAAG,EACf,OAAO,GAAE,0BAA+B,GACvC,iBAAiB,CA8BnB"}