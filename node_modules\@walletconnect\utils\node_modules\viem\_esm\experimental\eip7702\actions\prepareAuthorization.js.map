{"version": 3, "file": "prepareAuthorization.js", "sourceRoot": "", "sources": ["../../../../experimental/eip7702/actions/prepareAuthorization.ts"], "names": [], "mappings": "AAEA,OAAO,EAEL,YAAY,GACb,MAAM,yCAAyC,CAAA;AAChD,OAAO,EAAE,UAAU,EAAE,MAAM,uCAAuC,CAAA;AAClE,OAAO,EAAE,mBAAmB,EAAE,MAAM,gDAAgD,CAAA;AAGpF,OAAO,EACL,oBAAoB,GAErB,MAAM,4BAA4B,CAAA;AAKnC,OAAO,EAAE,cAAc,EAAE,MAAM,0CAA0C,CAAA;AAEzE,OAAO,EAAE,SAAS,EAAE,MAAM,6BAA6B,CAAA;AA4BvD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAwCG;AACH,MAAM,CAAC,KAAK,UAAU,oBAAoB,CAIxC,MAAyC,EACzC,UAAmD;IAEnD,MAAM,EACJ,OAAO,EAAE,QAAQ,GAAG,MAAM,CAAC,OAAO,EAClC,eAAe,EACf,OAAO,EACP,KAAK,GACN,GAAG,UAAU,CAAA;IAEd,IAAI,CAAC,QAAQ;QACX,MAAM,IAAI,oBAAoB,CAAC;YAC7B,QAAQ,EAAE,4CAA4C;SACvD,CAAC,CAAA;IACJ,MAAM,OAAO,GAAG,YAAY,CAAC,QAAQ,CAAC,CAAA;IAEtC,MAAM,OAAO,GAAG,CAAC,GAAG,EAAE;QACpB,MAAM,QAAQ,GAAG,UAAU,CAAC,OAAO,IAAI,UAAU,CAAC,QAAQ,CAAA;QAC1D,IAAI,OAAO,QAAQ,KAAK,SAAS;YAAE,OAAO,QAAQ,CAAA;QAClD,IAAI,QAAQ;YAAE,OAAO,YAAY,CAAC,QAAQ,CAAC,CAAA;QAC3C,OAAO,SAAS,CAAA;IAClB,CAAC,CAAC,EAAE,CAAA;IAEJ,MAAM,aAAa,GAAG;QACpB,eAAe;QACf,OAAO;QACP,KAAK;KACW,CAAA;IAElB,IAAI,OAAO,aAAa,CAAC,OAAO,KAAK,WAAW;QAC9C,aAAa,CAAC,OAAO;YACnB,MAAM,CAAC,KAAK,EAAE,EAAE;gBAChB,CAAC,MAAM,SAAS,CAAC,MAAM,EAAE,UAAU,EAAE,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;IAE3D,IAAI,OAAO,aAAa,CAAC,KAAK,KAAK,WAAW,EAAE,CAAC;QAC/C,aAAa,CAAC,KAAK,GAAG,MAAM,SAAS,CACnC,MAAM,EACN,mBAAmB,EACnB,qBAAqB,CACtB,CAAC;YACA,OAAO,EAAE,OAAO,CAAC,OAAO;YACxB,QAAQ,EAAE,SAAS;SACpB,CAAC,CAAA;QACF,IACE,CAAC,OAAO;YACR,CAAC,OAAO,KAAK,IAAI,IAAI,cAAc,CAAC,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;YAEtE,aAAa,CAAC,KAAK,IAAI,CAAC,CAAA;IAC5B,CAAC;IAED,OAAO,aAAa,CAAA;AACtB,CAAC"}