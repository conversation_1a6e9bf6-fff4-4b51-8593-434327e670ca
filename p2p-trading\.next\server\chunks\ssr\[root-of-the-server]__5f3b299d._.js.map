{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/src/lib/wagmi.ts"], "sourcesContent": ["import { createConfig, http } from 'wagmi'\nimport { bsc } from 'wagmi/chains'\nimport { metaMask, injected } from 'wagmi/connectors'\n\n// BSC Mainnet configuration\nexport const config = createConfig({\n  chains: [bsc],\n  connectors: [\n    metaMask(),\n    injected(),\n  ],\n  transports: {\n    [bsc.id]: http('https://bsc-dataseed1.binance.org/'),\n  },\n})\n\n// USDT Contract on BSC Mainnet\nexport const USDT_CONTRACT = {\n  address: '0x55d398326f99059fF775485246999027B3197955' as const,\n  abi: [\n    // ERC20 standard functions we need\n    {\n      name: 'transfer',\n      type: 'function',\n      stateMutability: 'nonpayable',\n      inputs: [\n        { name: 'to', type: 'address' },\n        { name: 'amount', type: 'uint256' }\n      ],\n      outputs: [{ name: '', type: 'bool' }]\n    },\n    {\n      name: 'balanceOf',\n      type: 'function',\n      stateMutability: 'view',\n      inputs: [{ name: 'account', type: 'address' }],\n      outputs: [{ name: '', type: 'uint256' }]\n    },\n    {\n      name: 'allowance',\n      type: 'function',\n      stateMutability: 'view',\n      inputs: [\n        { name: 'owner', type: 'address' },\n        { name: 'spender', type: 'address' }\n      ],\n      outputs: [{ name: '', type: 'uint256' }]\n    },\n    {\n      name: 'approve',\n      type: 'function',\n      stateMutability: 'nonpayable',\n      inputs: [\n        { name: 'spender', type: 'address' },\n        { name: 'amount', type: 'uint256' }\n      ],\n      outputs: [{ name: '', type: 'bool' }]\n    }\n  ] as const\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;AACA;AAAA;;;;AAGO,MAAM,SAAS,CAAA,GAAA,8JAAA,CAAA,eAAY,AAAD,EAAE;IACjC,QAAQ;QAAC,4JAAA,CAAA,MAAG;KAAC;IACb,YAAY;QACV,CAAA,GAAA,gKAAA,CAAA,WAAQ,AAAD;QACP,CAAA,GAAA,wKAAA,CAAA,WAAQ,AAAD;KACR;IACD,YAAY;QACV,CAAC,4JAAA,CAAA,MAAG,CAAC,EAAE,CAAC,EAAE,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE;IACjB;AACF;AAGO,MAAM,gBAAgB;IAC3B,SAAS;IACT,KAAK;QACH,mCAAmC;QACnC;YACE,MAAM;YACN,MAAM;YACN,iBAAiB;YACjB,QAAQ;gBACN;oBAAE,MAAM;oBAAM,MAAM;gBAAU;gBAC9B;oBAAE,MAAM;oBAAU,MAAM;gBAAU;aACnC;YACD,SAAS;gBAAC;oBAAE,MAAM;oBAAI,MAAM;gBAAO;aAAE;QACvC;QACA;YACE,MAAM;YACN,MAAM;YACN,iBAAiB;YACjB,QAAQ;gBAAC;oBAAE,MAAM;oBAAW,MAAM;gBAAU;aAAE;YAC9C,SAAS;gBAAC;oBAAE,MAAM;oBAAI,MAAM;gBAAU;aAAE;QAC1C;QACA;YACE,MAAM;YACN,MAAM;YACN,iBAAiB;YACjB,QAAQ;gBACN;oBAAE,MAAM;oBAAS,MAAM;gBAAU;gBACjC;oBAAE,MAAM;oBAAW,MAAM;gBAAU;aACpC;YACD,SAAS;gBAAC;oBAAE,MAAM;oBAAI,MAAM;gBAAU;aAAE;QAC1C;QACA;YACE,MAAM;YACN,MAAM;YACN,iBAAiB;YACjB,QAAQ;gBACN;oBAAE,MAAM;oBAAW,MAAM;gBAAU;gBACnC;oBAAE,MAAM;oBAAU,MAAM;gBAAU;aACnC;YACD,SAAS;gBAAC;oBAAE,MAAM;oBAAI,MAAM;gBAAO;aAAE;QACvC;KACD;AACH", "debugId": null}}, {"offset": {"line": 139, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Rachit%20coding%20%F0%9F%98%8A/P2P/p2p-trading/src/components/WalletProvider.tsx"], "sourcesContent": ["'use client'\n\nimport { WagmiProvider } from 'wagmi'\nimport { QueryClient, QueryClientProvider } from '@tanstack/react-query'\nimport { config } from '@/lib/wagmi'\nimport { ReactNode } from 'react'\n\nconst queryClient = new QueryClient()\n\ninterface WalletProviderProps {\n  children: ReactNode\n}\n\nexport function WalletProvider({ children }: WalletProviderProps) {\n  return (\n    <WagmiProvider config={config}>\n      <QueryClientProvider client={queryClient}>\n        {children}\n      </QueryClientProvider>\n    </WagmiProvider>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAJA;;;;;AAOA,MAAM,cAAc,IAAI,6KAAA,CAAA,cAAW;AAM5B,SAAS,eAAe,EAAE,QAAQ,EAAuB;IAC9D,qBACE,8OAAC,+IAAA,CAAA,gBAAa;QAAC,QAAQ,mHAAA,CAAA,SAAM;kBAC3B,cAAA,8OAAC,sLAAA,CAAA,sBAAmB;YAAC,QAAQ;sBAC1B;;;;;;;;;;;AAIT", "debugId": null}}]}