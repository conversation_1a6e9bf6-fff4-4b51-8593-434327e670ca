# Supabase Database Setup Guide

## Step 1: Execute Database Schema

1. Go to your Supabase project dashboard
2. Navigate to **SQL Editor** in the left sidebar
3. Create a new query
4. Copy and paste the entire content from `supabase-schema.sql`
5. Click **Run** to execute the script

## Step 1.5: Fix RLS Policies (IMPORTANT!)

If you're getting "row-level security policy" errors:
1. Go to **SQL Editor** again
2. Copy and paste the content from `fix-rls-policies.sql`
3. Click **Run** to update the policies

This will create:
- ✅ `users` table with wallet, upi_id, bank_details, created_at
- ✅ `orders` table with type, amount, rate, status, user_wallet, created_at  
- ✅ `proofs` table with order_id, buyer_wallet, proof_url, created_at
- ✅ Storage bucket `payment_proofs` for image uploads
- ✅ Row Level Security (RLS) policies for secure access
- ✅ Database indexes for better performance

## Step 2: Verify Tables Created

In the Supabase dashboard:
1. Go to **Table Editor**
2. You should see three tables: `users`, `orders`, `proofs`
3. Go to **Storage** and verify `payment_proofs` bucket exists

## Step 3: Test Database Connection

The application includes helper functions in `src/lib/supabase.ts`:
- `dbHelpers.getUser(wallet)` - Get user by wallet address
- `dbHelpers.upsertUser(user)` - Create or update user
- `dbHelpers.getSellOrders()` - Get all active sell orders
- `dbHelpers.createOrder(order)` - Create new buy/sell order
- `dbHelpers.uploadProof(file, orderId, buyerWallet)` - Upload payment proof

## Database Schema Overview

### Users Table
```sql
wallet (VARCHAR) - Primary key, Ethereum wallet address
upi_id (VARCHAR) - UPI ID for payments
bank_details (TEXT) - Bank account details
created_at (TIMESTAMP) - Account creation time
```

### Orders Table
```sql
id (UUID) - Primary key
type (VARCHAR) - 'buy' or 'sell'
amount (DECIMAL) - USDT amount
rate (DECIMAL) - INR rate per USDT
status (VARCHAR) - 'pending', 'completed', 'cancelled'
user_wallet (VARCHAR) - Foreign key to users.wallet
created_at (TIMESTAMP) - Order creation time
```

### Proofs Table
```sql
id (UUID) - Primary key
order_id (UUID) - Foreign key to orders.id
buyer_wallet (VARCHAR) - Foreign key to users.wallet
proof_url (TEXT) - URL to uploaded payment proof image
created_at (TIMESTAMP) - Proof upload time
```

## Security Features

- **Row Level Security (RLS)** enabled on all tables
- Users can only access their own data
- Secure file upload policies for payment proofs
- Proper foreign key constraints and data validation

## BSC Mainnet Configuration

The application is configured for BSC Mainnet:
- Chain ID: 56
- USDT Contract: ******************************************
- RPC URL: https://bsc-dataseed1.binance.org/
