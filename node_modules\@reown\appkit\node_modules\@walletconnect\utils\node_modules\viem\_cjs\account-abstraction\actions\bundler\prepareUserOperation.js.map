{"version": 3, "file": "prepareUserOperation.js", "sourceRoot": "", "sources": ["../../../../account-abstraction/actions/bundler/prepareUserOperation.ts"], "names": [], "mappings": ";;AAoQA,oDAyYC;AA5oBD,6EAGgD;AAChD,yFAGsD;AACtD,yEAAiF;AAGjF,2DAAiE;AAYjE,oFAGiD;AACjD,6DAA4E;AAC5E,8DAAuD;AACvD,mEAA4D;AAgB5D,0EAGyC;AACzC,kFAG6C;AAC7C,+EAGsC;AAEtC,MAAM,iBAAiB,GAAG;IACxB,SAAS;IACT,MAAM;IACN,KAAK;IACL,WAAW;IACX,OAAO;IACP,WAAW;CACH,CAAA;AAkMH,KAAK,UAAU,oBAAoB,CAUxC,MAAqD,EACrD,WAKC;IAID,MAAM,UAAU,GAAG,WAA6C,CAAA;IAChE,MAAM,EACJ,OAAO,EAAE,QAAQ,GAAG,MAAM,CAAC,OAAO,EAClC,UAAU,EAAE,UAAU,GAAG,iBAAiB,EAC1C,aAAa,GACd,GAAG,UAAU,CAAA;IAMd,IAAI,CAAC,QAAQ;QAAE,MAAM,IAAI,iCAAoB,EAAE,CAAA;IAC/C,MAAM,OAAO,GAAG,IAAA,8BAAY,EAAC,QAAQ,CAAC,CAAA;IAMtC,MAAM,aAAa,GAAG,MAAkC,CAAA;IAMxD,MAAM,SAAS,GAAG,UAAU,CAAC,SAAS,IAAI,aAAa,EAAE,SAAS,CAAA;IAClE,MAAM,gBAAgB,GAAG,OAAO,SAAS,KAAK,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAA;IAC9E,MAAM,EAAE,oBAAoB,EAAE,gBAAgB,EAAE,GAAG,CAAC,GAAG,EAAE;QAEvD,IAAI,SAAS,KAAK,IAAI;YACpB,OAAO;gBACL,oBAAoB,EAAE,CAAC,UAAe,EAAE,EAAE,CACxC,IAAA,wBAAS,EACP,aAAa,EACb,8CAAqB,EACrB,sBAAsB,CACvB,CAAC,UAAU,CAAC;gBACf,gBAAgB,EAAE,CAAC,UAAe,EAAE,EAAE,CACpC,IAAA,wBAAS,EACP,aAAa,EACb,sCAAiB,EACjB,kBAAkB,CACnB,CAAC,UAAU,CAAC;aAChB,CAAA;QAGH,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE,CAAC;YAClC,MAAM,EAAE,oBAAoB,EAAE,gBAAgB,EAAE,GAAG,SAAS,CAAA;YAC5D,OAAO;gBACL,oBAAoB,EAAE,CAAC,gBAAgB,IAAI,oBAAoB;oBAC7D,CAAC,CAAC,oBAAoB;oBACtB,CAAC,CAAC,gBAAgB,CAAgC;gBACpD,gBAAgB,EACd,gBAAgB,IAAI,oBAAoB;oBACtC,CAAC,CAAC,gBAAgB;oBAClB,CAAC,CAAC,SAAS;aAChB,CAAA;QACH,CAAC;QAGD,OAAO;YACL,oBAAoB,EAAE,SAAS;YAC/B,gBAAgB,EAAE,SAAS;SAC5B,CAAA;IACH,CAAC,CAAC,EAAE,CAAA;IACJ,MAAM,gBAAgB,GAAG,UAAU,CAAC,gBAAgB;QAClD,CAAC,CAAC,UAAU,CAAC,gBAAgB;QAC7B,CAAC,CAAC,aAAa,EAAE,gBAAgB,CAAA;IAMnC,IAAI,OAAO,GAAG;QACZ,GAAG,UAAU;QACb,SAAS,EAAE,gBAAgB;QAC3B,MAAM,EAAE,OAAO,CAAC,OAAO;KACO,CAAA;IAMhC,MAAM,CAAC,QAAQ,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;QACzD,CAAC,KAAK,IAAI,EAAE;YACV,IAAI,UAAU,CAAC,KAAK;gBAClB,OAAO,OAAO,CAAC,WAAW,CACxB,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;oBAC7B,MAAM,IAAI,GAAG,KAAa,CAAA;oBAC1B,IAAI,IAAI,CAAC,GAAG;wBACV,OAAO;4BACL,IAAI,EAAE,IAAA,0CAAkB,EAAC,IAAI,CAAC;4BAC9B,EAAE,EAAE,IAAI,CAAC,EAAE;4BACX,KAAK,EAAE,IAAI,CAAC,KAAK;yBACV,CAAA;oBACX,OAAO,IAAY,CAAA;gBACrB,CAAC,CAAC,CACH,CAAA;YACH,OAAO,UAAU,CAAC,QAAQ,CAAA;QAC5B,CAAC,CAAC,EAAE;QACJ,CAAC,KAAK,IAAI,EAAE;YACV,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,SAAS,CAAC;gBAAE,OAAO,SAAS,CAAA;YACrD,IAAI,UAAU,CAAC,QAAQ;gBAAE,OAAO,EAAE,QAAQ,EAAE,UAAU,CAAC,QAAQ,EAAE,CAAA;YACjE,IAAI,UAAU,CAAC,OAAO,IAAI,UAAU,CAAC,WAAW,EAAE,CAAC;gBACjD,OAAO;oBACL,OAAO,EAAE,UAAU,CAAC,OAAO;oBAC3B,WAAW,EAAE,UAAU,CAAC,WAAW;iBACpC,CAAA;YACH,CAAC;YAED,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,GAAG,MAAM,OAAO,CAAC,cAAc,EAAE,CAAA;YAE/D,IAAI,OAAO,CAAC,UAAU,CAAC,OAAO,KAAK,KAAK;gBACtC,OAAO;oBACL,QAAQ,EACN,OAAO,IAAI,WAAW,CAAC,CAAC,CAAC,IAAA,kBAAM,EAAC,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;iBACtE,CAAA;YACH,OAAO;gBACL,OAAO;gBACP,WAAW;aACZ,CAAA;QACH,CAAC,CAAC,EAAE;QACJ,CAAC,KAAK,IAAI,EAAE;YACV,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC;gBAAE,OAAO,SAAS,CAAA;YAGlD,IACE,OAAO,UAAU,CAAC,YAAY,KAAK,QAAQ;gBAC3C,OAAO,UAAU,CAAC,oBAAoB,KAAK,QAAQ;gBAEnD,OAAO,OAAO,CAAA;YAGhB,IAAI,aAAa,EAAE,aAAa,EAAE,kBAAkB,EAAE,CAAC;gBACrD,MAAM,IAAI,GAAG,MAAM,aAAa,CAAC,aAAa,CAAC,kBAAkB,CAAC;oBAChE,OAAO;oBACP,aAAa;oBACb,aAAa,EAAE,OAAwB;iBACxC,CAAC,CAAA;gBACF,OAAO;oBACL,GAAG,OAAO;oBACV,GAAG,IAAI;iBACR,CAAA;YACH,CAAC;YAGD,IAAI,CAAC;gBACH,MAAM,OAAO,GAAG,aAAa,CAAC,MAAM,IAAI,MAAM,CAAA;gBAC9C,MAAM,IAAI,GAAG,MAAM,IAAA,wBAAS,EAC1B,OAAO,EACP,0CAAkB,EAClB,oBAAoB,CACrB,CAAC;oBACA,KAAK,EAAE,OAAO,CAAC,KAAK;oBACpB,IAAI,EAAE,SAAS;iBAChB,CAAC,CAAA;gBACF,OAAO;oBACL,YAAY,EACV,OAAO,UAAU,CAAC,YAAY,KAAK,QAAQ;wBACzC,CAAC,CAAC,UAAU,CAAC,YAAY;wBACzB,CAAC,CAAC,MAAM,CAEJ,IAAI,CAAC,GAAG,CACN,MAAM,CAAC,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,EAC9B,MAAM,CAAC,IAAA,wBAAS,EAAC,GAAG,CAAC,CAAC,CACvB,CACF;oBACP,oBAAoB,EAClB,OAAO,UAAU,CAAC,oBAAoB,KAAK,QAAQ;wBACjD,CAAC,CAAC,UAAU,CAAC,oBAAoB;wBACjC,CAAC,CAAC,MAAM,CAEJ,IAAI,CAAC,GAAG,CACN,MAAM,CAAC,EAAE,GAAG,IAAI,CAAC,oBAAoB,CAAC,EACtC,MAAM,CAAC,IAAA,wBAAS,EAAC,GAAG,CAAC,CAAC,CACvB,CACF;iBACR,CAAA;YACH,CAAC;YAAC,MAAM,CAAC;gBACP,OAAO,SAAS,CAAA;YAClB,CAAC;QACH,CAAC,CAAC,EAAE;QACJ,CAAC,KAAK,IAAI,EAAE;YACV,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC;gBAAE,OAAO,SAAS,CAAA;YACnD,IAAI,OAAO,UAAU,CAAC,KAAK,KAAK,QAAQ;gBAAE,OAAO,UAAU,CAAC,KAAK,CAAA;YACjE,OAAO,OAAO,CAAC,QAAQ,EAAE,CAAA;QAC3B,CAAC,CAAC,EAAE;KACL,CAAC,CAAA;IAMF,IAAI,OAAO,QAAQ,KAAK,WAAW;QAAE,OAAO,CAAC,QAAQ,GAAG,QAAQ,CAAA;IAChE,IAAI,OAAO,OAAO,KAAK,WAAW;QAChC,OAAO,GAAG,EAAE,GAAG,OAAO,EAAE,GAAI,OAAe,EAAE,CAAA;IAC/C,IAAI,OAAO,IAAI,KAAK,WAAW;QAAE,OAAO,GAAG,EAAE,GAAG,OAAO,EAAE,GAAI,IAAY,EAAE,CAAA;IAC3E,IAAI,OAAO,KAAK,KAAK,WAAW;QAAE,OAAO,CAAC,KAAK,GAAG,KAAK,CAAA;IAMvD,IAAI,UAAU,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;QACrC,IAAI,OAAO,UAAU,CAAC,SAAS,KAAK,WAAW;YAC7C,OAAO,CAAC,SAAS,GAAG,UAAU,CAAC,SAAS,CAAA;;YAExC,OAAO,CAAC,SAAS,GAAG,MAAM,OAAO,CAAC,gBAAgB,CAChD,OAAwB,CACzB,CAAA;IACL,CAAC;IAOD,IAAI,OAAO,CAAC,UAAU,CAAC,OAAO,KAAK,KAAK,IAAI,CAAC,OAAO,CAAC,QAAQ;QAC3D,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAA;IAMzB,IAAI,OAA2B,CAAA;IAC/B,KAAK,UAAU,UAAU;QACvB,IAAI,OAAO;YAAE,OAAO,OAAO,CAAA;QAC3B,IAAI,MAAM,CAAC,KAAK;YAAE,OAAO,MAAM,CAAC,KAAK,CAAC,EAAE,CAAA;QACxC,MAAM,QAAQ,GAAG,MAAM,IAAA,wBAAS,EAAC,MAAM,EAAE,0BAAW,EAAE,YAAY,CAAC,CAAC,EAAE,CAAC,CAAA;QACvE,OAAO,GAAG,QAAQ,CAAA;QAClB,OAAO,OAAO,CAAA;IAChB,CAAC;IAID,IAAI,oBAAoB,GAAG,KAAK,CAAA;IAChC,IACE,UAAU,CAAC,QAAQ,CAAC,WAAW,CAAC;QAChC,oBAAoB;QACpB,CAAC,gBAAgB;QACjB,CAAC,UAAU,CAAC,gBAAgB,EAC5B,CAAC;QACD,MAAM,EACJ,OAAO,GAAG,KAAK,EACf,OAAO,EACP,GAAG,aAAa,EACjB,GAAG,MAAM,oBAAoB,CAAC;YAC7B,OAAO,EAAE,MAAM,UAAU,EAAE;YAC3B,iBAAiB,EAAE,OAAO,CAAC,UAAU,CAAC,OAAO;YAC7C,OAAO,EAAE,gBAAgB;YACzB,GAAI,OAAyB;SAC9B,CAAC,CAAA;QACF,oBAAoB,GAAG,OAAO,CAAA;QAC9B,OAAO,GAAG;YACR,GAAG,OAAO;YACV,GAAG,aAAa;SACc,CAAA;IAClC,CAAC;IAOD,IAAI,OAAO,CAAC,UAAU,CAAC,OAAO,KAAK,KAAK,IAAI,CAAC,OAAO,CAAC,gBAAgB;QACnE,OAAO,CAAC,gBAAgB,GAAG,IAAI,CAAA;IAMjC,IAAI,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;QAG/B,IAAI,OAAO,CAAC,aAAa,EAAE,WAAW,EAAE,CAAC;YACvC,MAAM,GAAG,GAAG,MAAM,OAAO,CAAC,aAAa,CAAC,WAAW,CACjD,OAAwB,CACzB,CAAA;YACD,OAAO,GAAG;gBACR,GAAG,OAAO;gBACV,GAAG,GAAG;aACwB,CAAA;QAClC,CAAC;QAID,IACE,OAAO,OAAO,CAAC,YAAY,KAAK,WAAW;YAC3C,OAAO,OAAO,CAAC,kBAAkB,KAAK,WAAW;YACjD,OAAO,OAAO,CAAC,oBAAoB,KAAK,WAAW;YACnD,CAAC,OAAO,CAAC,SAAS;gBAChB,OAAO,OAAO,CAAC,uBAAuB,KAAK,WAAW,CAAC;YACzD,CAAC,OAAO,CAAC,SAAS;gBAChB,OAAO,OAAO,CAAC,6BAA6B,KAAK,WAAW,CAAC,EAC/D,CAAC;YACD,MAAM,GAAG,GAAG,MAAM,IAAA,wBAAS,EACzB,aAAa,EACb,sDAAwB,EACxB,0BAA0B,CAC3B,CAAC;gBACA,OAAO;gBAGP,YAAY,EAAE,EAAE;gBAChB,kBAAkB,EAAE,EAAE;gBACtB,oBAAoB,EAAE,EAAE;gBACxB,aAAa;gBACb,GAAG,CAAC,OAAO,CAAC,SAAS;oBACnB,CAAC,CAAC;wBACE,uBAAuB,EAAE,EAAE;wBAC3B,6BAA6B,EAAE,EAAE;qBAClC;oBACH,CAAC,CAAC,EAAE,CAAC;gBACP,GAAG,OAAO;aAC2B,CAAC,CAAA;YACxC,OAAO,GAAG;gBACR,GAAG,OAAO;gBACV,YAAY,EAAE,OAAO,CAAC,YAAY,IAAI,GAAG,CAAC,YAAY;gBACtD,kBAAkB,EAChB,OAAO,CAAC,kBAAkB,IAAI,GAAG,CAAC,kBAAkB;gBACtD,oBAAoB,EAClB,OAAO,CAAC,oBAAoB,IAAI,GAAG,CAAC,oBAAoB;gBAC1D,uBAAuB,EACrB,OAAO,CAAC,uBAAuB,IAAI,GAAG,CAAC,uBAAuB;gBAChE,6BAA6B,EAC3B,OAAO,CAAC,6BAA6B;oBACrC,GAAG,CAAC,6BAA6B;aACL,CAAA;QAClC,CAAC;IACH,CAAC;IAQD,IACE,UAAU,CAAC,QAAQ,CAAC,WAAW,CAAC;QAChC,gBAAgB;QAChB,CAAC,gBAAgB;QACjB,CAAC,UAAU,CAAC,gBAAgB;QAC5B,CAAC,oBAAoB,EACrB,CAAC;QAED,MAAM,SAAS,GAAG,MAAM,gBAAgB,CAAC;YACvC,OAAO,EAAE,MAAM,UAAU,EAAE;YAC3B,iBAAiB,EAAE,OAAO,CAAC,UAAU,CAAC,OAAO;YAC7C,OAAO,EAAE,gBAAgB;YACzB,GAAI,OAAyB;SAC9B,CAAC,CAAA;QACF,OAAO,GAAG;YACR,GAAG,OAAO;YACV,GAAG,SAAS;SACkB,CAAA;IAClC,CAAC;IAMD,OAAO,OAAO,CAAC,KAAK,CAAA;IACpB,OAAO,OAAO,CAAC,UAAU,CAAA;IACzB,OAAO,OAAO,CAAC,gBAAgB,CAAA;IAC/B,IAAI,OAAO,OAAO,CAAC,SAAS,KAAK,QAAQ;QAAE,OAAO,OAAO,CAAC,SAAS,CAAA;IAInE,OAAO,OAKN,CAAA;AACH,CAAC"}