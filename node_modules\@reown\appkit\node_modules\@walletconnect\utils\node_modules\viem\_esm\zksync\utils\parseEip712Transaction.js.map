{"version": 3, "file": "parseEip712Transaction.js", "sourceRoot": "", "sources": ["../../../zksync/utils/parseEip712Transaction.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAA;AAEhD,OAAO,EACL,OAAO,EACP,OAAO,EACP,WAAW,EACX,WAAW,GACZ,MAAM,sBAAsB,CAAA;AAG7B,MAAM,UAAU,sBAAsB,CACpC,WAAgB;IAEhB,MAAM,OAAO,GAAG,OAAO,CAAC,WAAW,EAAE,OAAO,CAAC,CAAA;IAC7C,IAAI,OAAO,CAAC,CAAC,CAAC,KAAK,GAAG;QAAE,MAAM,IAAI,SAAS,CAAC,iCAAiC,CAAC,CAAA;IAE9E,SAAS,QAAQ,CAAC,KAAU;QAC1B,IAAI,CAAC,KAAK,IAAI,KAAK,KAAK,IAAI;YAAE,OAAO,KAAK,CAAA;QAC1C,OAAO,KAAK,CAAA;IACd,CAAC;IAED,SAAS,mBAAmB,CAAC,GAAU;QACrC,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,SAAS,CAAA;QACtC,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC;YAClB,MAAM,IAAI,SAAS,CACjB,qEAAqE,GAAG,CAAC,MAAM,GAAG,CACnF,CAAA;QAEH,OAAO;YACL,SAAS,EAAE,GAAG,CAAC,CAAC,CAAC;YACjB,cAAc,EAAE,GAAG,CAAC,CAAC,CAAC;SACvB,CAAA;IACH,CAAC;IAED,MAAM,GAAG,GAAG,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAU,CAAA;IAC9C,MAAM,eAAe,GAAG,mBAAmB,CAAC,GAAG,CAAC,EAAE,CAAqB,CAAC,CAAA;IACxE,OAAO;QACL,IAAI,EAAE,QAAQ;QACd,KAAK,EAAE,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACpC,oBAAoB,EAAE,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACnD,YAAY,EAAE,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3C,GAAG,EAAE,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAClC,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC;QACV,KAAK,EAAE,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACpC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC;QACZ,CAAC,EAAE,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;QACT,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;QACT,OAAO,EAAE,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QACvC,IAAI,EAAE,GAAG,CAAC,EAAE,CAAC;QACb,aAAa,EAAE,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QAC7C,WAAW,EAAE,GAAG,CAAC,EAAE,CAAqB;QACxC,eAAe,EAAE,GAAG,CAAC,EAAE,CAAC;QACxB,SAAS,EAAE,eAAe,EAAE,SAAS;QACrC,cAAc,EAAE,eAAe,EAAE,cAAc;KAChD,CAAA;AACH,CAAC"}