{"version": 3, "file": "parseEip712Transaction.js", "sourceRoot": "", "sources": ["../../../zksync/utils/parseEip712Transaction.ts"], "names": [], "mappings": ";;AAUA,wDA8CC;AAxDD,kDAAgD;AAEhD,mDAK6B;AAG7B,SAAgB,sBAAsB,CACpC,WAAgB;IAEhB,MAAM,OAAO,GAAG,IAAA,kBAAO,EAAC,WAAW,EAAE,OAAO,CAAC,CAAA;IAC7C,IAAI,OAAO,CAAC,CAAC,CAAC,KAAK,GAAG;QAAE,MAAM,IAAI,mBAAS,CAAC,iCAAiC,CAAC,CAAA;IAE9E,SAAS,QAAQ,CAAC,KAAU;QAC1B,IAAI,CAAC,KAAK,IAAI,KAAK,KAAK,IAAI;YAAE,OAAO,KAAK,CAAA;QAC1C,OAAO,KAAK,CAAA;IACd,CAAC;IAED,SAAS,mBAAmB,CAAC,GAAU;QACrC,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,SAAS,CAAA;QACtC,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC;YAClB,MAAM,IAAI,mBAAS,CACjB,qEAAqE,GAAG,CAAC,MAAM,GAAG,CACnF,CAAA;QAEH,OAAO;YACL,SAAS,EAAE,GAAG,CAAC,CAAC,CAAC;YACjB,cAAc,EAAE,GAAG,CAAC,CAAC,CAAC;SACvB,CAAA;IACH,CAAC;IAED,MAAM,GAAG,GAAG,IAAA,kBAAO,EAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAU,CAAA;IAC9C,MAAM,eAAe,GAAG,mBAAmB,CAAC,GAAG,CAAC,EAAE,CAAqB,CAAC,CAAA;IACxE,OAAO;QACL,IAAI,EAAE,QAAQ;QACd,KAAK,EAAE,IAAA,sBAAW,EAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACpC,oBAAoB,EAAE,IAAA,sBAAW,EAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACnD,YAAY,EAAE,IAAA,sBAAW,EAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3C,GAAG,EAAE,IAAA,sBAAW,EAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAClC,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC;QACV,KAAK,EAAE,IAAA,sBAAW,EAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACpC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC;QACZ,CAAC,EAAE,IAAA,sBAAW,EAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;QACT,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;QACT,OAAO,EAAE,IAAA,sBAAW,EAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QACvC,IAAI,EAAE,GAAG,CAAC,EAAE,CAAC;QACb,aAAa,EAAE,IAAA,sBAAW,EAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QAC7C,WAAW,EAAE,GAAG,CAAC,EAAE,CAAqB;QACxC,eAAe,EAAE,GAAG,CAAC,EAAE,CAAC;QACxB,SAAS,EAAE,eAAe,EAAE,SAAS;QACrC,cAAc,EAAE,eAAe,EAAE,cAAc;KAChD,CAAA;AACH,CAAC"}