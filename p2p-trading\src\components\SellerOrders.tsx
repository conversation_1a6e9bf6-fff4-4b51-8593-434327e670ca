'use client'

import { useState, useEffect } from 'react'
import { useAccount } from 'wagmi'
import { dbHelpers } from '@/lib/supabase'

interface Order {
  id: string
  type: 'buy' | 'sell'
  amount: number
  rate: number
  status: 'pending' | 'completed' | 'cancelled'
  user_wallet: string
  created_at: string
}

interface Proof {
  id: string
  order_id: string
  buyer_wallet: string
  proof_url: string
  created_at: string
  confirmed_by_seller?: boolean
}

interface SellerOrdersProps {
  onClose: () => void
}

export default function SellerOrders({ onClose }: SellerOrdersProps) {
  const { address } = useAccount()
  const [orders, setOrders] = useState<Order[]>([])
  const [proofs, setProofs] = useState<Proof[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [confirming, setConfirming] = useState<string | null>(null)

  useEffect(() => {
    if (address) {
      loadSellerData()
    }
  }, [address])

  const loadSellerData = async () => {
    if (!address) return

    try {
      setLoading(true)
      setError('')

      // Get seller's orders
      const { data: ordersData, error: ordersError } = await dbHelpers.getSellerOrders(address)
      if (ordersError) {
        setError('Failed to load orders: ' + ordersError.message)
        return
      }

      // Get proofs for seller's orders
      const { data: proofsData, error: proofsError } = await dbHelpers.getProofsForSeller(address)
      if (proofsError) {
        setError('Failed to load proofs: ' + proofsError.message)
        return
      }

      setOrders(ordersData || [])
      setProofs(proofsData || [])

    } catch (err) {
      setError('Failed to load seller data')
      console.error('Load seller data error:', err)
    } finally {
      setLoading(false)
    }
  }

  const confirmPaymentReceived = async (proofId: string) => {
    console.log('Confirming payment for proof:', proofId)
    setConfirming(proofId)
    setError('')

    try {
      // Mark proof as confirmed by seller
      const { data, error } = await dbHelpers.confirmPaymentBySeller(proofId)

      console.log('Confirmation result:', { data, error })

      if (error) {
        setError('Failed to confirm payment: ' + error.message)
        return
      }

      if (!data) {
        setError('Failed to confirm payment: No data returned')
        return
      }

      console.log('Payment confirmed successfully')

      // Reload data
      await loadSellerData()

    } catch (err) {
      setError('Failed to confirm payment: ' + (err instanceof Error ? err.message : 'Unknown error'))
      console.error('Confirm payment error:', err)
    } finally {
      setConfirming(null)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString()
  }

  const formatWallet = (wallet: string) => {
    return `${wallet.slice(0, 6)}...${wallet.slice(-4)}`
  }

  if (loading) {
    return (
      <div className="max-w-4xl mx-auto text-center">
        <div className="animate-spin w-8 h-8 border-4 border-blue-400 border-t-transparent rounded-full mx-auto mb-4"></div>
        <p>Loading your orders...</p>
      </div>
    )
  }

  const sellOrders = orders.filter(order => order.type === 'sell')
  const ordersWithProofs = sellOrders.map(order => ({
    ...order,
    proofs: proofs.filter(proof => proof.order_id === order.id)
  }))

  return (
    <div className="max-w-4xl mx-auto">
      <div className="mb-6 p-4 bg-blue-900/20 border border-blue-500/30 rounded-lg">
        <div className="flex justify-between items-center">
          <h2 className="text-2xl font-bold text-blue-400">Your Sell Orders</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white text-2xl"
          >
            ×
          </button>
        </div>
        <p className="text-gray-300 text-sm mt-2">
          Manage your sell orders and confirm payments
        </p>
      </div>

      {error && (
        <div className="mb-4 p-4 bg-red-900/20 border border-red-500/30 rounded-lg text-red-400">
          {error}
        </div>
      )}

      <div className="space-y-6">
        {ordersWithProofs.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            You have no sell orders yet
          </div>
        ) : (
          ordersWithProofs.map((order) => (
            <div key={order.id} className="p-6 bg-gray-800 border border-gray-700 rounded-lg">
              {/* Order Header */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                <div>
                  <p className="text-sm text-gray-400">Amount</p>
                  <p className="font-semibold text-lg">{order.amount} USDT</p>
                </div>
                <div>
                  <p className="text-sm text-gray-400">Rate</p>
                  <p className="font-semibold">₹{order.rate}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-400">Total</p>
                  <p className="font-semibold text-green-400">₹{(order.amount * order.rate).toFixed(2)}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-400">Status</p>
                  <span className={`px-2 py-1 rounded text-xs font-semibold ${
                    order.status === 'pending' ? 'bg-yellow-900/50 text-yellow-400' :
                    order.status === 'completed' ? 'bg-green-900/50 text-green-400' :
                    'bg-red-900/50 text-red-400'
                  }`}>
                    {order.status}
                  </span>
                </div>
              </div>

              {/* Payment Proofs */}
              {order.proofs.length > 0 && (
                <div className="mt-4 pt-4 border-t border-gray-700">
                  <h3 className="text-lg font-semibold mb-4 text-purple-400">Payment Proofs</h3>
                  <div className="space-y-4">
                    {order.proofs.map((proof) => (
                      <div key={proof.id} className="p-4 bg-gray-700 rounded-lg">
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                          {/* Proof Details */}
                          <div>
                            <div className="space-y-2 text-sm">
                              <div className="flex justify-between">
                                <span className="text-gray-400">Buyer:</span>
                                <span className="font-mono">{formatWallet(proof.buyer_wallet)}</span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-gray-400">Submitted:</span>
                                <span>{formatDate(proof.created_at)}</span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-gray-400">Status:</span>
                                <span className={proof.confirmed_by_seller ? 'text-green-400' : 'text-yellow-400'}>
                                  {proof.confirmed_by_seller ? 'Confirmed by you' : 'Awaiting confirmation'}
                                </span>
                              </div>
                            </div>
                          </div>

                          {/* Proof Image */}
                          <div>
                            <img 
                              src={proof.proof_url} 
                              alt="Payment Proof"
                              className="w-full max-w-xs rounded-lg border border-gray-600"
                              onError={(e) => {
                                e.currentTarget.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjMzMzIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlIG5vdCBmb3VuZDwvdGV4dD48L3N2Zz4='
                              }}
                            />
                          </div>
                        </div>

                        {/* Confirmation Button */}
                        {!proof.confirmed_by_seller && (
                          <div className="mt-4 pt-4 border-t border-gray-600">
                            <div className="space-y-2">
                              <button
                                onClick={() => confirmPaymentReceived(proof.id)}
                                disabled={confirming === proof.id}
                                className="btn-primary bg-green-600 hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
                              >
                                {confirming === proof.id ? 'Confirming...' : '✅ Confirm Payment Received'}
                              </button>

                              {/* Debug info */}
                              <div className="text-xs text-gray-500">
                                <p>Proof ID: {proof.id}</p>
                                <p>Order ID: {proof.order_id}</p>
                                <p>Click this button after you have received the INR payment from the buyer</p>
                              </div>
                            </div>
                          </div>
                        )}

                        {proof.confirmed_by_seller && (
                          <div className="mt-4 pt-4 border-t border-gray-600">
                            <div className="flex items-center gap-2 text-green-400">
                              <span>✅</span>
                              <span className="text-sm">Payment confirmed by you</span>
                            </div>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {order.proofs.length === 0 && order.status === 'pending' && (
                <div className="mt-4 pt-4 border-t border-gray-700 text-center text-gray-500">
                  No payment proofs submitted yet
                </div>
              )}
            </div>
          ))
        )}
      </div>

      <div className="mt-6 text-center space-x-4">
        <button
          onClick={loadSellerData}
          className="btn-primary px-6 py-2"
        >
          Refresh Orders
        </button>

        <button
          onClick={async () => {
            console.log('Current proofs data:', proofs)
            const { data } = await dbHelpers.debugListAllProofs()
            alert(`Found ${data?.length || 0} proofs in database. Check console for details.`)
          }}
          className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg text-sm"
        >
          Debug Proofs
        </button>
      </div>
    </div>
  )
}
