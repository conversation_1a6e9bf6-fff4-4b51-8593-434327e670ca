module.exports = {

"[project]/node_modules/@metamask/sdk/dist/browser/es/metamask-sdk.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_tr46_3d1b2cf9._.js",
  "server/chunks/ssr/52fcc_ws_e40adc4b._.js",
  "server/chunks/ssr/node_modules_@metamask_sdk_dist_browser_es_metamask-sdk_e6d9b64d.js",
  "server/chunks/ssr/node_modules_731e4b31._.js",
  "server/chunks/ssr/[root-of-the-server]__913a531c._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@metamask/sdk/dist/browser/es/metamask-sdk.js [app-ssr] (ecmascript)");
    });
});
}}),

};