{"version": 3, "file": "modeTestnet.js", "sourceRoot": "", "sources": ["../../../chains/definitions/modeTestnet.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,WAAW,EAAE,MAAM,+BAA+B,CAAA;AAC3D,OAAO,EAAE,WAAW,EAAE,MAAM,kCAAkC,CAAA;AAE9D,MAAM,QAAQ,GAAG,UAAU,CAAA,CAAC,UAAU;AAEtC,MAAM,CAAC,MAAM,WAAW,GAAG,aAAa,CAAC,WAAW,CAAC;IACnD,GAAG,WAAW;IACd,EAAE,EAAE,GAAG;IACP,IAAI,EAAE,cAAc;IACpB,cAAc,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE,EAAE;IAC9D,OAAO,EAAE;QACP,OAAO,EAAE;YACP,IAAI,EAAE,CAAC,8BAA8B,CAAC;SACvC;KACF;IACD,cAAc,EAAE;QACd,OAAO,EAAE;YACP,IAAI,EAAE,YAAY;YAClB,GAAG,EAAE,uCAAuC;YAC5C,MAAM,EAAE,2CAA2C;SACpD;KACF;IACD,SAAS,EAAE;QACT,GAAG,WAAW,CAAC,SAAS;QACxB,cAAc,EAAE;YACd,CAAC,QAAQ,CAAC,EAAE;gBACV,OAAO,EAAE,4CAA4C;gBACrD,YAAY,EAAE,OAAO;aACtB;SACF;QACD,MAAM,EAAE;YACN,CAAC,QAAQ,CAAC,EAAE;gBACV,OAAO,EAAE,4CAA4C;gBACrD,YAAY,EAAE,OAAO;aACtB;SACF;QACD,gBAAgB,EAAE;YAChB,CAAC,QAAQ,CAAC,EAAE;gBACV,OAAO,EAAE,4CAA4C;gBACrD,YAAY,EAAE,OAAO;aACtB;SACF;QACD,UAAU,EAAE;YACV,OAAO,EAAE,4CAA4C;YACrD,YAAY,EAAE,OAAO;SACtB;KACF;IACD,OAAO,EAAE,IAAI;IACb,QAAQ;CACT,CAAC,CAAA"}